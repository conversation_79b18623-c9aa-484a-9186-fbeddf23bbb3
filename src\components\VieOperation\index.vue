<template>
  <div>
    <el-button
      v-if="showImg"
      size="mini"
      type="text"
      :disabled="!isExistUrl()"
      @click="showCopyDiag()"
    > 图片链接
    </el-button>
    <!-- <el-button
      type="text"
      size="mini"
      @click="openExtraInfo()"
    > 额外信息
    </el-button> -->
    <el-dialog :visible.sync="open" width="500px">
      <div>
        <ul>
          <li v-for="(item, index) in imgUrls" :key="index">
            <el-link
              slot="reference"
              type="primary"
              :href="formatUrl(item)"
              :disabled="item===''"
              download="a"
              target="_blank"
            >
              {{ imgNames[index] }}
            </el-link>
          </li>
        </ul>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="copyOpen" width="300px">
      <div>
        <div :style="styleObject">
          <ul>
            <li v-for="(item, index) in imgUrls" :key="index">
              <el-button
                type="text"
                size="mini"
                :disabled="item===''"
                @click="copyLink(item)"
              >复制图片:{{ imgNames[index] }}</el-button>
            </li>
          </ul>
        </div>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="extraOpen" height="calc(100% - 61px)">
      <el-table :data="getExtraInfoData()" border height="calc(100% - 61px)">
        <el-table-column fixed prop="name" label="名称" width="100px" />
        <el-table-column prop="value" label="信息" max-width="700px">
          <template slot-scope="scope">
            <div v-if="dataObject(scope.row.value)">
              <pre style="overflow:auto;"><code>{{ JSON.stringify(scope.row.value, null, 4).replace(/\"/g, "") }}</code></pre>
            </div>
            <div v-else>{{ scope.row.value }}</div>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!-- 添加或修改对话框 -->
    <el-dialog :visible.sync="approvalOpen" :title="title" width="500px">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="申请目的" prop="purposes">
          <div>
            <el-input v-model="form.purposes" placeholder="默认申请目的为：获取[vie]下载链接" type="textarea" rows="1" />
          </div>
        </el-form-item>
        <el-form-item label="审批人" prop="approver_id">
          <div>
            <el-radio v-for="option in approver_list" :key="option.value" v-model="form.approver_id" :label="option.value">
              {{ option.label }}
            </el-radio>
          </div>
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="填写需要补充的信息" type="textarea" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-image-viewer
      v-if="showViewer"
      style="width: 98%; height: 98%"
      :on-close="closeViewer"
      :url-list="viewPhotoList"
    />
  </div>
</template>
<script>
import { string } from 'clipboard'
import elImageViewer from 'element-ui/packages/image/src/image-viewer.vue'
import { postCvApproval } from '@/api/layout'

export default {
  name: 'VieOperation',
  components: {
    elImageViewer
  },
  props: {
    imgUrls: {
      type: Array,
      default: () => []
    },
    showImg: {
      type: Boolean,
      default: false
    },
    imgNames: {
      type: Array,
      default: () => []
    },
    dialog: {
      type: Boolean,
      default: false
    },
    docid: {
      type: string,
      default: ''
    },
    extraInfo: {
      type: Object,
      default: () => {}
    },
    jobid: {
      type: string,
      default: ''
    },
    dowdate: {
      type: string,
      default: ''
    }
  },
  data() {
    return {
      extraOpen: false,
      open: false,
      copyOpen: false,
      showViewer: false,
      viewPhotoList: [],
      styleObject: {
        overflow: 'auto',
        height: this.imgUrls.length > 20 ? '500px' : '200px'
      },
      urlButtonDisabled: false,
      extraInfoData: {},
      // 是否开启审批表单
      approvalOpen: false,
      // 标题
      title: '创建审批-获取图片链接',
      // 表单参数
      form: {
        purposes: '获取[vie]下载链接',
        doc_id: ''
      },
      // 表单校验
      rules: {
        approver_id: [{ required: true, message: '选择一个审批人', trigger: 'blur' }]
      },
      approver_list: []
    }
  },
  created() {
    this.getDicts('approver_cvdata_list').then((response) => {
      console.log(response.data)
      this.approver_list = response.data
    })
  },
  methods: {
    // 获取表单参数
    getFormParams() {
      this.form.doc_id = this.jobid
      this.form.function = 'vie'
      this.form.file_type = 'img'
      this.form.approver_id = parseInt(this.form.approver_id)
      if (this.dowdate !== '') {
        const formatted = `${this.dowdate.substring(0, 4)}-${this.dowdate.substring(4, 6)}-${this.dowdate.substring(6, 8)}`
        this.form.starttime = formatted
      }
    },
    /** 提交按钮 */
    submitForm: function() {
      this.getFormParams()
      this.$refs['form'].validate((valid) => {
        if (valid) {
          postCvApproval(this.form).then((response) => {
            this.reset()
            if (response.code === 200) {
              this.msgSuccess(response.msg)
              this.approvalOpen = false
              // this.getList()
            } else {
              this.msgError(response.msg)
            }
          })
        } else {
          this.$message({
            showClose: true,
            message: '参数设置有误，审批表单提交失败! ',
            center: true,
            type: 'info'
          })
        }
      })
    },
    // 取消按钮
    cancel() {
      this.approvalOpen = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        remark: '',
        purposes: '获取[vie]下载链接',
        approver_id: undefined,
        starttime: '',
        file_type: '',
        function: '',
        doc_id: undefined
      }
      this.resetForm('form')
    },
    // 获取图片链接
    showCopyDiag() {
      // 开启审批表单
      this.approvalOpen = true
    },
    isExistUrl() {
      for (var i of this.imgUrls) {
        if (i.length > 0) {
          return true
        }
      }
      return false
    },
    copyLink(url) {
      var inputNode = document.getElementById('vie_input')
      if (inputNode != null) {
        inputNode.remove()
      }
      inputNode = document.createElement('input')
      inputNode.value = url
      inputNode.id = 'vie_input'
      document.body.appendChild(inputNode)
      inputNode.select()
      document.execCommand('Copy')
      inputNode.className = 'oInput'
      inputNode.style.display = 'none'
      this.$message.success('复制成功')
    },
    formatUrl(url) {
      var s = url === undefined ? '' : url
      return s.replaceAll('"', '').replace('-internal', '')
    },
    handleCancel() {
      this.$emit('cancel')
    },
    handleConfirm() {
      this.$emit('confirm')
    },
    // 图片弹出框
    handlePopImgs(row) {
      this.open = true
    },
    openExtraInfo() {
      this.extraOpen = true
    },
    closeViewer() {
      this.showViewer = false
    },
    onPreview() {
      const urls = this.imgUrls
      const temp = []
      urls.forEach((e) => {
        if (e !== undefined && e !== '') {
          temp.push(this.formatUrl(e))
        }
      })
      this.viewPhotoList = temp
      this.showViewer = true
    }, dataObject(info) {
      try {
        return Object.prototype.toString.call(info) === '[object Object]'
      } catch (e) {
        return false
      }
    }, getExtraInfoData() {
      return Object.entries(this.extraInfo).map(([name, value]) => {
        return { name, value }
      })
    }
  }
}
</script>
