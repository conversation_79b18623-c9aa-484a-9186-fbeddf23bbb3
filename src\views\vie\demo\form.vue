<template>
  <div>
    <CVHint>智能结构化已开通体验支持！</CVHint>
    <div class="container">
      <el-row type="flex" justify="center" :gutter="20">
        <el-col :span="8" class="grid-content">
          <div class="upload-container">
            <div class="params-container" style="width: 100% !important"></div>
            <div class="params-container">
              <div class="params">
                <span class="param-label">引擎版本 :</span>
                <el-select v-model="startPage.version" class="param-input" placeholder="引擎版本" size="small"
                  @change="selectVersion">
                  <el-option v-for="dict in versions" :key="dict.value" :label="dict.label" :value="dict.value"
                    @click.native="handleSelect(dict)"></el-option>
                </el-select>
              </div>
            </div>
            <div class="params-container" style="width: 100% !important"></div>
            <div class="params-container" style="width: 100% !important">
              <div class="params">
                <span style="color: red">{{ remark }}</span>
              </div>
            </div>
            <div class="params-container">
              <div v-show="fileName !== ''" class="params">
                <span class="param-label">{{ fileName }} :</span>
                <span class="param-input">{{ fileSize }}</span>
              </div>
              <div v-show="statistic.status !== -1" class="params">
                <span class="param-label">处理用时 :</span>
                <span class="param-input">{{ formattedDuration }}</span>
              </div>
              <div v-show="statistic.status !== -1" class="params">
                <span v-show="statistic.status === 1" class="param-label" style="color: #00ff33">提取成功!</span>
                <span v-show="statistic.status === 0" class="param-label" style="color: #ff0000">提取失败!</span>
              </div>
            </div>
            <el-upload v-loading="loading" element-loading-text="文件处理中" class="uploader" list-type="text"
              :accept="fileType.join(',')" :multiple="false" :show-file-list="false" :file-list="fileList"
              :http-request="vieImageUpload" :on-success="onSuccess" action="placeholder" drag>
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">
                将文件拖到此处，或<em>点击上传</em>
              </div>
            </el-upload>
          </div>
        </el-col>
        <el-col :span="8" class="grid-content">
          <el-row>
            <span class="label">样例表单:</span>
          </el-row>
          <el-row v-for="(form, index) in vie_form_samples" :key="form.url" :span="1" class="grid-content">
            <CVDemo :thumbnail="true" :loading="loading" :data="form" @click="vie(index, $event, form)"></CVDemo>
          </el-row>
        </el-col>
      </el-row>
    </div>
    <div class="container">
      <el-row v-show="result.code == 200" :gutter="20">
        <el-col :span="12" class="grid-content-vie-info" style="height: 620px;">
          <div v-if="!showPicPoint" style="display: flex; justify-content: center; align-items: center; height: 95%;">
            <img style="max-width: 100%; max-height: 100%; object-fit: contain;" :src="this.fileObj.url">
          </div>
          <div v-if="showPicPoint" style="display: flex; justify-content: center; align-items: center; height: 100%;">
            <canvas id="canvas" style="max-width: 100%; max-height: 100%; object-fit: contain;"></canvas>
          </div>
          <div>
            <a v-show="result.code == 200" target="_blank" download="">
              <div v-if="!showPicPoint" class="download-doc-btn"
                @click="openPicPointImg()">查看文本框</div>
              <div v-if="showPicPoint" class="download-doc-btn" @click="openOriginImg()">查看原图</div>
            </a>
          </div>
        </el-col>
        <el-col v-show="show_items_info === false" :span="12" class="grid-content-vie-info" style="height: 620px;">
          <el-scrollbar style="height: 95%">
            <table border="solid" cellspacing="0" cellpadding="10" frame="solid" rules="solid" style="width: 100%">
              <tbody align="center" valign="center">
                <tr v-for="(infos, index) in result.info" :key="index">
                  <table border="solid" cellspacing="0" cellpadding="10" frame="solid" rules="solid"
                    style="width: 100%">
                    <tr v-for="(info, index) in infos" :key="index">
                      <td bgcolor="#e5e9f2">{{ info.key }}</td>
                      <td>{{ info.value }}</td>
                    </tr>
                  </table>
                </tr>
              </tbody>
            </table>
          </el-scrollbar>
          <div>
            <a v-show="result.code == 200" target="_blank" download="">
              <div  class="download-doc-btn" @click="exportXlsx()">导出XLSX</div>
            </a>
          </div>
        </el-col>
        <el-col v-show="show_items_info === true" :span="12" class="grid-content-vie-info" style="height: 576px;">
          <ul>
            <li v-for="(errMsg, index) in result.errMsgs" :key="index">
              {{ errMsg }}
            </li>
          </ul>
          <el-scrollbar style="height: 100%">
            <table border="solid" cellspacing="0" cellpadding="10" frame="solid" rules="solid" style="width: 100%">
              <tbody align="center" valign="center">
                <tr v-for="(infos, index) in result.info" :key="index">
                  <table border="solid" cellspacing="0" cellpadding="10" frame="solid" rules="solid"
                    style="width: 100%">
                    <tr v-for="(info, index) in infos" :key="index">
                      <td bgcolor="#e5e9f2">{{ info.key }}</td>
                      <td>{{ info.value }}</td>
                    </tr>
                  </table>
                </tr>
              </tbody>
            </table>
            <table border="solid" cellspacing="0" cellpadding="10" frame="solid" rules="solid" style="width: 100%">
              <tbody align="center" valign="center">
                <tr v-for="(items, key) in result.items" :key="key">
                  <table border="solid" cellspacing="0" cellpadding="10" frame="solid" rules="solid"
                    style="width: 100%">

                    <tr v-for="(item, index) in items" :key="index">
                      <td bgcolor="#e5e9f2">{{ index }}</td>
                      <td>
                        <table border="solid" cellspacing="0" cellpadding="10" frame="solid" rules="solid"
                          style="width: 100%">
                          <thead>
                            <tr align="center" valign="center" bgcolor="#e5e9f2">
                              <th>字段名</th>
                              <th>字段值</th>
                            </tr>
                          </thead>
                          <tbody align="center" valign="center">
                            <tr v-for="(_item, index) in item" :key="index">
                              <td bgcolor="#e5e9f2">{{ _item.key }}</td>
                              <td>{{ _item.value }}</td>
                            </tr>
                          </tbody>
                        </table>
                      </td>
                    </tr>
                  </table>

                </tr>
              </tbody>
            </table>
          </el-scrollbar>
          <div>
            <a v-show="result.code == 200" target="_blank" download="">
              <div class="download-doc-btn" @click="exportXlsx()">导出XLSX</div>
            </a>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import axios from 'axios'
import CVHint from '@/views/cv/layout/components/CVHint'
import CVDemo from '@/views/cv/layout/components/CVDemo'
import CryptoJS from 'crypto-js'
import {
  formatfileSize,
  isValidFile,
  convertOpTimes
}
  from '@/api/cv/utils'
import * as XLSX from 'xlsx'
import {
  httpRequesSignKs,
  uploadKs3Store,
} from '@/api/cv/request-ks3'
import { getHeaders } from '@/utils/get-header'

export default {
  name: 'LayoutVIE',
  components: { CVHint, CVDemo },
  props: {
    maxFileNum: {
      validator(value) {
        if (value <= 0) {
          return 1
        }
        return value
      },
      default: 1
    },
    fileType: {
      type: Array,
      default: function () {
        return ['.png', '.jpg', '.jpeg']
      }
    }
  },
  data() {
    return {
      loading: false,
      url: {
        value: '//pcv-test.wps.cn',
        default: '//pcv-test.wps.cn'
      },
      fileUrl: '',
      remark: '',
      dpi: {
        value: 144,
        default: 144,
        min: 1,
        max: 400
      },
      formType: {
        value: ''
      },
      startPage: {
        value: 0,
        default: 0,
        min: 0,
        max: 10,
        exporttype: '',
        version: ''
      },
      endPage: {
        value: 0,
        default: 0,
        min: 0,
        max: 10
      },
      uri: '/create',
      fileObj: {
        name: '',
        size: 0,
        url: ''
      },
      formTypes: [],
      formTypeKeysArr: [],
      fileList: [],
      exporttypeOptions: [],
      versions: [],
      origin_info: [],
      item_extend_infos: [],
      result: {
        code: 0,
        info: [],
        items: [],
        duration: 0,
        opTimes: Array(11).fill(0), // 时间戳数组
        errMsgs: []
      },
      statistic: {
        status: -1,
        duration: 0
      },
      show_items_info: false,
      showPicPoint: false,
      // 表单
      item_extend: 'Item Extend 信息',
      // 表单（货物）
      extend_infos: {},
      vie_form_samples: [],
    }
  },
  computed: {
    formattedDuration() {
      return this.statistic.duration.toFixed(3) + 's'
    },
    fileName() {
      const { name } = this.fileObj
      return name.trim()
    },
    fileSize() {
      const { size } = this.fileObj
      return formatfileSize(size)
    }
  },
  created() {
    this.getDicts('pdf_export_type').then((response) => {
      this.exporttypeOptions = response.data
    })

    this.getDicts('vie_form_type').then((response) => {
      this.formTypes = response.data
      if (Array.isArray(this.formTypes) && this.formTypes.length > 0) {
        const formType = this.formTypes.find(
          (formType) => formType.label === '自动识别'
        )
        if (formType) {
          this.formType.value = formType.value
        }
      }
      for (let index = 0; index < response.data.length; index++) {
        const element = response.data[index]
        this.formTypeKeysArr.push(element.value)
      }
    })
    this.getDicts('pdf_convert_version').then(response => {
      this.versions = response.data.slice(1, 3)
      // this.versions = response.data
      if (this.versions.length > 0) {
        // this.versions[1].remark = "开发版由于经常调试，如有问题可以联系我们"
        this.url.value = this.versions[0].value
        this.startPage.version = this.versions[0].label
        this.remark = this.versions[0].remark
      }
    })
    this.getDicts('vie_card_sample').then((response) => {
      if (response.data.length > 0) {
        const form_arr = []
        const dataLength = response.data.length
        for (let index = 0; index < dataLength; index++) {
          const element = response.data[index]
          const dict = JSON.parse(element.remark)
          const sample = {
            url: dict.url,
            name: element.label,
            size: element.value
          }

          if (dict.type === 'form') {
            form_arr.push(sample)
          }
        }
        if (form_arr.length > 0) {
          this.vie_form_samples = []
          this.vie_form_samples.push(form_arr)
        }
      }
    })
  },
  methods: {
    // 提取重复代码到单独的函数中
    createTableRow(data) {
      return data.map(item => {
        let key = '';
        let value = '';
        item.key.forEach(k => key += k.data + '-');
        item.value.forEach(v => value += v.data || '-');
        return { key: key.slice(0, -1), value };
      });
    },
    // 使用辅助函数简化逻辑
    exportXlsx() {
      const infos = this.result.info;
      const items = this.result.items;
      const headers = {};
      const rows = [];

      // 使用辅助函数处理信息数据
      infos.forEach(data => {
        const row = this.createTableRow(data);
        row.forEach(item => {
          if (!headers[item.key]) {
            headers[item.key] = [];
          }
          headers[item.key].push(item.value);
        });
      });

      // 使用辅助函数处理项目数据
      items.forEach(data => {
        const row = this.createTableRow(data);
        row.forEach(item => {
          if (!headers[item.key]) {
            headers[item.key] = [];
          }
          headers[item.key].push(item.value);
        });
      });

      const headerKeys = Object.keys(headers);
      const maxLength = Math.max(...headerKeys.map(key => headers[key].length));

      // 根据headers数据生成每一行的数据
      for (let i = 0; i < maxLength; i++) {
        const row = {};
        headerKeys.forEach(key => {
          row[key] = headers[key][i] || '';
        });
        rows.push(row);
      }

      // 将行数据转换为XLSX的工作表
      const worksheet = XLSX.utils.json_to_sheet(rows);

      // 计算列宽
      headerKeys.forEach(key => {
        const columnWidth = Math.max(key.length, ...headers[key].map(value => value.toString().length));
        worksheet['!cols'] = worksheet['!cols'] || [];
        worksheet['!cols'].push({ wch: columnWidth });
      });

      // 创建一个新的XLSX工作簿
      const workbook = XLSX.utils.book_new();
      // 将工作表附加到工作簿中
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1');
      // 将工作簿写入文件
      XLSX.writeFile(workbook, this.fileName + '.xlsx');
    },
    openOriginImg() {
      this.showPicPoint = false
    },
    openPicPointImg() {
      this.showPicPoint = true
      const texts = this.origin_info
      const texts_item_info = this.item_extend_infos
      // 给图片添加点击事件
      function addEventListenerToImg(w, element, sentence) {
        var textElement = document.createElement('div')
        textElement.style.display = 'none'
        textElement.style.position = 'absolute'
        textElement.style.background = 'rgba(0, 0, 0, 0.7)'
        textElement.style.color = 'white'
        textElement.innerText = 'None'
        w.document.body.appendChild(textElement)
        var pos = sentence.box
        var text = sentence.data
        element.addEventListener('click', function (event) {
          // 获取点击位置的坐标，需要加上页面滚动位置
          var x = event.clientX + w.scrollX || w.pageXOffset
          var y = event.clientY + w.scrollY || w.pageYOffset
          if (x >= pos[0] && x <= pos[4] && y >= pos[1] && y <= pos[5]) {
            // 设置文字提示的位置，并显示
            textElement.style.left = x + 'px'
            textElement.style.top = y + 'px'
            textElement.style.display = 'block'
            textElement.innerText = text
          } else {
            textElement.style.display = 'none'
          }
        })
      }
      // const newWindow = window.open()
      const img = new Image()
      img.src = this.fileObj.url
      img.onload = function () {
        const canvas = document.getElementById('canvas');
        canvas.width = img.width
        canvas.height = img.height
        // 获取2D上下文
        const ctx = canvas.getContext('2d')
        ctx.drawImage(img, 0, 0)
        ctx.strokeStyle = 'red'
        ctx.lineWidth = img.width < 1000 ? 2 : 10
        for (var text of texts) {
          for (var item of text) {
            if (Object.keys(item).length > 0) {
              for (var sentence of item.value) {
                if (sentence.box !== null) {
                  if (sentence.box.length !== 0) {
                    var pos = sentence.box
                    ctx.beginPath()
                    ctx.moveTo(pos[0], pos[1])
                    ctx.lineTo(pos[2], pos[3])
                    ctx.lineTo(pos[4], pos[5])
                    ctx.lineTo(pos[6], pos[7])
                    ctx.closePath()
                    ctx.stroke()
                    addEventListenerToImg(window, canvas, item.value)
                  }
                }
              }
              for (var sentence of item.key) {
                if (sentence.box !== null) {
                  if (sentence.box.length !== 0) {
                    var pos = sentence.box
                    ctx.beginPath()
                    ctx.moveTo(pos[0], pos[1])
                    ctx.lineTo(pos[2], pos[3])
                    ctx.lineTo(pos[4], pos[5])
                    ctx.lineTo(pos[6], pos[7])
                    ctx.closePath()
                    ctx.stroke()
                    addEventListenerToImg(window, canvas, item.value)
                  }
                }
              }
            }
          }
        }
        for (var items of texts_item_info) {
          for (var item of items) {
            if (Object.keys(item).length > 0) {
              for (var sentence of item) {
                for (var block of sentence.key) {
                  if (block.box !== null) {
                    if (block.box.length !== 0) {
                      var pos = block.box
                      ctx.beginPath()
                      ctx.moveTo(pos[0], pos[1])
                      ctx.lineTo(pos[2], pos[3])
                      ctx.lineTo(pos[4], pos[5])
                      ctx.lineTo(pos[6], pos[7])
                      ctx.closePath()
                      ctx.stroke()
                      addEventListenerToImg(window, canvas, sentence)
                    }
                  }
                }
                for (var block of sentence.value) {
                  if (block.box !== null) {
                    if (block.box.length !== 0) {
                      var pos = block.box
                      ctx.beginPath()
                      ctx.moveTo(pos[0], pos[1])
                      ctx.lineTo(pos[2], pos[3])
                      ctx.lineTo(pos[4], pos[5])
                      ctx.lineTo(pos[6], pos[7])
                      ctx.closePath()
                      ctx.stroke()
                      addEventListenerToImg(window, canvas, sentence)
                    }
                  }
                }
              }
            }
          }
        }
      }
    },
    handleSelect(item) {
      this.startPage.version = item.label
      this.remark = item.remark
    },
    selectformType(value) {
      this.formType.value = value
    },
    handleformTypeSelect(item) {
      this.formType.value = item.value
    },
    selectVersion(value) {
      this.url.value = value
    },
    handleOpTimes(times) {
      // 1.时间戳转换
      const opTimes = this.result.opTimes
      convertOpTimes(opTimes, times)
    },
    fileChange(file, fileList) {
      this.fileList = fileList.slice(-this.maxFileNum)
    },
    onSuccess(response, file, fileList) {
      // hook
      this.fileChange(file, fileList)
    },
    resetResult() {
      this.percentage = 0
      this.result = {
        code: 0,
        info: [],
        items: [],
        duration: 0,
        opTimes: Array(11).fill(0), // 时间戳数组
        errMsgs: []
      }
      this.statistic = {
        status: -1,
        duration: 0
      }
    },
    beforeUpload(file) {
      // 清理旧结果
      this.fileObj = { name: '', size: 0, url: '' }
      this.resetResult()

      // 文件类型判断
      const { name } = file
      var ext = '.' + name.trim().split('.').pop()
      ext = ext.toLowerCase()
      if (!isValidFile(ext, this.fileType)) {
        this.$message({
          showClose: true,
          message: '请选择png/jpg/jpeg文件!',
          center: true,
          duration: 4000,
          type: 'error'
        })
        return false
      } else {
        this.fileObj = file
        return true
      }
    },
    async vie(arrayid, id, files) {
      // 开始加载
      this.loading = true
      const file = files[id]
      this.fileObj = { name: file.name, size: file.size }
      this.resetResult()

      const data = {
        img_url: file.url,
        card_type: this.formType.value,
        get_extend: 1
      }
      this.fileObj.url = file.url
      this.showPicPoint = false
      const axios_vie_post = {
        method: 'post',
        headers: {
          'Cache-Control': 'no-cache',
          'Content-Type': 'application/json',
          ...getHeaders()
        },
        url: this.url.value + '/layout/vie/struct',
        data,
        responseType: 'json'
      }
      const s_time = new Date().getTime()
      const status = await this.httpRequest(axios_vie_post)
      // 结束加载
      this.loading = false
      const e_time = new Date().getTime()
      this.statistic = {
        status,
        duration: this.result.duration || (e_time - s_time) / 1000
      }
      return Promise.resolve()
    },
    getFileNameMD5(name) {
      const userId = this.$store.state.user.userId
      const deptId = this.$store.state.user.deptId
      const useremail = this.$store.state.user.email
      var a = name.lastIndexOf('.')
      var filter = name.substring(a + 1, name.length)
      var fname = name.substring(0, a)

      var mddata = fname + userId + useremail + deptId

      return CryptoJS.MD5(mddata).toString() + '.' + filter
    },
    getDataStructure(data, version) {
      const receiptInfo = data.page_info[0];
      return {
        element_infos: receiptInfo.element_info,
        img_url: receiptInfo.shape.origin_change_image_url
      };
      // }
    },
    async vieImageUpload(param) {
      // 开始加载
      this.loading = true

      const { file } = param

      if (!this.beforeUpload(file)) {
        // 结束加载
        this.loading = false
        return Promise.reject()
      }
      const { name } = file

      const filenamebymd5 = this.getFileNameMD5(name)
      const res_sign_data = await httpRequesSignKs(filenamebymd5)
      const s_time = new Date().getTime()
      if (res_sign_data.status === 200) {
        const res_uploadks3_data = await uploadKs3Store(res_sign_data, file)
        if (res_uploadks3_data.status === 200) {
          const imageurl =
            'https://' +
            res_sign_data.data.data['url'] +
            '/' +
            res_sign_data.data.data['key']
          const data = {
            img_url: imageurl,
            card_type: this.formType.value,
            get_extend: 1
          }
          this.fileObj.url = imageurl
          this.showPicPoint = false
          const axios_vie_post = {
            method: 'post',
            headers: {
              'Cache-Control': 'no-cache',
              'Content-Type': 'application/json',
              ...getHeaders()
            },
            url: this.url.value + '/layout/vie/struct',
            data,
            responseType: 'json',
            processData: false // 必须
          }
          await this.httpRequest(axios_vie_post)
        }
      }
      param.onSuccess()
      // 结束加载
      this.loading = false
      const e_time = new Date().getTime()
      this.statistic = {
        status: 1,
        duration: this.result.duration || (e_time - s_time) / 1000
      }
      return Promise.resolve()
    },
    httpRequest(axios_config) {
      this.show_items_info = false
      return axios({
        ...axios_config,
        validateStatus: function (status) {
          if (status == 400) {
            return status
          } else {
            return status >= 200 && status < 300; // 允许处理200-299范围内的状态码  
          }
        }
      })
        .then((res) => {
          if (res === undefined) {
            console.log('undefined res')
            return Promise.resolve(-1)
          }
          // 处理完成
          const { data } = res
          if (res.status === 400) {
            this.result.errMsgs = data.errMsgs
            return Promise.reject('抱歉，暂不支持此类型票证信息提取')
          }

          if (data.errMsgs !== undefined) {
            this.result.errMsgs = data.errMsgs
            return Promise.reject('详情查看错误信息')
          }

          const { element_infos, img_url } = this.getDataStructure(data.data, this.startPage.version)
          this.origin_info = []
          this.item_extend_infos = []
          let new_infos = []
          let new_item_infos = []

          element_infos.forEach(info => {
            this.origin_info.push(info.info)
            this.item_extend_infos.push(info.item_info[0])
            let new_info = []
            let new_item_info = []
            info.info.forEach(element => {
              if (element.key && element.value) {
                let key = ''
                let value = ''
                element.key.forEach(k => {
                  key += k.data + '-'
                })
                element.value.forEach(v => {
                  if (v.data) {
                    value += v.data
                  } else {
                    value += '-'
                  }
                })
                new_info.push({ key: key.slice(0, -1), value })  // Remove trailing '-'
              }
            })
            new_infos.push(new_info)
            // 
            if (info.item_info[0]) {
            info.item_info[0].forEach(element => {
              let item = []
              for (let key in element) {
                let element_detail = element[key]
                if (element_detail.key && element_detail.value) {
                  let key_str = ''
                  let value_str = ''
                  element_detail.key.forEach(k => {
                    key_str += k.data + '-'
                  })
                  element_detail.value.forEach(v => {
                    if (v.data) {
                      value_str += v.data
                    } else {
                      value_str += '-'
                    }
                  })
                  item.push({ key: key_str.slice(0, -1), value: value_str })  // Remove trailing '-'
                }
              }
              new_item_info.push(item)
            })
              new_item_infos.push(new_item_info)
            }
          })

          // 有旋转图片拿旋转后的，否则是原图
          if (img_url !== "") {
            this.fileObj.url = img_url.replace(/-internal/g, "")
          }
          this.result.code = data.code
          this.result.info = new_infos
          this.result.items = new_item_infos
          if (Object.keys(this.result.items).length !== 0) {
            this.show_items_info = true
            return Promise.resolve(1)
          }
        })
        .catch((error) => {
          this.$message({
            showClose: true,
            message: '表单信息提取失败:' + error,
            center: true,
            duration: 4000,
            type: 'error'
          })
          return Promise.resolve(0)
        })
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/styles/mixin.scss";

.container {
  padding-bottom: 2%;
  padding-left: 8%;
  padding-right: 8%;
}

::v-deep .el-tabs__item {
  padding: 0 16px !important;
  height: 50px;
  line-height: 50px;
  font-size: 15px;
  border: none;
  background-color: #f5f5f5;
  cursor: pointer;
  color: #1106a5;
}

::v-deep .el-tabs__item:hover {
  background-color: #3591ad;
}

::v-deep .el-tabs__item.is-active {
  background-color: #f5f4fa;
}

::v-deep .default-file-single {
  .thumbnail {
    img {
      width: 200%;
      height: 90%;
    }
  }
}

.el-col {
  border-radius: 8px;
  border: 1px dashed #d9d9d9;
  background-color: #ffffff;
  color: #666666;
  padding: 1%;
}

.bg-purple-dark {
  background: #99a9bf;
}

.bg-purple {
  background: #d3dce6;
}

.bg-purple-light {
  background: #e5e9f2;
}

.grid-content {
  border-radius: 8px;
  vertical-align: middle;
}

.grid-content-vie-info {
  border-radius: 8px;
  vertical-align: middle;
  height: 620px;
}

.row-bg {
  padding: 10px 0;
  background-color: #f9fafc;
}

.upload-container {
  width: 100%;
  padding: 1%;
  position: relative;
  @include clearfix;

  .params-container {
    width: 50%;
    display: inline-block;
    padding-left: 1%;
    padding-right: 1%;
  }

  .params {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }

  .param-label {
    width: 50%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-bottom: 2%;
  }

  .param-input {
    width: 50%;
    text-align: right;
    margin-bottom: 2%;
  }
}

.result {
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

.download-doc-btn {
  text-align: center;
  width: 100%;
  height: 4vh;
  font-size: 2vh;
  line-height: 4vh;
  color: #fff;
  background-color: #338ef0;
  border: none;
  border-radius: 4px;
  margin-top: 8px;
}

.download-doc-btn a {
  text-decoration: none;
  color: #fff;
}
</style>
