<template>
  <div>
    <CVHint>常见票证信息提取已开通体验支持！</CVHint>
    <div class="container">
      <el-row type="flex" justify="center" :gutter="10">
        <el-col :span="8" class="grid-content">
          <div class="upload-container">
            <div class="params-container" style="width: 100% !important" />
            <div class="params-container">
              <div class="params">
                <span class="param-label">引擎版本 :</span>
                <el-select v-model="startPage.version" class="param-input" placeholder="引擎版本" size="small"
                  @change="selectVersion">
                  <el-option v-for="dict in versions" :key="dict.value" :label="dict.label" :value="dict.value"
                    @click.native="handleSelect(dict)" />
                </el-select>
              </div>
            </div>
            <div class="params-container" style="width: 100% !important" />
            <div class="params-container">
              <div v-show="startPage.version === '开发版'" class="params">
                <span class="param-label">票证类型 :</span>
                <el-select v-model="invoiceType.value" class="param-input" placeholder="细分类型" size="small"
                  @change="selectInvoiceType">
                  <el-option v-for="dict in invoiceTypes" :key="dict.value" :label="dict.label" :value="dict.value"
                    @click.native="handleInvoiceTypeSelect(dict)" />
                </el-select>
              </div>
            </div>
            <div class="params-container" style="width: 100% !important">
              <div class="params">
                <span style="color: red"> {{ remark }}</span>
              </div>
            </div>
            <div class="params-container">
              <div v-show="fileName !== ''" class="params">
                <span class="param-label">{{ fileName }} :</span>
                <span class="param-input">
                  {{ fileSize }}
                </span>
              </div>
              <div v-show="statistic.status !== -1" class="params">
                <span class="param-label">处理用时 :</span>
                <span class="param-input">
                  {{ formattedDuration }}
                </span>
              </div>
              <div v-show="statistic.status !== -1" class="params">
                <span v-show="statistic.status === 1" class="param-label" style="color: #00ff33">提取成功!</span>
                <span v-show="statistic.status === 0" class="param-label" style="color: #ff0000">提取失败!</span>
              </div>
            </div>

            <el-upload v-loading="loading" element-loading-text="文件处理中" class="uploader" list-type="text"
              :accept="fileType.join(',')" :multiple="false" :show-file-list="false" :file-list="fileList"
              :http-request="vieImageUpload" :on-success="onSuccess" action="placeholder" drag>
              <i class="el-icon-upload" />
              <div class="el-upload__text">
                将文件拖到此处，或<em>点击上传</em>
              </div>
            </el-upload>
          </div>
        </el-col>
        <el-col :span="8" class="grid-content">
          <el-tabs v-model="activeTab" @tab-click="toggleTab">
            <el-tab-pane label="发票示例" name="invoice">
              <el-row v-if="activeTab === 'invoice'" v-for="(invoice, index) in vie_invoice_samples" :key="invoice.url"
                :span="1" class="grid-content">
                <CVDemo :thumbnail="true" :loading="loading" :data="invoice" @click="vie(index, $event, invoice)">
                </CVDemo>
              </el-row>
            </el-tab-pane>
            <el-tab-pane label="票据示例" name="receipt">
              <el-row v-if="activeTab === 'receipt'" v-for="(receipt, index) in vie_receipt_samples" :key="receipt.url"
                :span="5" class="grid-content">
                <CVDemo :thumbnail="true" :loading="loading" :data="receipt" @click="vie(index, $event, receipt)">
                </CVDemo>
              </el-row>
            </el-tab-pane>
            <el-tab-pane label="证件示例" name="card">
              <el-row v-if="activeTab === 'card'" v-for="(card, index) in vie_card_samples" :key="card.url" :span="5"
                class="grid-content">
                <CVDemo :thumbnail="true" :loading="loading" :data="card" @click="vie(index, $event, card)"></CVDemo>
              </el-row>
            </el-tab-pane>
            <el-tab-pane label="卡证示例" name="certificate">
              <el-row v-if="activeTab === 'certificate'" v-for="(certificate, index) in vie_certificate_samples"
                :key="certificate.url" :span="5" class="grid-content">
                <CVDemo :thumbnail="true" :loading="loading" :data="certificate" @click="vie(index, $event, certificate)">
                </CVDemo>
              </el-row>
            </el-tab-pane>
          </el-tabs>
        </el-col>
      </el-row>
    </div>
    <br>
    <div class="container">
      <el-row v-show="result.code == 200" :gutter="20">
        <el-col :span="12" class="grid-content-vie-info">
          <div v-if="!showPicPoint" style="display: flex; justify-content: center; align-items: center;">
            <img style="max-width: 100%; max-height: 500px; object-fit: contain;" :src="this.fileObj.url">
          </div>
          <div v-if="showPicPoint" style="display: flex; justify-content: center; align-items: center;">
            <canvas id="canvas" style="max-width: 100%; max-height: 500px; object-fit: contain;"></canvas>
          </div>
          <div>
            <a v-show="result.code == 200" target="_blank" download="">
              <div v-if="!showPicPoint" class="download-doc-btn" @click="openPicPointImg()">查看文本框</div>
              <div v-if="showPicPoint" class="download-doc-btn" @click="openOriginImg()">查看原图</div>
              <div v-if="!showPicPoint" class="download-doc-btn" @click="openNewWindowPicPointImg()">新窗口查看文本框</div>
            </a>
          </div>
        </el-col>
        <!-- 展示(额外信息，货物，药品，项目) -->
        <el-col v-show="show_extend_info === true" :span="12" class="grid-content-vie-info">
          <ul>
            <li v-for="(errMsg, index) in result.errMsgs" :key="index">
              {{ errMsg }}
            </li>
          </ul>
          <el-scrollbar style="height: 100%">
            <table border="solid" cellspacing="0" cellpadding="10" frame="solid" rules="solid" style="width: 100%">
              <thead>
                <tr align="center" valign="center" bgcolor="#e5e9f2">
                  <th>字段名</th>
                  <th>字段值</th>
                </tr>
              </thead>
              <tbody align="center" valign="center">
                <tr v-for="(item, index) in result.info" :key="index">
                  <td bgcolor="#e5e9f2">{{ item.key.data }}</td>
                  <td>{{ item.value.data }}</td>
                </tr>
              </tbody>
            </table>
            <table border="solid" cellspacing="0" cellpadding="10" frame="solid" rules="solid" style="width: 100%">
              <thead>
                <tr align="center" valign="center" bgcolor="#e5e9f2">
                  <th>序号</th>
                  <th>{{ this.card_extend }}</th>
                </tr>
              </thead>
              <tbody align="center" valign="center">
                <tr v-for="(value, key) in extend_infos" :key="key">
                  <td bgcolor="#e5e9f2">{{ key }}</td>
                  <td>
                    <table border="solid" cellspacing="0" cellpadding="10" frame="solid" rules="solid"
                      style="width: 100%">
                      <thead>
                        <tr align="center" valign="center" bgcolor="#e5e9f2">
                          <th>字段名</th>
                          <th>字段值</th>
                        </tr>
                      </thead>
                      <tbody align="center" valign="center">
                        <tr v-for="(item, index) in value" :key="index">
                          <td bgcolor="#e5e9f2">{{ item.key.data }}</td>
                          <td>{{ item.value.data }}</td>
                        </tr>
                      </tbody>
                    </table>
                  </td>
                </tr>
              </tbody>
            </table>
          </el-scrollbar>
        </el-col>
        <!-- 通用票证信息展示 -->
        <el-col v-show="show_result_info === true" :span="12" class="grid-content-vie-info">
          <ul>
            <li v-for="(errMsg, index) in result.errMsgs" :key="index">
              {{ errMsg }}
            </li>
          </ul>
          <el-scrollbar style="height: 100%">
            <table border="solid" cellspacing="0" cellpadding="10" frame="solid" rules="solid" style="width: 100%">
              <tbody align="center" valign="center">
                <tr v-for="item in result.info" :key="item.key.data">
                  <td bgcolor="#e5e9f2">{{ item.key.data }}</td>
                  <td>{{ item.value.data }}</td>
                </tr>
              </tbody>
            </table>
          </el-scrollbar>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import axios from 'axios'
import CVHint from '@/views/cv/layout/components/CVHint'
import CVDemo from '@/views/cv/layout/components/CVDemo'
import CryptoJS from 'crypto-js'
import {
  urlRegex,
  formatfileSize,
  isValidFile,
  convertOpTimes
} from '@/api/cv/utils'
import {
  httpRequesSignKs,
  uploadKs3Store,
} from '@/api/cv/request-ks3'
import { getHeaders } from '@/utils/get-header'
import a from 'file-saver'

export default {
  name: 'LayoutVIE',
  components: { CVHint, CVDemo },
  props: {
    maxFileNum: {
      validator(value) {
        if (value <= 0) {
          return 1
        }
        return value
      },
      default: 1
    },
    fileType: {
      type: Array,
      default: function () {
        return ['.png', '.jpg', '.jpeg']
      }
    }
  },
  data() {
    return {
      loading: false,
      url: {
        value: '//pcv-test.wps.cn',
        default: '//pcv-test.wps.cn'
      },
      fileUrl: '',
      remark: '',
      dpi: {
        value: 144,
        default: 144,
        min: 1,
        max: 400
      },
      invoiceType: {
        value: ''
      },
      startPage: {
        value: 0,
        default: 0,
        min: 0,
        max: 10,
        exporttype: '',
        version: ''
      },
      endPage: {
        value: 0,
        default: 0,
        min: 0,
        max: 10
      },
      uri: '/create',
      fileObj: {
        name: '',
        size: 0,
        url: ''
      },
      invoiceTypes: [],
      invoiceTypeKeysArr: [],
      fileList: [],
      exporttypeOptions: [],
      versions: [],
      result: {
        code: 0,
        info: [],
        item_info: [],
        info_and_items:[],
        duration: 0,
        opTimes: Array(11).fill(0), // 时间戳数组
        errMsgs: []
      },
      statistic: {
        status: -1,
        duration: 0
      },
      show_result_info: false,
      show_extend_info: false,
      showPicPoint:false,
      // 发票
      card_extend: '货物信息',
      // 发票（货物）
      extend_infos: {},
      vie_invoice_samples: [],
      vie_card_samples: [],
      vie_certificate_samples: [],
      vie_receipt_samples: [],
      activeTab: 'invoice', // 默认显示发票示例
      bill_extend_types: [
        'bill_scan',
        'bill_stand',
        'bill_toll',
        'js_general_toll_invoice',
        'zj_general_invoice',
        'electronic_invoice',
        'goods_transportation_electronic_invoice',
        'passenger_transportation_electronic_invoice',
        'volume_invoice',
        'outpatient_invoice',
        'inpatient_invoice',
        'print_invoice_hor',
        'print_invoice_vert',
        'print_invoice_gd',
        'print_invoice_js',
        'central_receipt',
        'express_receipt',
        'medical_details',
        'sell_list',
        'trip_table',
        'nontax_payment',
        'tax_payment',
        'air_ticket',
        'electronic_air_ticket',
        NaN
      ],
      // 门诊发票（药品明细） patient_invoice
      patient_invoice_types: [
        'outpatient_invoice',
        'inpatient_invoice',
        NaN
      ],
      // 非税收发票 central_receipt
      central_receipt_types: [
        'central_receipt',
        NaN
      ]
    }
  },
  computed: {
    formattedDuration() {
      return this.statistic.duration.toFixed(3) + 's'
    },
    fileName() {
      const { name } = this.fileObj
      return name.trim()
    },
    fileSize() {
      const { size } = this.fileObj
      return formatfileSize(size)
    }
  },
  created() {
    this.getDicts('pdf_export_type').then((response) => {
      this.exporttypeOptions = response.data
    })

    this.getDicts('vie_invoice_type').then((response) => {
      this.invoiceTypes = response.data
      if (Array.isArray(this.invoiceTypes) && this.invoiceTypes.length > 0) {
        const InvoiceType = this.invoiceTypes.find(
          (invoiceType) => invoiceType.label === '自动识别'
        )
        if (InvoiceType) {
          this.invoiceType.value = InvoiceType.value
        }
      }
      for (let index = 0; index < response.data.length; index++) {
        const element = response.data[index]
        this.invoiceTypeKeysArr.push(element.value)
      }
    })
    this.getDicts('pdf_convert_version').then(response => {
      this.versions = response.data
      if (this.versions.length > 0) {
        this.versions[0].remark = "如果想体验最新效果可以选择测试或者开发版，但测试与开发版由于经常调试，服务可能不稳定,如有问题可以联系我们"
        this.url.value = this.versions[0].value
        this.startPage.version = this.versions[0].label
        this.remark = this.versions[0].remark
      }
    })
    this.getDicts('vie_card_sample').then((response) => {
      if (response.data.length > 0) {
        const invoice_arr = []
        const card_arr = []
        const certificate_arr = []
        const receipt_arr = []
        const dataLength = response.data.length
        for (let index = 0; index < dataLength; index++) {
          const element = response.data[index]
          const dict = JSON.parse(element.remark)
          const sample = {
            url: dict.url,
            name: element.label,
            size: element.value
          }

          if (dict.type === 'invoice') {
            invoice_arr.push(sample)
          }
          if (dict.type === 'card') {
            card_arr.push(sample)
          }
          if (dict.type === 'certificate') {
            certificate_arr.push(sample)
          }
          if (dict.type === 'receipt') {
            receipt_arr.push(sample)
          }
        }
        if (invoice_arr.length > 0) {
          this.vie_invoice_samples = []
          this.vie_invoice_samples.push(invoice_arr)
        }
        if (card_arr.length > 0) {
          this.vie_card_samples = []
          this.vie_card_samples.push(card_arr)
        }
        if (certificate_arr.length > 0) {
          this.vie_certificate_samples = []
          this.vie_certificate_samples.push(certificate_arr)
        }
        if (receipt_arr.length > 0) {
          this.vie_receipt_samples = []
          this.vie_receipt_samples.push(receipt_arr)
        }
      }
    })
  },
  methods: {
    openOriginImg(){
      this.showPicPoint = false
    },
    openPicPointImg() {
      this.showPicPoint = true
      const texts = this.result.info_and_items
      // 给图片添加点击事件
      function addEventListenerToImg(w, element, sentence) {
        var textElement = document.createElement('div')
        textElement.style.display = 'none'
        textElement.style.position = 'absolute'
        textElement.style.background = 'rgba(0, 0, 0, 0.7)'
        textElement.style.color = 'white'
        textElement.innerText = 'None'
        w.document.body.appendChild(textElement)
        var pos = sentence.box
        var text = sentence.data
        element.addEventListener('click', function (event) {
          // 获取点击位置的坐标，需要加上页面滚动位置
          var x = event.clientX + w.scrollX || w.pageXOffset
          var y = event.clientY + w.scrollY || w.pageYOffset
          if (x >= pos[0] && x <= pos[4] && y >= pos[1] && y <= pos[5]) {
            // 设置文字提示的位置，并显示
            textElement.style.left = x + 'px'
            textElement.style.top = y + 'px'
            textElement.style.display = 'block'
            textElement.innerText = text
          } else {
            textElement.style.display = 'none'
          }
        })
      }
      // const newWindow = window.open()
      const img = new Image()
      img.src = this.fileObj.url
      img.onload = function () {
        // const canvas = newWindow.document.createElement('canvas')
        const canvas = document.getElementById('canvas');
        canvas.width = img.width
        canvas.height = img.height
        // 获取2D上下文
        const ctx = canvas.getContext('2d')
        ctx.drawImage(img, 0, 0)
        ctx.strokeStyle = 'red'
        ctx.lineWidth = img.width < 1000?2:10
        for (var item of texts) {
          // 可能出现没有句子的块，所以需要判断是否为null
          if (item.value.box !== null) {
            if (item.value.box.length !== 0) {
              var pos = item.value.box
              ctx.beginPath()
              ctx.moveTo(pos[0], pos[1])
              ctx.lineTo(pos[2], pos[3])
              ctx.lineTo(pos[4], pos[5])
              ctx.lineTo(pos[6], pos[7])
              ctx.closePath()
              ctx.stroke()
              addEventListenerToImg(window, canvas, item.value)
            }
          }
        }
        // newWindow.document.body.appendChild(canvas)
      }
    },
    openNewWindowPicPointImg() {
      // this.showPicPoint = true
      const texts = this.result.info_and_items
      // 给图片添加点击事件
      function addEventListenerToImg(w, element, sentence) {
        var textElement = document.createElement('div')
        textElement.style.display = 'none'
        textElement.style.position = 'absolute'
        textElement.style.background = 'rgba(0, 0, 0, 0.7)'
        textElement.style.color = 'white'
        textElement.innerText = 'None'
        w.document.body.appendChild(textElement)
        var pos = sentence.box
        var text = sentence.data
        element.addEventListener('click', function (event) {
          // 获取点击位置的坐标，需要加上页面滚动位置
          var x = event.clientX + w.scrollX || w.pageXOffset
          var y = event.clientY + w.scrollY || w.pageYOffset
          if (x >= pos[0] && x <= pos[4] && y >= pos[1] && y <= pos[5]) {
            // 设置文字提示的位置，并显示
            textElement.style.left = x + 'px'
            textElement.style.top = y + 'px'
            textElement.style.display = 'block'
            textElement.innerText = text
          } else {
            textElement.style.display = 'none'
          }
        })
      }

      const newWindow = window.open()
      const img = new Image()
      img.src = this.fileObj.url
      img.onload = function () {
        const canvas = newWindow.document.createElement('canvas')
        // const canvas = document.getElementById('canvas');
        canvas.width = img.width
        canvas.height = img.height

        // 获取2D上下文
        const ctx = canvas.getContext('2d')
        ctx.drawImage(img, 0, 0)
        ctx.strokeStyle = 'red'
        ctx.lineWidth = img.width < 1000?2:10

        for (var item of texts) {
          // 可能出现没有句子的块，所以需要判断是否为null
          if (item.value.box !== null) {
            if (item.value.box.length !== 0) {
              var pos = item.value.box
              ctx.beginPath()
              ctx.moveTo(pos[0], pos[1])
              ctx.lineTo(pos[2], pos[3])
              ctx.lineTo(pos[4], pos[5])
              ctx.lineTo(pos[6], pos[7])
              ctx.closePath()
              ctx.stroke()
              addEventListenerToImg(window, canvas, item.value)
            }
          }
        }
        newWindow.document.body.appendChild(canvas)
      }
    },
    toggleTab(tab) {
      this.activeTab = tab.name; // 切换选项卡
    },
    handleSelect(item) {
      this.startPage.version = item.label
      this.remark = item.remark
    },
    selectInvoiceType(value) {
      this.invoiceType.value = value
    },
    handleInvoiceTypeSelect(item) {
      this.invoiceType.value = item.value
    },
    selectVersion(value) {
      this.url.value = value
    },
    handleOpTimes(times) {
      // 1.时间戳转换
      const opTimes = this.result.opTimes
      convertOpTimes(opTimes, times)
    },
    urlChange(value) {
      // url 检测
      if (urlRegex.test(value)) {
        this.url.value = value
      } else {
        this.url.value = this.url.default
      }
    },
    numberInputChange(obj, value) {
      if (value === '') {
        obj.value = obj.default
      } else if (value > obj.max) {
        obj.value = obj.max
      } else if (value < obj.min) {
        obj.value = obj.min
      }
      obj.value = parseInt(value)
    },
    dpiChange(value) {
      this.numberInputChange(this.dpi, value)
    },
    startPageChange(value) {
      this.numberInputChange(this.startPage, value)
    },
    endPageChange(value) {
      this.numberInputChange(this.endPage, value)
    },
    fileChange(file, fileList) {
      this.fileList = fileList.slice(-this.maxFileNum)
    },
    onSuccess(response, file, fileList) {
      // hook
      this.fileChange(file, fileList)
    },
    resetResult() {
      this.percentage = 0
      this.result = {
        code: 0,
        info: {},
        duration: 0,
        opTimes: Array(11).fill(0), // 时间戳数组
        errMsgs: []
      }
      this.statistic = {
        status: -1,
        duration: 0
      }
    },
    beforeUpload(file) {
      // 清理旧结果
      this.fileObj = { name: '', size: 0, url: '' }
      this.resetResult()

      // 文件类型判断
      const { name } = file
      var ext = '.' + name.trim().split('.').pop()
      ext = ext.toLowerCase()
      if (!isValidFile(ext, this.fileType)) {
        this.$message({
          showClose: true,
          message: '请选择png/jpg/jpeg文件!',
          center: true,
          duration: 4000,
          type: 'error'
        })
        return false
      } else {
        this.fileObj = file
        return true
      }
    },
    async vie(arrayid, id, files) {
      // 开始加载
      this.loading = true
      const file = files[id]
      this.fileObj = { name: file.name, size: file.size }
      this.resetResult()

      const data = {
        img_url: file.url,
        card_type: this.invoiceType.value,
        get_extend: 1
      }
      this.fileObj.url = file.url
      this.showPicPoint = false
      const axios_vie_post = {
        method: 'post',
        headers: {
          'Cache-Control': 'no-cache',
          'Content-Type': 'application/json',
          ...getHeaders()
        },
        url: this.url.value + '/layout/vie/card',
        data,
        responseType: 'json'
      }
      const s_time = new Date().getTime()
      const status = await this.httpRequest(axios_vie_post)
      // 结束加载
      this.loading = false
      const e_time = new Date().getTime()
      this.statistic = {
        status,
        duration: this.result.duration || (e_time - s_time) / 1000
      }
      return Promise.resolve()
    },
    getFileNameMD5(name) {
      const userId = this.$store.state.user.userId
      const deptId = this.$store.state.user.deptId
      const useremail = this.$store.state.user.email
      var a = name.lastIndexOf('.')
      var filter = name.substring(a + 1, name.length)
      var fname = name.substring(0, a)

      var mddata = fname + userId + useremail + deptId

      return CryptoJS.MD5(mddata).toString() + '.' + filter
    },
    getDataStructure(data, version) {
        const receiptInfo = data.receipt_info[0];
        return {
          data_type: receiptInfo.img_cls,
          items: receiptInfo.item_info,
          info: receiptInfo.info,
          img_url:receiptInfo.shape.origin_change_image_url
        };
      // }
    },
    async vieImageUpload(param) {
      // 开始加载
      this.loading = true

      const { file } = param

      if (!this.beforeUpload(file)) {
        // 结束加载
        this.loading = false
        return Promise.reject()
      }
      const { name } = file

      const filenamebymd5 = this.getFileNameMD5(name)
      const res_sign_data = await httpRequesSignKs(filenamebymd5)
      const s_time = new Date().getTime()
      if (res_sign_data.status === 200) {
        const res_uploadks3_data = await uploadKs3Store(res_sign_data, file)
        if (res_uploadks3_data.status === 200) {
          const imageurl =
            'https://' +
            res_sign_data.data.data['url'] +
            '/' +
            res_sign_data.data.data['key']
          const data = {
            img_url: imageurl,
            card_type: this.invoiceType.value,
            get_extend: 1
          }
          this.fileObj.url = imageurl
          this.showPicPoint = false
          const axios_vie_post = {
            method: 'post',
            headers: {
              'Cache-Control': 'no-cache',
              'Content-Type': 'application/json',
              ...getHeaders()
            },
            url: this.url.value + '/layout/vie/card',
            data,
            responseType: 'json',
            processData: false // 必须
          }
          await this.httpRequest(axios_vie_post)
        }
      }
      param.onSuccess()
      // 结束加载
      this.loading = false
      const e_time = new Date().getTime()
      this.statistic = {
        status: 1,
        duration: this.result.duration || (e_time - s_time) / 1000
      }
      return Promise.resolve()
    },
    httpRequest(axios_config) {
      this.show_result_info = false
      this.show_extend_info = false
      return axios({
        ...axios_config,
        validateStatus: function (status) {
          if (status == 400) {
            return status
          } else {
            return status >= 200 && status < 300; // 允许处理200-299范围内的状态码  
          }
        }
      })
        .then((res) => {
          if (res === undefined) {
            console.log('undefined res')
            return Promise.resolve(-1)
          }
          // 处理完成
          const { data } = res
          if (res.status === 400) {
            this.result.errMsgs = data.errMsgs
            return Promise.reject('抱歉，暂不支持此类型票证信息提取')
          }

          if (data.errMsgs !== undefined) {
            this.result.errMsgs = data.errMsgs
            return Promise.reject('详情查看错误信息')
          }

          const { data_type, items,info, img_url } = this.getDataStructure(data.data, this.startPage.version)
          this.result.info_and_items = [...info]
          items.forEach(a => {
              a.forEach(b => {
                this.result.info_and_items.push(b);
              });
            });
          // 有旋转图片拿旋转后的，否则是原图
          if (img_url!=="") {
            this.fileObj.url = img_url.replace(/-internal/g, "")
          }
          this.result.code = data.code

          if (Object.keys(info).length !== 0) {
            this.result.info = info
            this.show_result_info = true
            // 四种发票类型，才展示货物信息
            if (this.bill_extend_types.includes(data_type)) {
              this.show_extend_info = true
              this.show_result_info = false
              this.card_extend = '货物信息'
              this.extend_infos = items
            }
            // 医疗门诊 展示药品信息
            if (this.patient_invoice_types.includes(data_type)) {
              this.card_extend = '药品信息'
              this.extend_infos = items
              this.show_extend_info = true
              this.show_result_info = false
            }
            // 非税收展示项目明细
            if (this.central_receipt_types.includes(data_type)) {
              this.show_extend_info = true
              this.card_extend = '项目明细'
              this.extend_infos = items
              this.show_result_info = false
            }
            return Promise.resolve(1)
          }
        })
        .catch((error) => {
          this.$message({
            showClose: true,
            message: '票证信息提取失败:' + error,
            center: true,
            duration: 4000,
            type: 'error'
          })
          return Promise.resolve(0)
        })
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/styles/mixin.scss";

.container {
  padding-bottom: 2%;
  padding-left: 8%;
  padding-right: 8%;
}

::v-deep .el-tabs__item {
  padding: 0 16px !important;
  height: 50px;
  line-height: 50px;
  font-size: 15px;
  border: none;
  background-color: #f5f5f5;
  cursor: pointer;
  color: #1106a5;
  /* Change the text color to a darker color */
}
::v-deep .el-tabs__item:hover {
  background-color: #3591ad;
}

::v-deep .el-tabs__item.is-active {
  background-color: #f5f4fa;
}

::v-deep .default-file-single {
  .thumbnail {
    img {
      width: 200%;
      height: 90%;
    }
  }
}

.el-col {
  border-radius: 8px;
  border: 1px dashed #d9d9d9;
  background-color: #ffffff;
  color: #666666;
  padding: 1%;
}

.bg-purple-dark {
  background: #99a9bf;
}

.bg-purple {
  background: #d3dce6;
}

.bg-purple-light {
  background: #e5e9f2;
}

.grid-content {
  border-radius: 8px;
  vertical-align: middle;
}

.grid-content-vie-info {
  border-radius: 8px;
  vertical-align: middle;
  height: 620px;
}

.row-bg {
  padding: 10px 0;
  background-color: #f9fafc;
}

.upload-container {
  width: 100%;
  padding: 1%;
  position: relative;
  @include clearfix;

  .params-container {
    width: 50%;
    display: inline-block;
    padding-left: 1%;
    padding-right: 1%;
  }

  .params {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }

  .param-label {
    width: 50%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-bottom: 2%;
  }

  .param-input {
    width: 50%;
    text-align: right;
    margin-bottom: 2%;
  }
}

.result {
  display: flex;
  justify-content: center;
  // align-items: center;
  align-items: flex-start;
  //height: 100vh; /* 设置高度，使容器占据整个视口 */
}

.download-doc-btn {
  text-align: center;
  width: 100%;
  height: 4vh;
  font-size: 2vh;
  line-height: 4vh;
  color: #fff;
  background-color: #338ef0;
  border: none;
  border-radius: 4px;
  margin-top: 8px;
}

.download-doc-btn a {
  text-decoration: none;
  color: #fff;
}
</style>
