<template>
  <el-form ref="form" v-model="data" label-width="120px">
    <div v-for="item in formStruct" :key="item.value.prop">
      <el-form-item :label="item.label" :prop="item.value.prop">
        <div class="sample-form">
          <el-row>
            <el-col :span="12">
              <el-form-item
                label="获取方式"
                :prop="`${item.value.prop}.form`"
              >
                <el-select
                  v-model="data[item.value.prop].form"
                  placeholder="form"
                  :disabled="!editable"
                >
                  <el-tooltip
                    v-for="formItem in sampleFormOptions"
                    :key="formItem.value"
                    :content="formItem.remark"
                    placement="top"
                  >
                    <el-option
                      :label="formItem.label"
                      :value="formItem.value"
                    />
                  </el-tooltip>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="样章类型" :prop="`${item.value.prop}.sample_type`">
                <el-tooltip
                  content="样章类型将会影响请求体的结构,如pdf2docx接口的url对应选择pdf和imgUrls参数对应选择img"
                  placement="top"
                >
                  <el-select
                    v-model="data[item.value.prop].sample_type"
                    placeholder="type"
                    :disabled="!editable"
                  >
                    <el-option
                      v-for="option in item.sample_types"
                      :key="option"
                      :label="option"
                      :value="option"
                    />
                  </el-select>
                </el-tooltip>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item
            label="样章来源"
            :prop="`${item.value.prop}.value`"
          >
            <el-tooltip
              content="请选定获取方式和样章类型后设置样章来源,根据获取方式不同可填写ks3目录、样章库标签、样章id等,多个值用逗号分隔"
              placement="top"
            >
              <el-input
                v-model="data[item.value.prop].value"
                placeholder="请选定获取方式和样章类型后设置样章来源,根据获取方式不同可填写ks3目录、样章库标签、样章id等"
                :disabled="!editable"
              />
            </el-tooltip>
          </el-form-item>
        </div>

      </el-form-item>
    </div>
  </el-form>
</template>

<script>
export default {
  name: 'SampleForm',
  props: {
    dynamic: {
      type: Array,
      default: () => ([])
    },
    struct: {
      type: Array,
      default: () => ([])
    },
    sampleFormOptions: {
      type: Array,
      default: () => ([
        { label: '样章库tag', value: 'sample_tag', remark: '样章' },
        { label: '样章库id', value: 'sample_id', remark: '样章+其他' }
      ])
    },
    editable: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      data: {},
      formStruct: []
    }
  },
  watch: {
    dynamic: {
      immediate: true,
      handler(newVal) {
        const res = this.convertToFlat(newVal)
        this.data = { ...this.data, ...res }
      },
      deep: true
    },
    struct: {
      immediate: true,
      handler(newVal, oldVal) {
        this.formStruct = this.processStruct(newVal)
      }
    }
  },
  created() {
    const res = this.convertToFlat(this.dynamic)
    this.data = { ...this.data, ...res }
    this.formStruct = this.processStruct(this.struct)
  },
  methods: {
    getComponentProps(item) {
      return item.props || {}
    },
    processStruct(struct) {
      const propMap = new Map()
      const tempData = {}
      struct.forEach(item => {
        if (item.value.type === 'other') {
          return
        }
        const prop = item.value.prop
        const sample_type = item.value.sample_type
        if (!propMap.has(prop)) {
          tempData[prop] = { sample_type: sample_type }
          propMap.set(prop, { ...item, sample_types: [sample_type] })
        } else {
          const existingItem = propMap.get(prop)
          existingItem.sample_types.push(sample_type)
        }
      })
      this.data = tempData
      return Array.from(propMap.values())
    },
    convertToDynamic() {
      const res = []
      this.struct.forEach(item => {
        const { prop, sample_type, path, types } = item.value
        let value = []
        let form = ''
        if (item.value.type === 'other') {
          value = item.value.value
          form = item.value.form
        } else if (this.data[prop] && this.data[prop].value && this.data[prop].sample_type === sample_type) {
          value = this.data[prop].value.split(',')
          form = this.data[prop].form
        } else {
          return
        }
        const pathParts = path.split('.')
        let currentLevel = res
        pathParts.forEach((part, index) => {
          let existNode = currentLevel.find(item => item.key === part)
          if (!existNode) {
            existNode = {
              key: part,
              type: types[index] || 'json',
              len: 1,
              children: []
            }
            currentLevel.push(existNode)
          }

          if (index === pathParts.length - 1) {
            // 最后一层
            existNode.value = value
            existNode.form = form
            delete existNode.children
          } else {
            currentLevel = existNode.children
          }
        })
      })
      return res
    },
    convertToFlat(res) {
      const flatData = {}

      const traverse = (nodes, parentPath = '') => {
        nodes.forEach(node => {
          const currentPath = parentPath ? `${parentPath}.${node.key}` : node.key

          if (node.children && node.children.length > 0) {
            // 如果有子节点，递归处理
            traverse(node.children, currentPath)
          } else {
            // 如果是叶子节点，根据 path 匹配 struct 中的 prop
            const matchedStruct = this.struct.find(item => item.value.path === currentPath)
            if (matchedStruct) {
              const prop = matchedStruct.value.prop // 从 struct 中获取 prop
              flatData[prop] = {
                value: Array.isArray(node.value) ? node.value.join(',') : node.value,
                form: node.form || '',
                sample_type: matchedStruct.value.sample_type
              }
            } else {
              console.warn(`未找到匹配的 struct 项: ${currentPath}`)
            }
          }
        })
      }

      traverse(res)
      return flatData
    },
    validate() {
      this.formStruct.forEach(item => {
        const prop = item.value.prop
        const flied = this.data[prop]
        if (!flied || !flied.value || flied.value.trim() === '') {
          this.$message.error(`请填写 ${item.label}`)
          return false
        }

        if (!flied.form) {
          this.$message.error(`请选择 ${item.label} 的获取方式`)
          return false
        }
      })
      return true
    }
  }
}
</script>

<style>
/* 让 el-input 和展开箭头保持同一行 */
.sample-form .el-form-item__content  {
  display: flow-root;
  margin-bottom: 22px;
}
.sample-form .el-form-item__label {
  text-align: left;
}
</style>
