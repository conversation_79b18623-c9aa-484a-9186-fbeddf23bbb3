<template>
  <div>
    <div style="border-bottom: 1px solid #ccc"><slot /></div>
    <!-- 调整尺寸 -->
    <!-- <div style="height: 324px; overflow-y:auto;"> -->
    <div style="height: 520px; overflow-y:auto;">
      <!-- 80px(div+margin)*4+1px(border)*4 -->
      <div v-for="(item, id) in data" :key="id" class="default-file-single">
        <div v-if="thumbnail" class="thumbnail">
          <img :src="item.url.replace(/(\.pdf|\.png|\.jpg|\.jpeg|\.json)$/,'.thumbnail.png')" alt="">
        </div>
        <div class="content">
          <p style="font-weight: bold">
            <a
              :href="item.url"
              target="_blank"
              class="default-file-single-title"
            >
              {{ item.name.replace(/(\.pdf|\.png|\.jpg|\.jpeg)$/,'') }}（查看原文）
            </a>
          </p>
          <br><el-button
            v-if="loading"
            class="default-file-single-click"
            type="primary"
            size="mini"
            disabled
          >加载中...</el-button>
          <el-button
            v-else
            class="default-file-single-click"
            type="primary"
            size="mini"
            @click="onclick(id)"
          >立即体验</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CVDemo',
  props: {
    thumbnail: {
      type: Boolean,
      default: function() {
        return false
      }
    },
    loading: {
      type: Boolean,
      default: function() {
        return false
      }
    },
    data: {
      type: Array,
      default: function() {
        return []
      }
    }
  },
  methods: {
    onclick(id) {
      this.$emit('click', id)
    }
  }
}
</script>

<style lang="scss" scoped>
.default-file-single {
  text-align: center;
  width: 100%; // height: 32(line-height)*2+8(padding)*2
  border-bottom: 1px solid #ccc;
  .thumbnail{
    width: 56px;
    height: 80px;
    float: left;
    display:flex;
    img{
      align-self: center;
      margin: auto;
      width: 90%;
      height: 90%;
    }
  }
  .content {
    padding-block-start: 8px;
    padding-block-end: 8px;
  }
  p{
    line-height: 32px;
    margin-block-start: 0px;
    margin-block-end: 0px;
  }
}

.default-file-single-click:hover,
.default-file-single-title:hover {
  cursor: pointer;
  color: #005cbf;
  text-decoration: underline;
}
</style>
