<template>
  <div style="line-height: 16px; ">
    <el-button type="primary" size="mini" @click="formatJson">格式化</el-button>
    <codemirror
      ref="myEditor"
      v-model="jsonText"
      :options="editorOptions"
    />
  </div>
</template>

<script>
import { codemirror } from 'vue-codemirror'
import 'codemirror/lib/codemirror.css'
import 'codemirror/theme/material.css'
import 'codemirror/mode/javascript/javascript.js'

export default {
  name: 'JsonEdit',
  components: { codemirror },
  props: {
    value: {
      type: [Array, Object],
      required: true
    },
    editable: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      jsonText: JSON.stringify(this.value, null, 2),
      editorOptions: {
        mode: 'application/json',
        theme: 'material',
        lineNumbers: true,
        lineWrapping: true,
        tabSize: 2,
        readOnly: 'nocursor',
        style: 'height: 100px;',
        height: '100px'
      }
    }
  },
  watch: {
    value: {
      handler(val) {
        try {
          this.jsonText = JSON.stringify(val, null, 2)
        } catch (e) {
          this.jsonText = ''
        }
      },
      deep: true
    },
    jsonText(val) {
      try {
        const parsed = JSON.parse(val)
        this.$emit('input', parsed)
      } catch (e) {
        // 编辑不合法 JSON 时不 emit
      }
    },
    editable(val) {
      this.editorOptions.readOnly = val ? false : 'nocursor'
    }
  },
  mounted() {
    this.editorOptions.readOnly = this.editable ? false : 'nocursor'
    this.$nextTick(() => {
      this.editor = this.$refs.myEditor.codemirror
    })
  },
  methods: {
    formatJson() {
      try {
        const parsed = JSON.parse(this.jsonText)
        this.jsonText = JSON.stringify(parsed, null, 2)
      } catch (e) {
        this.$message.error('JSON 格式错误')
      }
    },
    refresh() {
      if (this.editor) {
        this.editor.refresh()
      }
    },
    validateJson() {
      try {
        JSON.parse(this.jsonText)
        return true
      } catch (e) {
        return false
      }
    }
  }
}
</script>
<style>
.CodeMirror {
  height: 300px;
}
</style>
