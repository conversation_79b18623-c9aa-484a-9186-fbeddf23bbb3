
<template>
  <BasicLayout>
    <template #wrapper>
      <el-card class="box-card">
        <el-form ref="queryForm" :model="queryParams" :inline="true" label-width="88px">
          <el-form-item label="docid" prop="docid"><el-input
            v-model="queryParams.docid"
            placeholder="请输入id"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="origin" prop="origin"><el-input
            v-model="queryParams.origin"
            placeholder="请输入origin"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="thirdclienttype" prop="thirdclienttype"><el-input
            v-model="queryParams.thirdclienttype"
            placeholder="请输入thirdclienttype"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="eVersion" prop="engineVersion"><el-input
            v-model="queryParams.engineversion"
            placeholder="请输入engineVersion"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="日期">
            <el-date-picker
              v-model="queryParams.starttime"
              size="small"
              type="date"

              align="right"
              value-format="yyyy-MM-dd"
            /> </el-form-item>
          <el-form-item label="状态" prop="status"><el-select
            v-model="queryParams.status"
            placeholder="状态"
            clearable
            size="small"
          >
            <el-option
              v-for="dict in statusOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
          </el-form-item>

          <el-form-item label="错误详情">
            <el-checkbox
              @change="errmsgClick()"
            />
          </el-form-item>
          <el-form-item label="errmsg" prop="errmsg"><el-input
            v-model="queryParams.errmsg"
            placeholder="请输入异常信息"
            clearable
            size="small"
          />
          </el-form-item>
          <el-form-item label="错误"><el-select
            v-model="queryParams.errorstatus"
            placeholder="错误个数"
            clearable
            size="small"
          >
            <el-option
              v-for="dict in errstatusOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
          </el-form-item>
          <el-form-item label="Model"><el-select
            v-model="queryParams.exporttype"
            placeholder="转换类型"
            clearable
            size="small"
          >
            <el-option
              v-for="dict in exporttypeOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
          </el-form-item>
          <el-form-item label="文档类型" prop="filetype"><el-input
            v-model="queryParams.filetype"
            placeholder="文档类型"
            clearable
            size="small"
          />
          </el-form-item>
          <el-form-item>
            <div style="display: flex; align-items: center;">
              <el-button
                v-permisaction="['admin:cvdataRequestDocx:select']"
                type="primary"
                icon="el-icon-search"
                size="mini"
                @click="handleQuery"
              >搜索</el-button>
              <el-button v-permisaction="['admin:cvdataRequestDocx:refresh']" icon="el-icon-refresh" size="mini" style="margin-right: 10px;" @click="resetQuery">重置</el-button>
              <!-- <el-button icon="el-icon-download" size="mini" @click="handleExport">导出</el-button> -->
              <CVdataExport
                :query-params="{ ...queryParams, pageIndex: queryParams.pageIndex, pageSize: queryParams.pageSize }"
                :approver-list="approver_list"
                :approver-function="approver_function"
                :total.sync="total"
              />
            </div>
          </el-form-item>
        </el-form>

        <el-table v-loading="loading" :data="requestsList" span="11.1" :cell-style="{padding:'5'}">
          <el-table-column v-if="false" type="selection" width="1" align="left" /><el-table-column
            label="docid"
            align="center"
            prop="docid"
            width="320"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="origin"
            align="center"
            prop="origin"
            width="90"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="third_client_type"
            align="center"
            prop="thirdclienttype"
            width="130"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="日期"
            align="left"
            prop="starttime"
            width="180"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="状态"
            align="center"
            prop="status"
            width="80"
            :formatter="statusFormat"
          >
            <template slot-scope="scope">
              {{ statusFormat(scope.row) }}
            </template>
          </el-table-column><el-table-column
            label="耗时(S)"
            align="left"
            prop="duration"
            width="120"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="页数"
            align="left"
            prop="pagecount"
            width="70"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            v-if="requestsList.length!==0&&requestsList[0].models!==undefined"
            label="Model"
            align="center"
            prop="models"
            width="80"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            v-if="requestsList.length!==0&&requestsList[0].models===undefined"
            label="转换类型"
            align="center"
            prop="exporttype"
            width="80"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="错误个数"
            align="left"
            prop="errcount"
            width="80"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="文档类型"
            align="left"
            prop="filetype"
            width="80"
            :show-overflow-tooltip="true"
          />
          <!-- v-if="showErrMsgs" -->
          <el-table-column
            v-if="showErrMsgs"
            label="错误详情"
            align="center"
            prop="errmsgs"
            width="500"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">

              <div style="height:70px;overflow:auto" v-html="errmsgsFormat(scope.row)" />
            </template></el-table-column>
          <el-table-column label="详情" align="center">
            <template slot-scope="scope">
              <CVdataParameter
                :key="scope.row.docid"
                :docid="scope.row.docid"
                :starttime="queryParams.starttime"
                function="pdf2docx"
              />
            </template>
          </el-table-column>
          <el-table-column label="下载" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <CVdataOperation
                :approver-list="approver_list"
                :show-pdf="scope.row.pdfurl=='true'"
                cvmodel="requests"
                :docid="scope.row.docid"
                :dowdate="dowdate"
                :pdf-url="scope.row.pdfurl"
                :show-img="scope.row.imgurl=='true'&&scope.row.imgurl!='[]'"
                :show-docx-ks="scope.row.wordurlks!='null'&&scope.row.wordurlks!=''&&scope.row.exporttype=='docx'"
                :show-xlsx="scope.row.wordurlks!='null'&&scope.row.wordurlks!=''&&scope.row.exporttype=='xlsx'"
                :show-pptx="scope.row.wordurlks!='null'&&scope.row.wordurlks!=''&&scope.row.exporttype=='pptx'"
                :docx-ks-url="scope.row.wordurlks"
                :show-input-pdf="scope.row.url=='true'&&(scope.row.filetype=='pdf'||scope.row.filetype=='')"
                :input-pdf-url="scope.row.url"
                :show-input-zip="scope.row.filetype=='zip'"
                :zip-source-url="scope.row.url"
                :exporttype="scope.row.exporttype"
                :show-imgs="scope.row.pagecount!=0&&scope.row.pagecount!=1&&scope.row.imgurl!=''&&scope.row.imgurl!='[]'&&scope.row.imgurl!='false'"
              />
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageIndex"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>
    </template>
  </BasicLayout>
</template>

<script>
import { addRequests, delRequests, getRequests, listRequests, updateRequests } from '@/api/cvdata/requests'
import CVdataOperation from '@/components/Cdata/Operation.vue'
import CVdataExport from '@/components/Cdata/Export.vue'
import CVdataParameter from '@/components/Cdata/Parameter.vue'
export default {
  name: 'Requests',
  components: {
    CVdataOperation, CVdataExport, CVdataParameter
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      isEdit: false,
      showErrMsgs: false,
      // 类型数据字典
      typeOptions: [],
      requestsList: [],
      statusOptions: [],
      errstatusOptions: [],
      exporttypeOptions: [],
      // 关系表类型
      dowdate: undefined,
      imgUrls: [],
      // 查询参数
      queryParams: {
        pageIndex: 1,
        pageSize: 10,
        cvmodel: 'requests',
        errmsg: undefined,
        origin: undefined,
        docid: undefined,
        starttime: undefined,
        status: 'noFinished',
        engineversion: undefined,
        exporttype: undefined,
        filetype: undefined,
        models: '',
        thirdclienttype: undefined,
        isExport: false
      },
      approver_list: [],
      approver_function: 'pdf2docx',
      // 表单参数
      form: {
      },
      // 表单校验
      rules: { docid: [{ required: true, message: 'id不能为空', trigger: 'blur' }],
        starttime: [{ required: true, message: '(:s)不能为空', trigger: 'blur' }],
        status: [{ required: true, message: ', 0:, 1:, 2:不能为空', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.getList()
    this.getDicts('pdf_edit_status').then(response => {
      this.statusOptions = response.data
    })
    this.getDicts('pdf_edit_errstatus').then(response => {
      this.errstatusOptions = response.data
    })
    this.getDicts('pdf_exporttype').then(response => {
      this.exporttypeOptions = response.data
    })
    this.getDicts('approver_cvdata_list').then((response) => {
      this.approver_list = response.data
    })
  },
  methods: {
    /** 查询参数列表 */
    getList() {
      this.loading = true
      // listRequests接口传入两个参数：pageIndex 和 pageSize
      listRequests(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.requestsList = response.data.list
        this.total = response.data.count
        this.loading = false
      }
      )
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined
      }
      this.resetForm('form')
    },
    getImgList: function() {
      this.form[this.fileIndex] = this.$refs['fileChoose'].resultList[0].fullUrl
    },
    fileClose: function() {
      this.fileOpen = false
    },
    statusFormat(row) {
      return this.selectDictLabel(this.statusOptions, row.status)
    },
    errmsgsFormat(row) {
      var errsrow = row.errmsgs.replaceAll('[', '').replaceAll(']', '').replaceAll('"', '').split(',')
      var html = ''
      for (var i in errsrow) {
        console.log(errsrow[i])
        html += errsrow[i] + '<br/>'
      }
      return html
    },
    fileDownload(row, filetype) {
      return process.env.VUE_APP_BASE_API + '/api/v1/requests/download/' + filetype + '/' + row.docid + '/' + this.dowdate
      //    downloadPdfEditRequest(row).then()
    },
    errmsgClick() {
      this.showErrMsgs = !this.showErrMsgs
    },
    // 关系
    // 文件
    /** 搜索按钮操作 */
    handleQuery() {
      this.dowdate = this.queryParams.starttime
      this.queryParams.pageIndex = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.errorstatus = undefined
      this.queryParams.exporttype = undefined
      this.dowdate = undefined
      this.queryParams.starttime = undefined
      this.queryParams.engineversion = undefined
      this.queryParams.filetyp = undefined
      this.queryParams.models = ''
      this.queryParams.thirdclienttype = undefined
      this.dateRange = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加Requests'
      this.isEdit = false
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.docid)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const docid =
                row.docid || this.ids
      getRequests(docid).then(response => {
        this.form = response.data
        this.open = true
        this.title = '修改Requests'
        this.isEdit = true
      })
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.docid !== undefined) {
            updateRequests(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(response.msg)
                this.open = false
                this.getList()
              } else {
                this.msgError(response.msg)
              }
            })
          } else {
            addRequests(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(response.msg)
                this.open = false
                this.getList()
              } else {
                this.msgError(response.msg)
              }
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      var Ids = (row.docid && [row.docid]) || this.ids

      this.$confirm('是否确认删除编号为"' + Ids + '"的数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function() {
        return delRequests({ 'ids': Ids })
      }).then((response) => {
        if (response.code === 200) {
          this.msgSuccess(response.msg)
          this.open = false
          this.getList()
        } else {
          this.msgError(response.msg)
        }
      }).catch(function() {
      })
    }
  }
}
</script>
