<template>
  <div class="nested-field-table">
    <el-table
      :data="data"
      border
      row-key="_id"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      :default-expand-all="true"
    >
      <!-- key -->
      <el-table-column label="字段Key">
        <template #default="scope">
          <div class="key-cell">
            <el-input
              v-model="scope.row.key"
              size="small"
              placeholder="key"
              :disabled="!editable"
            />
          </div>
        </template>
      </el-table-column>

      <!-- value -->
      <el-table-column label="字段值">
        <template #default="scope">
          <el-tooltip
            content="字段值结合字段来源生成真正数据,多个值用逗号分隔"
            placement="top"
          >
            <el-input
              v-model="scope.row.valueStr"
              size="small"
              placeholder="value"
              :disabled="!editable || hasChildren(scope.row)"
              @input="handleValueInput(scope.row); isValueStrUpdating = true"
              @blur="isValueStrUpdating = false"
            />
          </el-tooltip>
        </template>
      </el-table-column>

      <!-- type -->
      <el-table-column label="字段类型">
        <template #default="scope">
          <el-select
            v-model="scope.row.type"
            size="small"
            placeholder="type"
            :disabled="!editable"
          >
            <el-option
              v-for="option in typeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </template>
      </el-table-column>

      <!-- form -->
      <el-table-column label="字段来源">
        <template #default="scope">
          <el-select
            v-model="scope.row.form"
            size="small"
            placeholder="form"
            :disabled="!editable || hasChildren(scope.row)"
          >
            <el-tooltip
              v-for="item in formOptions"
              :key="item.value"
              :content="item.remark"
              placement="top"
            >
              <el-option
                :label="item.label"
                :value="item.value"
              />
            </el-tooltip>
          </el-select>
        </template>
      </el-table-column>

      <!-- len -->
      <el-table-column label="列表长度">
        <template #default="scope">
          <el-input-number
            v-model="scope.row.len"
            size="small"
            :min="1"
            :disabled="!editable || scope.row.type !== 'list'"
          />
        </template>
      </el-table-column>

      <!-- sampletype -->
      <el-table-column label="样章类型">
        <template #default="scope">
          <el-tooltip
            content="限制样章类型,仅对字段来源得到样章时生效,不同类型通过逗号分隔,如: png,jpg.."
            placement="top"
          >
            <el-input
              v-model="scope.row.sampletypeStr"
              size="small"
              placeholder="sampletype"
              :disabled="!editable || hasChildren(scope.row)"
              @input="handleSampletypeInput(scope.row); isSampletypeStrUpdating = true"
              @blur="isSampletypeStrUpdating = false"
            />
          </el-tooltip>
        </template>
      </el-table-column>

      <!-- 操作 -->
      <el-table-column label="操作">
        <template #default="scope">
          <el-button
            type="text"
            size="mini"
            icon="el-icon-plus"
            :disabled="!editable"
            @click="addSibling(scope.row)"
          >新增</el-button>
          <el-button
            type="text"
            size="mini"
            icon="el-icon-arrow-down"
            :disabled="!editable"
            @click="addChild(scope.row)"
          >子字段</el-button>
          <el-button
            type="text"
            size="mini"
            icon="el-icon-delete"
            :disabled="!editable"
            @click="removeRow(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  name: 'NestedFieldTb',
  props: {
    data: {
      type: Array || null,
      required: true
    },
    editable: {
      type: Boolean,
      default: true
    },
    formOptions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      typeOptions: [
        { label: 'json', value: 'json' },
        { label: 'list', value: 'list' }
      ],
      isValueStrUpdating: false,
      isSampletypeStrUpdating: false
    }
  },
  watch: {
    data: {
      handler(newVal) {
        if (this.isValueStrUpdating || this.isSampletypeStrUpdating) {
          return
        }
        this.initProxy(newVal)
      },
      immediate: true,
      deep: true
    }
  },
  created() {
    this.initProxy(this.data)
  },
  methods: {
    hasChildren(row) {
      return row.children && row.children.length > 0
    },
    initProxy(list, level = 0, perfix = '0') {
      list.forEach((item, index) => {
        item._id = `${perfix}_${index}`
        item.valueStr = Array.isArray(item.value) ? item.value.join(',') : item.value || ''
        item.sampletypeStr = Array.isArray(item.sampletype) ? item.sampletype.join(',') : item.sampletype || ''

        if (item.children && item.children.length) {
          this.initProxy(item.children, level + 1, item._id)
        }
      })
    },
    createNewRow() {
      return {
        key: '',
        value: [],
        valueStr: '',
        type: '',
        form: '',
        len: undefined,
        sampletype: [],
        sampletypeStr: '',
        children: []
      }
    },
    addSibling(row) {
      const parent = this.findParent(this.data, row)
      const index = parent ? parent.children.indexOf(row) : this.data.indexOf(row)
      const newRow = this.createNewRow()
      if (parent) {
        parent.children.splice(index + 1, 0, newRow)
      } else {
        this.data.splice(index + 1, 0, newRow)
      }
    },
    addChild(row) {
      if (!row.children) this.$set(row, 'children', [])
      row.children.push(this.createNewRow())
    },
    removeRow(row) {
      const parent = this.findParent(this.data, row)
      const list = parent ? parent.children : this.data
      const index = list.indexOf(row)
      if (index !== -1) {
        if (list.length > 1) {
          list.splice(index, 1)
        } else {
          Object.assign(row, this.createNewRow())
        }
      }
    },
    findParent(list, targetRow, parent = null) {
      for (const row of list) {
        if (row === targetRow) return parent
        if (row.children) {
          const found = this.findParent(row.children, targetRow, row)
          if (found) return found
        }
      }
      return null
    },
    handleValueInput(row) {
      row.value = row.valueStr
        .split(',')
        .map(s => s.trim())
        .filter(s => s.length > 0)
    },
    handleSampletypeInput(row) {
      row.sampletype = row.sampletypeStr
        .split(',')
        .map(s => s.trim())
        .filter(s => s.length > 0)
    }
  }
}
</script>

<style>
/* 让 el-input 和展开箭头保持同一行 */
.nested-field-table .el-table .cell {
  display: flex;
  align-items: center;
  white-space: nowrap;
}
</style>
