import request from '@/utils/request'

import { post } from '@/api/model/http'

// export function getModelList(query) {
//     return get(
//      '/api/api/v1/convert',
//       query
//     )
//   }

export function getModelList(query) {
  return request({
    url: '/api/v1/convert',
    method: 'get',
    params: query
  })
}

// export function getObjWithSuffix(data) {
//     return post(
//       '/api/api/v1/convert/getObjWithSuffix',
//       data
//         )
//   }

export function getObjWithSuffix(data) {
  return request({
    url: 'api/v1/convert/getObjWithSuffix',
    method: 'post',
    data: data
  })
}

// export function InitiateUpload(data) {
//     return post(
//      '/api/api/v1/convert/initiateUpload',
//       data
//     )
//   }

export function InitiateUpload(data) {
  return request({
    url: 'api/v1/convert/initiateUpload',
    method: 'post',
    data: data
  })
}

export function UploadPart(data) {
  return post(
    '/api/api/v1/convert/uploadPart',
    data
  )
}
//   export function UploadPart(data) {
//     return request({
//       url: 'api/v1/convert/uploadPart',
//       method: 'post',
//       data: data
//     })
//   }

// export function CompleteUpload(data) {
//     return post(
//       '/api/api/v1/convert/completeUpload',
//     data
//     )
//   }
export function CompleteUpload(data) {
  return request({
    url: 'api/v1/convert/completeUpload',
    method: 'post',
    data: data
  })
}

// export function AbortMultipartUpload(data) {
//     return post(
//       '/api/api/v1/convert/abortMultipartUpload',
//       data
//     )
//   }

export function AbortMultipartUpload(data) {
  return request({
    url: 'api/v1/convert/abortMultipartUpload',
    method: 'post',
    data: data
  })
}

export function SubmitConvert(data) {
  return request({
    url: 'api/v1/convert',
    method: 'post',
    data: data
  })
}


export function updateConvert(data) {
  return request({
    url: 'api/v1/convert/update',
    method: 'post',
    data: data
  })
}

export function checkPath(data) {
  return request({
    url: 'api/v1/convert/checkPath',
    method: 'post',
    data: data
  })
}