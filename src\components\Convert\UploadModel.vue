<template>
  <div>
    <el-button
      type="success"
      icon="el-icon-search"
      size="mini"
      @click="approval()"
    >新建</el-button>

    <!-- 添加或修改对话框 -->
    <el-dialog
      :visible.sync="approvalOpen"
      :title="title"
      width="1000x"
      @open="handleOpen"
      :close-on-click-modal="false" 
    >
      <!-- 错误信息展示 -->
      <div v-if="submitErrorMessage" class="error-message">
        {{ submitErrorMessage }}
      </div>
      <div style="max-height: 70vh; overflow-y: auto; padding-right: 10px">
        <!-- 添加滚动容器 -->
        <el-form ref="form" :model="form" :rules="rules" label-width="100px">
          <!-- 下拉菜单组件 -->
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="归属项目" prop="project">
                <el-select
                  v-model="form.project"
                  placeholder="请选择归属项目"
                  clearable
                  size="small"
                >
                  <el-option
                    v-for="(value, index) in projectOptions"
                    :key="index"
                    :label="value"
                    :value="value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="显卡类型" prop="device">
                <el-select
                  v-model="form.device"
                  placeholder="显卡类型"
                  clearable
                  size="small"
                >
                  <el-option
                    v-for="(value, index) in deviceOptions"
                    :key="index"
                    :label="value"
                    :value="value"
                    :disabled="value === '4090' || value === 't4'"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 模型负责人 -->
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="模型负责人" prop="author">
                <div>
                  <el-input
                    v-model="form.author"
                    placeholder="请输入模型负责人"
                    type="input"
                    rows="1"
                    disabled
                  />
                </div>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="模型名称" prop="name">
                <div>
                  <el-input
                    v-model="form.name"
                    placeholder="请输入模型名称"
                    type="input"
                    rows="1"
                  />
                </div>
              </el-form-item>
            </el-col>
          </el-row>

            <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="模型版本" prop="version">
              <div>
                <el-tooltip content="【x1.x2.x3】x1随着backbone的改动自增，x2随着 输入输出层变动递增，x3为日期，每次模型更新日期一定变化,如:【0.0.2024121100】" placement="top">
                <el-input
                  v-model="form.version"
                  placeholder="格式如: x1.x2.x3"
                  type="input"
                  rows="1"
                />
                </el-tooltip>
              </div>
              </el-form-item>
            </el-col>
            </el-row>

          <!-- 模型描述 -->
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="模型描述" prop="description">
                <div>
                  <el-input
                    v-model="form.description"
                    placeholder="模型描述及说明"
                    type="textarea"
                    rows="1"
                    style="width: 208px"
                  />
                </div>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 文件上传组件表单项 -->
          <el-form-item label="上传方式"  >
            <el-radio-group v-model="choiceInput" @change="handleChoiceChange">
              <el-radio label="uploadFile">上传文件</el-radio>
              <el-radio label="inputLink">输入ks3文件链接</el-radio>
            </el-radio-group>
          </el-form-item>

          <!-- 文件上传组件表单项 -->
          <el-form-item label="上传文件" style="display: block;width: 100%;">
            <div v-if="choiceInput == 'uploadFile'">
              <el-upload
                ref="upload"
                v-loading="uploading"
                element-loading-t
                ext="文件处理中"
                class="uploader"
                list-type="text"
                accept=".onnx"
                :multiple="true"
                :show-file-list="true"
                :file-list="fileList"
                :on-remove="handleRemove"
                :on-change="handleChange"
                :auto-upload="false"
                :disabled="isUploadDisabled"
                action="placeholder"
                drag
              >
                <i class="el-icon-upload" />
                <div class="el-upload__text">
                  {{
                    isUploadDisabled
                      ? "请点击《取消上传》后再选择文件"
                      : "将文件拖到此处，并【点击上传】"
                  }}
                </div>
              </el-upload>
              <br>
              <div class="params">
                <el-button
                  size="small"
                  type="primary"
                  :disabled="!fileList.length"
                  @click="toggleUpload"
                >
                  {{
                    uploading && !paused
                      ? "暂停上传"
                      : uploadId
                        ? "继续上传"
                        : "开始上传"
                  }}</el-button>

                <el-button
                  size="small"
                  type="success"
                  :disabled="uploading"
                  @click="clearUpload"
                >清空上传列表</el-button>
                <el-button
                  size="small"
                  type="danger"
                  @click="cancelUpload"
                >取消上传</el-button>
              </div>
              <el-progress
                v-if="uploadProgress > 0"
                :percentage="uploadProgress"
                status="success"
                :disabled="!uploading"
              />
              <div v-if="uploadProgress > 0" class="progress-text">
                {{ uploadProgress }}%
              </div>
            </div>
         

          <!-- 文件链接手动输入表单项 -->
          <div v-else-if="choiceInput == 'inputLink'">
            <el-input
              v-model="manualFileLink"
              placeholder="请输入文件链接"
              style="width: 400px; margin-right: 10px"
            />
            <el-button
              size="small"
              type="primary"
              @click="validateFileLink"
            >验证链接</el-button>
          </div>
          </el-form-item>

          <!-- 已上传文件列表 -->
          <div class="uploaded-files-section">
            <div v-if="choiceInput === 'uploadFile'">
              <span class="section-title">已上传onnx文件列表：</span>
              <el-table
                v-show="showResult"
                :data="resultList"
                border
                style="margin-top: 10px; width: 100%"
                :header-cell-style="{
                  backgroundColor: '#f5f7fa',
                  color: '#333',
                  fontWeight: 'bold',
                }"
                :row-class-name="tableRowClassName"
              >
                <el-table-column
                  prop="count"
                  label="#"
                  width="50"
                  align="center"
                />

                <el-table-column prop="name" label="文件名" width="200" />
                <el-table-column
                  prop="value"
                  label="objectKey / 错误信息"
                  align="left"
                />
              </el-table>
            </div>

            <span
              class="section-title"
              style="margin-top: 10px"
            >成功上传与表单待提交文件：</span>
            <div
              style="
                max-height: 50px;
                overflow-x: auto;
                padding: 10px;
                white-space: nowrap;
                border: 1px solid #ebeef5;
                border-radius: 4px;
                background-color: #fafafa;
                display: flex;
                align-items: center;
              "
            >
              <span
                v-if="form.model_ks3"
                style="display: inline-block; word-break: break-all"
              >
                {{ formattedModelKS3 }}
              </span>
              <span
                v-else
                style="color: #999; font-style: italic; margin: 0 auto"
              >暂无数据</span>
            </div>

            <!-- 不支持文件类型 -->
            <span
              v-show="showFailedList"
              class="section-title"
              style="margin-top: 20px"
            >文件类型不支持上传的文件：</span>
            <el-table
              v-show="showFailedList"
              :data="failedList"
              border
              style="margin-top: 10px; width: 100%"
              :header-cell-style="{
                backgroundColor: '#f5f7fa',
                color: '#333',
                fontWeight: 'bold',
              }"
              :row-class-name="tableRowClassName"
            >
              <el-table-column prop="name" label="文件名" width="250" />
              <el-table-column prop="reason" label="原因" width="400" />
            </el-table>
          </div>

          <div>自定义参数
            <span class="custom-hint">【参数无需括号，使用英文逗号分隔参数】</span>
          </div>

          <div>
            <el-form-item label="动态尺寸">
              <el-button
                type="primary"
                @click="addDynamicConfig"
              >添加动态配置</el-button>
            </el-form-item>

            <!-- 动态配置组列表 -->
            <div
              v-for="(config, index) in form.parameters.dynamic_configs"
              :key="index"
              style="margin-bottom: 20px"
            >
              <el-form-item :label="'配置 ' + (index + 1)">
                <el-container style="display: flex; align-items: center">
                  <el-tooltip content="【不要】填写OUTPUT【输出参数】" placement="top">
                  <el-input
                    v-model="config.name"
                    size="small"
                    placeholder="指定层名称"
                    style="width: 120px; margin-right: 10px"
                  />
                  </el-tooltip>
                  <el-tooltip content="输入【无需】使用 ()[] 等括号" placement="top">
                  <el-input
                    v-model="config.min"
                    size="small"
                    placeholder="最小形状,格式: 1,3,640,640"
                    style="width: 200px; margin-right: 10px"
                  />
                  </el-tooltip>
                  <el-tooltip content="输入【无需】使用 ()[] 等括号" placement="top">
                  <el-input
                    v-model="config.opt"
                    size="small"
                    placeholder="理想形状,格式: 1,3,640,640"
                    style="width: 200px; margin-right: 10px"
                  />
                  </el-tooltip>
                  <el-tooltip content="输入【无需】使用 ()[] 等括号" placement="top">
                  <el-input
                    v-model="config.max"
                    size="small"
                    placeholder="最大形状,格式: 1,3,640,640"
                    style="width: 200px; margin-right: 10px"
                  />
                  </el-tooltip>
                  <el-button
                    type="danger"
                    icon="el-icon-delete"
                    @click="removeDynamicConfig(index)"
                  />
                </el-container>
              </el-form-item>
            </div>
          </div>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          :loading="submitLoading"
          @click="submitForm"
        >确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getUserProfile } from '@/api/admin/sys-user'
import axios from 'axios'

import { formatfileSize } from '@/api/cv/utils'
import { getHeaders } from '@/utils/get-header'
import { getToken } from '@/utils/auth'
import SparkMD5 from 'spark-md5'
import {
  InitiateUpload,
  CompleteUpload,
  AbortMultipartUpload,
  checkPath
} from '@/api/convert/convert'

export default {
  name: 'ModelOperation',
  components: {},
  props: {
    dialog: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      VUE_APP_BASE_API: process.env.VUE_APP_BASE_API,
      projectOptions: ['scan', 'edit', 'zhumo', 'docqa'],
      deviceOptions: ['4090', 't4','4090-2411','t4-2411'],

      // 是否开启审批表单
      approvalOpen: false,
      // 标题
      title: '上传待转换的模型',
      // 表单参数
      form: {
        name: '',
        version: '',
        project: '',
        author: '',
        description: '',

        model_ks3: '',
        md5: '',
        parameters: {
          dynamic_configs: []
        },
      },

      // 表单校验
      rules: {
        project: [
          { required: true, message: '项目名称不能为空', trigger: 'blur' }
        ],
        device: [
          { required: true, message: '显卡类型不能为空', trigger: 'blur' }
        ],

        author: [
          { required: true, message: '模型负责人不能为空', trigger: 'blur' }
        ],
        name: [
          { required: true, message: '模型名称不能为空', trigger: 'blur' },
          { 
            validator: this.checkChinese, 
            message: '模型名称不能包含中文字符/空格', 
            trigger: 'blur' 
          }
          
        ],
        version: [
          { required: true, message: '模型版本不能为空', trigger: 'blur' },
          {
            validator: this.checkVersionFormat,
            trigger: 'blur'
          }
        ]
        
      },
      approver_list: [],
      // 文件类型
      fileType: '',
      // 正在加载
      uploading: false,
      submitLoading: false,
      choiceInput: 'uploadFile',
      // 上传列表
      fileList: [],
      resultList: [],
      // 存储不支持上传的文件
      failedList: [],
      showFailedList: false,
      showResult: true,
      collapseActiveNamesAll: ['1'],
      uploadId: '',
      chunkSize: 5 * 1024 * 1024, // 5 MB per chunk
      uploadedChunks: [],
      uploadProgress: 0,
      cancelTokens: [],
      paused: false, // 是否处于暂停状态
      objectKey: '',
      parts: [],
      submitErrorMessage: '', // 错误信息存储变量
      choiceInput: 'uploadFile', // 'uploadFile' 或 'inputLink'
      manualFileLink: '', // 手动输入的文件链接
  
    }
  },
  computed: {
    formattedModelKS3() {
      return this.cleanFileUrl(this.form.model_ks3)
    },

    formattedDuration() {
      return this.statistic.duration.toFixed(3) + 's'
    },
    fileName() {
      const { name } = this.fileObj
      return name.trim()
    },
    fileSize() {
      const { size } = this.fileObj
      return formatfileSize(size)
    },
    parameters() {
      return {
        parameters: {
          dynamic_configs: this.form.dynamic_configs.map((config) => ({
            name: config.name,
            min: Array.isArray(config.min)
              ? config.min
              : config.min.split(',').map(Number), // 转为数组
            opt: Array.isArray(config.opt)
              ? config.opt
              : config.opt.split(',').map(Number), // 转为数组
            max: Array.isArray(config.max)
              ? config.max
              : config.max.split(',').map(Number) // 转为数组
          }))
        }
      }
    },

    isUploadDisabled() {
      return this.uploading || this.parts.length > 0 || this.uploadId !== ''
    }
  },

  created() {
    this.getDicts('approver_cvdata_list').then((response) => {
      this.approver_list = response.data
    })
  },
  methods: {
    cleanFileUrl(fileUrl) {

      // 使用正则表达式去掉 http://xxx.com 部分
      
      fileUrl = fileUrl.replace(/^https?:\/\/[^\/]+\.com\//, '');

      fileUrl = fileUrl.split('?')[0]
      return fileUrl
    },
    getUserEmail() {
      getUserProfile().then((response) => {
        this.form.author = response.data.user.email
      })
    },

    // 开启上传表单
    async approval() {
      this.approvalOpen = true
    },

    // 取消按钮
    cancel() {
      this.approvalOpen = false
      this.reset()
    },
    handleOpen() {
      this.reset()
      this.resetResult()
      this.getUserEmail()
    },
    // 表单重置
    reset() {
      this.uploading = false
      this.fileList = []
      this.form = {
        name: '',
        version: '',
        author: '',
        description: '',
        model_ks3: '',
        md5: '',
        parameters: {
          dynamic_configs: []
        }
      }
      this.submitErrorMessage = ''
      this.manualFileLink = ''
      this.resetForm('form')
    },

    handleChange(file, fileList) {
      // 只允许上传一个文件，如果文件列表超过1个，就删除前面的
      if (fileList.length > 1) {
        fileList.splice(0, fileList.length - 1) // 保留最新的一个文件
      }
      // this.fileList = fileList;  // 更新文件列表
      this.fileList = fileList.map((f) => f.raw || f)
    },

    handleRemove(file, fileList) {
      this.fileList = fileList
    },
    resetResult() {
      this.resultList = []
      this.failedList = []
    },

    isOnnxFileType(file) {
      // 如果文件的 MIME 类型为空并且文件名以 .onnx 结尾，返回 true
      if (!file.type && file.name.endsWith('.onnx')) {
        return true
      }
      return false
    },
    message(type, msg, dataMsg) {
      this.$message({
        showClose: true,
        message: msg + dataMsg,
        center: true,
        duration: 4000,
        type: type
      })
    },
    clearUpload() {
      this.$refs.upload.clearFiles()
      this.fileList = []
      this.resultList = []
      this.uploadId = ''
      this.objectKey = ''
      this.md5 = ''
    },
    /** 提交按钮 */
    async submitForm() {
      // console.log("Before submission:", JSON.stringify(this.form));
      this.submitLoading = true
      try {
        // 验证表单是否有效
        const isValid = await new Promise((resolve) => {
          this.$refs['form'].validate((valid) => {
            this.submitLoading = false
            resolve(valid)
          })
        })

        if (isValid) {
          //表单必须提交文件ks3路径才能提交
            if (!this.form.model_ks3) {  
              this.$message.error('请上传文件');
              return; 
          }  
          // 遍历 dynamic_configs 数组，检查每一个配置项的字段
          for (
            let i = 0;
            i < this.form.parameters.dynamic_configs.length;
            i++
          ) {
            const config = this.form.parameters.dynamic_configs[i]

            if (!config.name || !config.min || !config.opt || !config.max) {
              // 如果 config 的任何一个字段为空，弹出提示
              this.$message({
                type: 'error',
                message: `动态配置项 ${
                  i + 1
                } 中有未填写的字段，请检查 "name"、"min"、"opt" 和 "max" 是否都已填写。或者删除配置项`
              })
              this.submitLoading = false
              return // 停止提交，提醒用户补充填写
            }

            // 检查是否包含括号字符
            const bracketPattern = /[()\[\]{}]/;
            if (
              bracketPattern.test(config.name) ||
              bracketPattern.test(config.min) ||
              bracketPattern.test(config.opt) ||
              bracketPattern.test(config.max)
            ) {
              this.$message({
                type: 'error',
                message: `动态配置项 ${i + 1} 中包含括号字符，请检查 "name"、"min"、"opt" 和 "max" 是否包含 ()[]{} 等括号字符。`
              });
              this.submitLoading = false;
              return; // 停止提交，提醒用户删除括号字符
            }
          }

        
          // 创建新的表单对象，复制原始表单中的值
          const formCopy = {
            name: this.form.name,
            version: this.form.version,
            project: this.form.project,
            author: this.form.author,
            description: this.form.description,
            device: this.form.device,

            model_ks3: this.form.model_ks3,
            md5: this.form.md5
          }

          // 动态添加 parameters 参数
          if (this.form.parameters.dynamic_configs.length > 0) {
            formCopy.parameters = {
              dynamic_configs: this.form.parameters.dynamic_configs.map(
                (config) => ({
                  name: config.name ? config.name : null,
                  min: config.min ? config.min.split(',').map(Number) : '',
                  opt: config.opt ? config.opt.split(',').map(Number) : '',
                  max: config.max ? config.max.split(',').map(Number) : ''
                })
              )
            }
          }

          try {
            const response = await axios.post(
              this.VUE_APP_BASE_API + '/api/v1/convert',
              formCopy,
              {
                headers: {
                  ...getHeaders(),
                  Authorization: 'Bearer ' + getToken()
                }
              }
            )
            const res = response.data
            if (res.code !== 200) {
              this.submitErrorMessage = res.msg
              console.log('请求失败:', res.msg)
              this.$message({
                message: res.msg,
                type: 'error', // 设置为 error 类型
                duration: 3000 // 提示持续 3 秒
              })

              this.submitLoading = false
            } else {
              // 请求成功时显示提示框

              this.$message({
                message: res.msg,
                type: 'success',
                duration: 3000 // 提示持续 3 秒
              })
              this.resetResult()
              this.approvalOpen = false
              this.submitLoading = false

              this.reset()

              // 表单提交成功后,刷新模型列表
              this.$emit('formSubmitted');
            }
          } catch (error) {
            console.log('提交表单异常：', error)
            // console.error("An error occurred:", error.message); // 打印错误信息
            this.submitLoading = false
          }

          // 模拟等待 5 秒以显示加载状态
          await new Promise((resolve) => setTimeout(resolve, 5000))
        }
        else{
          //表单不通过rules规则给出提示
          this.$message.error('请填完善必要的的表单信息');  
        }
      } finally {
        // this.submitLoading = false;
      }
    },

    // 切换上传和暂停
    toggleUpload() {
      if (this.uploading && !this.paused) {
        this.pauseUpload() // 暂停上传
      } else {
        this.submitUpload() // 继续上传
      }
    },

    pauseUpload() {
      this.paused = true
      this.uploading = false
      this.cancelTokens.forEach((token) => token.cancel('用户暂停上传'))
      this.cancelTokens = []
    },

    async submitUpload() {
      this.uploading = true
      this.paused = false
      this.uploadProgress = 0
      var counts = 0
      const totalCount = this.fileList.length
      for (const file of this.fileList) {
        try {
          let objectKey = this.objectKey || '' // 使用之前保存的objectKey
          let uploadId = this.uploadId || '' // 使用之前保存的uploadId

          if (!uploadId) {
            const md5 = await this.calculateFileMD5(file)
            this.md5 = md5
            // const responseData = await this.initiateUpload(file, md5);
            const responseData = await InitiateUpload({
              fileName: file.name,
              md5: md5
            })
            if (responseData.code !== 200) {
              throw new Error(`Initialization failed: ${responseData.msg}`)
            }
            uploadId = responseData.data.uploadId
            objectKey = responseData.data.objectKey
            this.uploadId = uploadId // 保存uploadId
            this.objectKey = objectKey // 保存objectKey
            this.parts = [] // 初始化parts数组
          }

          const chunkSize = 2 * 1024 * 1024 // 5 MB chunks

          const totalChunks = Math.ceil(file.size / chunkSize)

          const startPartNumber = this.parts ? this.parts.length + 1 : 1 // 从最后上传的块继续

          // Step 2: Upload each chunk with its part number
          for (
            let partNumber = startPartNumber;
            partNumber <= totalChunks;
            partNumber++
          ) {
            if (this.paused) break // 暂停上传
            const start = (partNumber - 1) * chunkSize
            const end = Math.min(start + chunkSize, file.size)

            const chunk = file.slice(start, end)
            const cancelToken = axios.CancelToken.source()
            this.cancelTokens.push(cancelToken)

            const responseData = await this.uploadPart(
              uploadId,
              objectKey,
              partNumber,
              chunk,
              cancelToken.token
            )
            if (responseData.code !== 200) {
              throw new Error('UploadPart failed:' + responseData.msg)
            }
            const etag = responseData.data
            this.parts.push({ partNumber: partNumber, eTag: etag })

            // Update progress for each part upload
            this.uploadProgress = Math.round((partNumber / totalChunks) * 100)
          }

          // Step 3: Complete the upload with all the parts information

          // const uploadResult = await this.completeUpload(uploadId, objectKey, this.parts);
          const uploadResult = await CompleteUpload({
            uploadId: uploadId,
            filename: objectKey,
            parts: this.parts
          })

          if (uploadResult.code === 200) {
            this.$message.success({
              message: 'File upload complete',
              duration: 1000 // 1秒后自动关闭
            })

            let url = uploadResult.data
            this.form.model_ks3 = url

            url = this.cleanFileUrl(url)

            counts += 1
            this.resultList.push({
              name: file.name,
              value: url,

              count: counts + '/' + totalCount
            })
            // this.showResult = true

            this.form.md5 = this.md5

            this.parts = []
            this.objectKey = ''
            this.uploadId = ''
            this.uploadProgress = 0
          } else {
            throw new Error('CompleteUpload failed:' + uploadResult.msg)
          }
        } catch (error) {
          if (axios.isCancel(error) && this.paused) {
            console.log('暂停上传...')
          } else if (axios.isCancel(error)) {
            // 取消上传
            this.uploadId = ''
            this.objectKey = ''
            this.md5 = ''
            console.log(' 取消上传...')

            this.uploadProgress = 0
          } else {
            //  有异常bug

            console.log('Error uploading file:', error.message)
            this.$message({
              message: 'File upload failed: ' + error,
              type: 'error',
              duration: 3000 // 持续显示，用户需手动关闭
            })
            this.uploadId = ''
            this.objectKey = ''
            this.md5 = ''
            this.uploading = false
            this.uploadProgress = 0
            this.resultList.push({
              name: file.name,
              value: '异常信息：' + error.message,
              count: counts + '/' + totalCount
            })
          }

          break
        } finally {
          this.uploading = false
          this.cancelTokens = []
          // this.parts=[]
        }
      }
    },

    tableRowClassName({ rowIndex }) {
      return rowIndex % 2 === 0 ? 'table-row-light' : 'table-row-dark'
    },
    addDynamicConfig() {
      this.form.parameters.dynamic_configs.push({
        name: '',
        min: '',
        opt: '',
        max: ''
      })
    },
    removeDynamicConfig(index) {
      this.form.parameters.dynamic_configs.splice(index, 1)
    },
    async initiateUpload(file, md5) {
      const response = await InitiateUpload({ fileName: file.name, md5: md5 })
      return response
    },

    async uploadPart(uploadId, fileName, partNumber, fileChunk, cancelToken) {
      const formData = new FormData()
      formData.append('uploadId', uploadId)
      formData.append('partNumber', partNumber)
      formData.append('filename', fileName)
      formData.append('file', fileChunk)

      var reqUrl = this.VUE_APP_BASE_API + 'api/v1/convert/uploadPart'
      const response = await axios.post(reqUrl, formData, {
        headers: { ...getHeaders(), Authorization: 'Bearer ' + getToken() },
        cancelToken
      })

      return response.data
    },

    // Step 3: Complete the upload

    async cancelUpload() {
      this.uploading = false
      this.uploadProgress = 0
      // Cancel all active requests for file parts
      this.cancelTokens.forEach((token) => token.cancel('用户取消上传'))
      this.cancelTokens = []
      this.parts = [] // 重置已上传的分块信息

      // Abort the multipart upload on the backend
      if (this.uploadId) {
        try {
          const response = await AbortMultipartUpload({
            url: this.objectKey,
            uploadId: this.uploadId
          })
          if (response.code !== 200) {
            throw new Error(response.msg)
          } else {
            this.$message.warning('已成功取消上传!')
          }
        } catch (error) {
          console.error('Error aborting upload:', error)
          this.$message.error('取消上传失败，请重试。');
        } finally {
          // Reset the upload ID after attempting to abort
          this.uploadId = ''
        }
      } else {
        this.$message.warning('没有上传会话可取消。');
      }
    },

    async calculateFileMD5(file) {
      return new Promise((resolve, reject) => {
        const chunkSize = 64 * 1024 * 1024 // 每次读取2MB
        const chunks = Math.ceil(file.size / chunkSize)
        const spark = new SparkMD5.ArrayBuffer()
        const fileReader = new FileReader()
        let currentChunk = 0

        fileReader.onload = function(e) {
          spark.append(e.target.result) // 向 MD5 计算器追加文件数据
          currentChunk++

          if (currentChunk < chunks) {
            loadNext()
          } else {
            resolve(spark.end()) // 完成后得到 MD5 值
          }
        }

        fileReader.onerror = function() {
          reject('MD5 计算失败')
        }

        function loadNext() {
          const start = currentChunk * chunkSize
          const end = Math.min(start + chunkSize, file.size)
          fileReader.readAsArrayBuffer(file.slice(start, end))
        }

        loadNext()
      })
    },
    // 自定义验证方法 模型名称不能输入中文
    checkChinese(rule, value, callback) {
      const chineseRegex = /[\u4e00-\u9fa5\s（）]/;  // 匹配中文字符 空格 中文括号（）
      if (chineseRegex.test(value)) {
        callback(new Error('模型名称不能包含中文字符/空格'));  // 中文时提示错误
      } else {
        callback();  // 不包含中文时通过验证
      }
    },
    // 自定义验证方法 模型版本必须为【x1.x2.x3】格式
    checkVersionFormat(rule, value, callback) {
      // 定义x1.x2.x3格式的正则表达式，x1, x2, x3均为数字，必须有两个点号分隔
      const versionRegex = /^\d+\.\d+\.\d+$/;
      if (!value) {
        callback(new Error('模型版本不能为空'));
      } else if (!versionRegex.test(value)) {
        callback(new Error('模型版本必须为【x1.x2.x3】格式'));
      } else {
        // 可进一步扩展逻辑：检查 x1, x2, x3 是否符合递增规则
        callback();
      }
    },

    //将 https://xxx.com/abcd.onnx 分割出 https://xxx.com/ 和/abcd.onnx
    splitUrl(url) {
      const splitIndex = url.indexOf('.com') ; 

      if (splitIndex !== -1) {
        const prefix = url.substring(0, splitIndex + 4);   // 找到第一个 .com 的位置并加上 .com 的长度
        return prefix
      }else {
        // 如果不是https://xxx.com/abcd.onnx，而是tmp/model/xxx 的链接没有com字符，就返回 http://zhai-datas.ks3-cn-beijing-internal.ksyun.com
        return "http://zhai-datas.ks3-cn-beijing-internal.ksyun.com"
      }

    },

    // 解码 URL 字符串 多次解码得到%20【空格】等URL编码字符
    decodeMultipleTimes(encodedStr) {
      let decodedStr = encodedStr;
      let maxIterations = 3;  // 设置最大解码次数
      let iterations = 0;
      try {
        while (iterations < maxIterations) {
          const newDecodedStr = decodeURIComponent(decodedStr);
          if (newDecodedStr === decodedStr) {
            break;
          }
          decodedStr = newDecodedStr;
          iterations++;
        }
      } catch (e) {
        console.error('Error decoding URL:', e);
      }
      return decodedStr;
    },
    // 验证文件链接
    async validateFileLink() {
      try {
        var cleanUrl = this.cleanFileUrl(this.manualFileLink)
        //允许链接中含有URL编码后的中文字符，这里将URL字符解码成原中文
        const decodedUrl = this.decodeMultipleTimes(cleanUrl);

        const response = await checkPath({url:decodedUrl})

        if (response.code !== 200) {
          this.$message({
            message: res.msg,
            type: 'error',
            duration: 3000
          });
        } else {

            if (response.data.exist) {
                //得到https://xxx.ks3-cn-beijing.ksyuncs.com/部分
                let prefix = this.splitUrl(this.manualFileLink)
                //拼接成完整的文件链接
                let model_ks3 = prefix.replace(/-internal/g, '') + "/" + decodedUrl
                this.form.model_ks3 = model_ks3
                this.form.md5 = response.data.md5

                this.$message({
                    message: '文件链接有效',
                    type: 'success',
                    duration: 3000
                  })
                this.submitErrorMessage = ''
            }else{
              this.$message({
                message: '文件链接无效',
                type: 'error',
                duration: 3000
              })};
        }
      } catch (error) {
        console.log('验证文件链接异常：', error);
        this.$message({
          message: '验证文件链接失败',
          type: 'error',
          duration: 3000
        });
      }
    },
    handleChoiceChange(value) {
      // 切换手动输入文件链接时，清空已上传的文件
      if (value === 'inputLink' && this.uploadId) {
          this.cancelUpload();
      }
      this.form.model_ks3 = '';
      this.form.md5 = '';
      this.submitErrorMessage = ''
    },
    
    
  }
}
</script>

<style scoped>
.uploaded-files-section {
  width: 100%;
  margin-top: 20px;
}

.params-container {
  padding: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 5px;
}

.section-title {
  color: blue;
  font-weight: bold;
  font-size: 16px;
}

.table-row-light {
  background-color: #f9f9f9;
}

.table-row-dark {
  background-color: #ffffff;
}
.trt_dynamic_input {
  margin-right: 3px;
}

.error-message {
  color: red;
  margin-bottom: 10px;
  text-align: center;
  font-size: 18px; /* 字号 */
  font-weight: bold; /* 加粗 */
}

/* 将验证提示定位到右侧 */
::v-deep .el-form-item.is-error .el-form-item__error {
  position: absolute;
  top: 50%; /* 垂直居中对齐 */
  left: 100%; /* 定位在输入框的右侧 */
  transform: translateY(-50%); /* 使提示垂直居中 */
  white-space: nowrap; /* 防止换行 */
  color: #f56c6c; /* 错误提示的颜色 */
  margin-left: 10px; /* 根据需要调整右侧提示的间距 */
  font-size: 14px; /* 调整字体大小，可以根据需要增大 */
}

/* 将验证提示定位到右侧：确保 form-item 设置了 relative 以便绝对定位生效 */
.el-form-item {
  position: relative;
}
.custom-hint {
    color: rgb(255, 0, 0);
    font-size: 12px;
    margin-left: 5px;
  }
</style>
