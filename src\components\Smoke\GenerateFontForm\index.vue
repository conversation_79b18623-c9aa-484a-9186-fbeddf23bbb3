<template>
  <div v-if="isPartial">
    <el-form ref="partialForm" v-model="form" label-width="120px">
      <el-form-item v-if="form.page_parse_form=='ks3_objkey'" label="页面识别结果" prop="page_parse">
        <el-tooltip :content="page_parse_tip" effect="dark" placement="top">
          <el-input v-model="form.page_parse" placeholder="请输入ks3目录" size="small" :disabled="!editable" />
        </el-tooltip>
      </el-form-item>
      <el-form-item v-else label="页面识别结果" prop="page_parse_task">
        <el-tooltip :content="page_parse_tip" effect="dark" placement="top">
          <el-select v-model="form.page_parse_task" placeholder="请选择局部编辑任务" size="small" :disabled="!editable">
            <el-tooltip
              v-for="item in task"
              :key="item.id"
              :content="`任务状态:`+convertStatus(item.status)+` `+item.remark"
              placement="top"
            >
              <el-option
                :label="item.name"
                :value="item.id"
              />
            </el-tooltip>
          </el-select>
        </el-tooltip>
      </el-form-item>
      <el-form-item label="获取方式" prop="page_parse">
        <el-tooltip :content="page_parse_tip" effect="dark" placement="top" :disale="!editable">
          <el-select v-model="form.page_parse_form" placeholder="请选择获取方式" size="small" @change="handleFormChange">
            <el-tooltip
              v-for="item in PageParseOptions"
              :key="item.value"
              :content="item.remark"
              placement="top"
            >
              <el-option
                :label="item.label"
                :value="item.value"
              />
            </el-tooltip>
          </el-select>
        </el-tooltip>
      </el-form-item>
    </el-form>
  </div>
  <div v-else>
    <el-form ref="vetorForm" v-model="form" label-width="120px">
      <el-form-item label="字体图" prop="image_texts">
        <el-tooltip :content="image_texts_tip" effect="dark" placement="top">
          <el-input v-model="form.image_texts" placeholder="请输入字体图" size="small" :disabled="!editable" />
        </el-tooltip>
      </el-form-item>
      <el-form-item label="字典" prop="dict">
        <el-tooltip :content="dict_tip" effect="dark" placement="top">
          <el-input v-model="form.dict" placeholder="请输入ks3目录" size="small" :disabled="!editable" />
        </el-tooltip>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>

import axios from 'axios'
export default {
  name: 'GenerateFontForm',
  props: {
    value: {
      type: Array,
      default: () => ([])
    },
    uri: {
      type: String,
      default: ''
    },
    editable: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      form: {
        dict: '',
        image_texts: '',
        page_parse: '',
        page_parse_task: '',
        page_parse_form: 'page_parse_task'
      },
      task: [],
      statusOptions: [],
      PageParseOptions: [
        { label: 'ks3目录', value: 'ks3_objkey', remark: '提供一个ks3目录前缀,下面包含多个子目录(可多层嵌套),每个包含query.json和status.json文件的目录作为一个输入' },
        { label: '冒烟任务', value: 'page_parse_task', remark: '以某次局部编辑页面识别冒烟任务的结果作为输入,可选择未完成任务,如果到了执行时间识别任务未完成会自动延迟当前任务执行' }
      ],
      image_texts_tip: '提供一个ks3路径,下面包含多个子目录(可多层嵌套),最后一层目录命名：{字体名}+{字体族}，如:AOTTQQ+*KSNSHMEYHT00,目录中包含多个单字图片文件,每个图片名即图片内容定义的文字，如：汉字“中”的图片文件名为“中.png”',
      dict_tip: '提供一个ks3 objkey,对应一个文本文件,每行一个字,在进行字体生成时会随机在这些字中选择,如果没有提供,则使用默认的dict.txt',
      page_parse_tip: '局部编辑页面识别结果，转存ks3目录和冒烟任务结果两种获取方式'
    }
  },
  computed: {
    isPartial() {
      return this.uri.includes('/partial')
    }
  },
  watch: {
    uri: {
      handler(newVal) {
        if (newVal.includes('/partial')) {
          this.getTaskOptions(newVal)
        }
      }
    },
    data: {
      handler(newVal) {
        this.showDynamicData()
      },
      immediate: true
    }
  },
  created() {
    this.getDicts('pdf_smoke_status').then((response) => {
      this.statusOptions = response.data
    })
  },
  methods: {
    convertStatus(status) {
      return this.selectDictLabel(this.statusOptions, status)
    },
    handleSubmit() {
      const isValid = this.validate()
      if (isValid) {
        const data = this.createDynamicData()
        this.$emit('input', data)
        return true
      }
      return false
    },
    getTaskOptions(uri) {
      const reqData = {
        uri: '/partial',
        status: [0, 1, 2, 3, 4, 5]
      }
      axios.post('/data-processing/smoke/tasks/search', reqData).then((res) => {
        if (res.data.code === 200) {
          this.task = res.data.data.tasks
        } else {
          this.$message.error(res.data.msg)
        }
      }).catch((err) => {
        this.msgError('获取任务列表失败' + err.response.data.msg)
      })
    },
    handleFormChange(val) {
      if (val === 'ks3_objkey') {
        this.form.page_parse_task = ''
      } else if (val === 'page_parse_task') {
        this.form.page_parse = ''
        this.getTaskOptions(this.uri)
      }
    },
    createDynamicData() {
      if (this.uri.includes('/partial')) {
        if (this.form.page_parse_form === 'ks3_objkey') {
          const data = {
            key: 'page_parse',
            form: this.form.page_parse_form,
            value: [this.form.page_parse]
          }
          return [data]
        } else if (this.form.page_parse_form === 'page_parse_task') {
          const data = {
            key: 'page_parse',
            form: this.form.page_parse_form,
            value: [String(this.form.page_parse_task)]
          }
          return [data]
        }
      } else {
        return [
          {
            key: 'image_texts',
            form: 'ks3_objkey',
            value: [this.form.image_texts]
          },
          {
            key: 'dict',
            form: 'ks3_objkey',
            value: [this.form.dict]
          }
        ]
      }
    },
    validate() {
      if (this.uri.includes('/partial')) {
        if (!this.form.page_parse_form) {
          this.$message.error('请选择获取方式')
          return false
        }
        if (!this.form.page_parse && this.form.page_parse_form === 'ks3_objkey') {
          this.$message.error('请输入ks3目录')
          return false
        }
        if (!this.form.page_parse_task && this.form.page_parse_form === 'page_parse_task') {
          this.$message.error('请选择局部编辑任务')
          return false
        }
      } else {
        if (!this.form.image_texts) {
          this.$message.error('请输入字体图')
          return false
        }
        if (!this.form.dict) {
          this.$message.error('请输入字典')
          return false
        }
      }
      return true
    },
    showDynamicData() {
      if (this.value.length === 0) {
        this.reset()
        return
      }
      const form = {}
      for (const item of this.value) {
        if (this.uri.includes('/partial')) {
          if (item.key === 'page_parse') {
            if (item.form === 'ks3_objkey') {
              form.page_parse = item.value[0]
            } else if (item.form === 'page_parse_task') {
              form.page_parse_task = item.value[0]
            }
            form.page_parse_form = item.form
          }
        } else {
          if (item.key === 'image_texts') {
            form.image_texts = item.value[0]
          } else if (item.key === 'dict') {
            form.dict = item.value[0]
          }
        }
      }
      this.form = form
    },
    reset() {
      this.form = {
        dict: '',
        image_texts: '',
        page_parse: '',
        page_parse_task: '',
        page_parse_form: 'page_parse_task'
      }
    }
  }
}
</script>
