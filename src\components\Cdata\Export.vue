<template>
  <div>
    <div>
      <el-button v-permisaction="['cvdata:All:export']" icon="el-icon-download" size="mini" @click="handleExport">导出</el-button>
    </div>
    <div>
      <!-- 审批表单 -->
      <el-dialog :visible.sync="approvalOpen" :title="title" width="500px">
        <el-form ref="form" :model="form" :rules="rules" label-width="120px">
          <el-form-item label="申请目的" prop="purposes" style="margin-bottom: 2px;">
            <el-input v-model="form.purposes" placeholder="默认申请目的为：导出符合查询条件的记录" type="textarea" rows="2" />
          </el-form-item>
          <el-form-item label="审批人" prop="approver_id">
            <el-radio v-for="option in approverList" :key="option.value" v-model="form.approver_id" :label="option.value">
              {{ option.label }}
            </el-radio>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="form.remark" placeholder="填写需要补充的信息" type="textarea" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" :loading="loading" @click="submitFormApproval">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog>

      <el-dialog :visible.sync="warnOpen" title="提示" width="200px">
        <span>是否导出全部数据？</span>
        <span slot="footer" class="dialog-footer">
          <el-button @click="exportData">是</el-button>
          <el-button @click="warnOpen = false">否</el-button>
        </span>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { string } from 'clipboard'
import { postCvApproval } from '@/api/layout'
import { listRequests } from '@/api/cvdata/requests'
import { getScanRequestList } from '@/api/scan/scan-request'
export default {
  name: 'CVdataExport',
  components: {
  },
  props: {
    queryParams: {
      type: Object,
      required: true
    },
    total: {
      type: Number,
      required: true
    },
    approverList: {
      type: Array,
      required: true
    },
    approverFunction: {
      type: string,
      required: true
    }
  },
  data() {
    return {
      exportId: 0,
      approvalOpen: false,
      // 遮罩层
      loading: false,
      // 弹出层标题
      title: '',
      // total: 0,
      // 表单参数
      form: {
      },
      // 表单校验
      rules: {
        approver_id: [{ required: true, message: '选择一个审批人', trigger: 'blur' }]
      },
      warnOpen: false
    }
  },
  created() {
    // this.getDicts('approver_cvdata_list').then((response) => {
    //   console.log(response.data)
    //   this.approver_list = response.data
    // })
  },
  methods: {
    /** */
    isAllExport(queryParams) {
      if ((queryParams.models === undefined || queryParams.models === '') && queryParams.origin === undefined && queryParams.thirdclienttype === undefined) {
        return true
      } else {
        return false
      }
    },
    /** 导出按钮操作 */
    async handleExport() {
      const isAll = this.isAllExport(this.queryParams)
      if (isAll) {
        this.warnOpen = true
      } else {
        await this.exportData()
      }
    },
    async exportData() {
      // 关闭提示
      this.warnOpen = false
      // 分页查询，设置导出参数
      this.queryParams.isExport = true
      // 确定全导出
      this.queryParams.pageIndex = 0
      this.queryParams.pageSize = 0
      switch (this.approverFunction) {
        case 'scan':
          await getScanRequestList(this.addDateRange(this.queryParams)).then(response => {
            this.exportId = response.data.list.Id
            this.total = response.data.count
          }, _ => {
            this.scanRequestList = []
            this.total = 0
          })
          break
        // 暂时未支持vie导出
        // case 'vie':
        //   break
        default:
          await listRequests(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
            this.exportId = response.data.list.Id
            this.total = response.data.count
          })
          break
      }
      this.title = '【发起导出审批】需要导出 ' + this.total + '条记录'
      this.message('info', '成功查询到' + this.total + '条记录。', '')
      // 发起审批
      await this.approval()
    },
    /* 发起审批 */
    async approval() {
      // 开启审批表单
      this.approvalOpen = true
    },
    // 获取表单参数
    getFormParams() {
      this.form.id = this.exportId
      this.form.approver_id = parseInt(this.form.approver_id)
      this.form.starttime = this.dowdate
      // 判断function
      // console.log('this.approverFunction:', this.approverFunction)
      this.form.function = this.approverFunction
      this.form.action = 'export'
      // 设置purposes
      if (this.form.purposes === '') {
        this.form.purposes = '[' + this.form.function + ']导出满足查询条件的记录'
      }
    },
    /** 提交按钮 */
    submitFormApproval: function() {
      // this.loading = true
      this.getFormParams()
      this.$refs['form'].validate((valid) => {
        if (valid) {
          postCvApproval(this.form).then((response) => {
            this.reset()
            if (response.code === 200) {
              this.msgSuccess(response.msg)
              this.approvalOpen = false
              // this.getList()
            } else {
              this.msgError(response.msg)
            }
          })
        } else {
          this.$message({
            showClose: true,
            message: '参数设置有误，审批表单提交失败! ',
            center: true,
            type: 'info'
          })
        }
      })
    },
    // 取消按钮
    cancel() {
      this.approvalOpen = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined
      }
      this.resetForm('form')
    },
    message(type, msg, dataMsg) {
      this.$message({
        showClose: true,
        message: msg + dataMsg,
        center: true,
        duration: 4000,
        type: type
      })
    }
  }
}
</script>
