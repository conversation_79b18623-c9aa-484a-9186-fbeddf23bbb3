<template>
  <!-- 创建一个卡片式的容器，使用主题内容区域将背景色统一 -->
  <el-card>
    <!-- 标题插槽：显示使用说明 -->
    <div slot="header">
      <CVHint>使用说明：（图片转docx/xlsx/pptx）选择一张图片文件，系统会对文件进行处理，点击“下载结果”获取处理的效果</CVHint>
    </div>

    <!-- 主体内容 -->
    <div class="card-content">
      <div>
        <div class="container">
          <el-row type="flex" justify="center" :gutter="20">
            <!-- 将不同版本的提示语挪为单独一列 -->
            <el-col :span="5" class="grid-content">
              <div class="params-container" style="width: 100% !important">
                <div class="params">
                  <span style="color:blue"> {{ "版本信息：" }}</span>
                </div><br>
                <div class="params">
                  <span v-show="remark === ''" style="color:darkblue"> {{ "已是最新效果。" }}</span>
                </div>
                <div class="params">
                  <!-- 不同版本对应的提示语 -->
                  <span style="color:darkblue"> {{ remark }}</span>
                </div>
              </div>
              <!-- 展示获取到的文件信息 -->
              <br><div>
                <div class="params">
                  <span v-show="result.classifier !== '' && result.classifier !== undefined" style="color:blue"> {{ "获取的文件信息：" }}</span>
                </div>
                <div>
                  <ul v-show="result.classifier !== '' && result.classifier !== undefined" class="list-item">
                    <li style="color:darkblue"> {{ "分类器：" }}</li>
                    <p style="color:black"> {{ result.classifier }}</p>
                    <li style="color:darkblue"> {{ "关键词：" }}</li>
                    <p v-if="result.keyWords > 0" style="color:black"> {{ result.keyWords }}</p>
                    <p v-else style="color:black"> {{ "未提取到关键词。" }}</p>
                    <li style="color:darkblue"> {{ "总结：" }}</li>
                    <p style="color:black"> {{ result.summary }}</p>
                  </ul>
                </div><br>
              </div>
              <!-- 展示错误信息的无序列表 -->
              <!-- <br><div>
                <div class="params">
                  <span v-show="result.errMsgs.length > 0" style="color:blue"> {{ "错误信息：" }}</span>
                </div><br>
                <ul>
                  <li v-for="(errMsg, index) in result.errMsgs" :key="index">
                    {{ errMsg }}
                  </li>
                </ul>
              </div> -->
            </el-col>
            <el-col :span="8" class="grid-content">
              <span style="color:blue"> {{ "参数设置：" }}</span>
              <div class="upload-container">
                <div class="params-container">
                  <div class="params">
                    <span class="param-label">版本 :</span>
                    <el-select
                      v-model="startPage.version"
                      class="param-input"
                      placeholder="版本"
                      size="small"
                      @change="selectVersion"
                    >
                      <el-option
                        v-for="dict in versions"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                        @click.native="handleSelect(dict)"
                      />
                    </el-select>
                  </div>
                  <div class="params">
                    <span class="param-label">dpi :</span>
                    <el-input
                      v-model="dpi.value"
                      class="param-input"
                      type="number"
                      :min="dpi.min"
                      :max="dpi.max"
                      @change="dpiChange"
                    />
                  </div>
                  <div class="params">
                    <span class="param-label">导出类型 :</span>
                    <el-select
                      v-model="startPage.exporttype"
                      class="param-input"
                      placeholder="导出类型"
                      size="small"
                      @change="selectExporttype"
                    >
                      <el-option
                        v-for="dict in exporttypeOptions"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                        :disabled="dict.disabled"
                      />
                    </el-select>
                  </div>
                </div>
                <div class="params-container" style="width: 100% !important">
                  <!-- <div class="params">
                    <span
                      class="param-label"
                      style="width: 25% !important"
                    >url of png :</span>
                    <el-input
                      v-model="fileUrl"
                      class="param-input"
                      style="width: 75% !important"
                      type="text"
                      :disabled="startPage.exporttype==='picedit'"
                    />
                  </div> -->
                  <!-- 新增高级设置按钮 -->
                  <br><div class="params">
                    <el-button
                      type="warning"
                      size="mini"
                      @click="handleSetting"
                    >高级设置</el-button>
                    <el-tooltip content="切换输入方式" placement="right-end">
                      <el-select v-model.number="choiceInput" placeholder="默认上传文件">
                        <el-option
                          v-for="dict in choiceInputOptions"
                          :key="dict.value"
                          :label="dict.label"
                          :value="dict.value"
                        />
                      </el-select>
                    </el-tooltip>
                    <!-- <el-button
                      type="primary"
                      size="mini"
                      :disabled="startPage.exporttype==='picedit'"
                      @click="pdf2wordconvertclick"
                    >转换</el-button> -->
                  </div>

                  <hr>
                  <div v-if="choiceInput=='inputUrl'">
                    <br><span style="color:blue"> {{ "输入图片链接：" }}</span><br>
                    <div class="params">
                      <span
                        class="param-label"
                        style="width: 25% !important"
                      >url of png :</span>
                      <el-input
                        v-model="fileUrl"
                        class="param-input"
                        style="width: 75% !important"
                        type="text"
                        :disabled="startPage.exporttype==='picedit'"
                      />
                    </div>
                    <el-button
                      type="primary"
                      size="mini"
                      :disabled="startPage.exporttype==='picedit'"
                      @click="pdf2wordconvertclick"
                    >开始转换</el-button>
                  </div>

                  <!-- 转换或点击上传前高级设置对话框 -->
                  <el-dialog :title="title" :visible.sync="open" width="600px">
                    <el-form ref="form" :model="form" :rules="rules" label-width="120px" inline-message>
                      <el-row>
                        <el-col :span="12">
                          <el-tooltip content="是否使用扫描模式" placement="left-end">
                            <template slot="default">
                              <el-form-item label="isScan:">
                                <el-radio-group v-model="form.isScan">
                                  <el-radio
                                    v-for="dict in isScanOptions"
                                    :key="dict.value"
                                    :label="dict.value"
                                  >{{ dict.label }}</el-radio>
                                </el-radio-group>
                              </el-form-item>
                            </template>
                          </el-tooltip>
                        </el-col>
                        <el-col :span="12">
                          <el-tooltip content="获取文件信息" placement="right-end">
                            <template slot="default">
                              <el-form-item label="getFileMeta:">
                                <el-radio-group v-model="form.getFileMeta">
                                  <el-radio
                                    v-for="dict in getFileMetaOptions"
                                    :key="dict.value"
                                    :label="dict.value"
                                  >{{ dict.label }}</el-radio>
                                </el-radio-group>
                              </el-form-item>
                            </template>
                          </el-tooltip>
                        </el-col>
                      </el-row>
                      <el-row>
                        <el-col :span="12">
                          <el-form-item label="PDFDebugFlags:" prop="pdfDebugFlags">
                            <el-input v-model.number="form.pdfDebugFlags" placeholder="请输入PDFDebugFlags, 默认值为0" />
                          </el-form-item>
                        </el-col>
                        <el-col :span="12">
                          <el-tooltip content="是否使用一键清晰" placement="right-end">
                            <template slot="default">
                              <el-form-item label="一键清晰:">
                                <el-radio-group v-model="form.isOptPlus">
                                  <el-radio
                                    v-for="dict in isOptPlusOptions"
                                    :key="dict.value"
                                    :label="dict.value"
                                  >{{ dict.label }}</el-radio>
                                </el-radio-group>
                              </el-form-item>
                            </template>
                          </el-tooltip>
                        </el-col>
                      </el-row>

                      <el-row v-if="form.isOptPlus==true">
                        <el-col :span="12">
                          <el-tooltip content="是否使用文字超分" placement="left-end">
                            <template slot="default">
                              <el-form-item label="use_sr:">
                                <el-radio-group v-model="form.isOptPlus_useSr">
                                  <el-radio
                                    v-for="dict in isOptPlusUseSrOptions"
                                    :key="dict.value"
                                    :label="dict.value"
                                  >{{ dict.label }}</el-radio>
                                </el-radio-group>
                              </el-form-item>
                            </template>
                          </el-tooltip>
                        </el-col>
                        <el-col :span="12">
                          <el-tooltip content="是否使用四边形检测" placement="right-end">
                            <template slot="default">
                              <el-form-item label="use_quad:">
                                <el-radio-group v-model="form.isOptPlus_useQuad">
                                  <el-radio
                                    v-for="dict in isOptPlusUseQuadOptions"
                                    :key="dict.value"
                                    :label="dict.value"
                                  >{{ dict.label }}</el-radio>
                                </el-radio-group>
                              </el-form-item>
                            </template>
                          </el-tooltip>
                        </el-col>
                      </el-row>
                      <el-row v-if="form.isOptPlus==true">
                        <el-col :span="12">
                          <el-tooltip content="是否使用手写去除模型" placement="left-end">
                            <template slot="default">
                              <el-form-item label="use_hw:">
                                <el-radio-group v-model="form.isOptPlus_useHw">
                                  <el-radio
                                    v-for="dict in isOptPlusUseHwOptions"
                                    :key="dict.value"
                                    :label="dict.value"
                                  >{{ dict.label }}</el-radio>
                                </el-radio-group>
                              </el-form-item>
                            </template>
                          </el-tooltip>
                        </el-col>
                        <el-col :span="12">
                          <el-tooltip content="是否使用水印去除模型" placement="right-end">
                            <template slot="default">
                              <el-form-item label="use_wm:">
                                <el-radio-group v-model="form.isOptPlus_useWm">
                                  <el-radio
                                    v-for="dict in isOptPlusUseWmOptions"
                                    :key="dict.value"
                                    :label="dict.value"
                                  >{{ dict.label }}</el-radio>
                                </el-radio-group>
                              </el-form-item>
                            </template>
                          </el-tooltip>
                        </el-col>
                      </el-row>

                    </el-form>
                    <div slot="footer" class="dialog-footer">
                      <el-button class="reset-button" type="warning" @click="reset">重 置</el-button>
                      <div class="right-buttons">
                        <el-button class="confirm-button" type="primary" @click="submitForm">确 定</el-button>
                        <el-button class="cancel-button" @click="cancel">取 消</el-button>
                      </div>
                    </div>
                  </el-dialog>

                </div>

                <!-- 新增转化结果 -->
                <!-- <div class="params-container">
                  <br><div v-show="fileName !== ''" class="params">
                    <span style="color:blue" class="param-label">{{ '转化结果' }} :</span>
                  </div>
                  <div v-show="fileName !== ''" class="params">
                    <span class="param-label">{{ fileName }} :</span>
                    <span class="param-input">
                      {{ fileSize }}
                    </span>
                  </div>
                  <div v-show="statistic.status !== -1" class="params">
                    <span class="param-label">处理用时 :</span>
                    <span class="param-input">
                      {{ formattedDuration }}
                    </span>
                  </div>
                  <div v-show="statistic.status !== -1" class="params">
                    <span
                      v-show="statistic.status === 1"
                      class="param-label"
                      style="color:blue"
                    >转化成功 !</span>
                    <span
                      v-show="statistic.status === 0"
                      class="param-label"
                      style="color: #ff0000"
                    >转化失败!</span>
                  </div>
                </div> -->

                <div v-if="choiceInput=='uploadFile'">
                  <br><span style="color:blue"> {{ "上传图片：" }}</span>
                  <el-upload
                    ref="upload"
                    v-loading="loading"
                    element-loading-text="文件处理中"
                    class="uploader"
                    list-type="text"
                    :accept="fileType.join(',')"
                    :multiple="true"
                    :show-file-list="true"
                    :file-list="fileList"
                    :http-request="pdf2wordUpload"
                    :on-remove="handleRemove"
                    :auto-upload="true"
                    action="placeholder"
                    drag
                  >
                    <i class="el-icon-upload" />
                    <div class="el-upload__text">
                      将文件拖到此处，或
                      <el-button type="text">点击上传</el-button>
                    </div>
                  </el-upload>
                  <br><div class="params">
                    <el-button size="small" type="primary" @click="submitUpload">开始转换</el-button>
                    <el-button size="small" type="success" @click="clearUpload">清空上传列表</el-button>
                  </div>
                </div>
                <!-- <em>点击上传</em> -->
                <el-progress
                  v-if="percentage !== 0 && percentage !== 100"
                  :text-inside="true"
                  :stroke-width="14"
                  :percentage="percentage"
                />
              </div>

              <div><hr>
                <div class="params-container">
                  <br><div class="params">
                    <br><div><span style="color:darkblue"> {{ '转化成功后的结果在页面下方展示。' }}</span></div><br>
                  </div>
                  <div v-show="fileNameList.length !== 0" class="params">
                    <span style="color:blue" class="param-label">{{ '转化结果' }} :</span>
                    <div v-for="(file, index) in fileNameList" :key="`${file.fileName}-${index}`">
                      <span class="param-label">{{ file.fileName }} :</span>
                      <span class="param-input">{{ file.fileSize }}</span>
                      <div v-if="form.isOptPlus==true&&optPlusUrl.length>0">
                        <div v-for="(opturl, i) in optPlusUrl" :key="i">
                          <div v-if="index==i">
                            <el-button
                              size="medium"
                              type="text"
                              @click="copyLink(opturl)"
                            >复制一键清晰转化后的{{ file.fileName }}</el-button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div v-show="statistic.status !== -1" class="params">
                    <span class="param-label">处理用时 :</span>
                    <span class="param-input">
                      {{ formattedDuration }}
                    </span>
                  </div>
                  <div v-show="statistic.status !== -1" class="params">
                    <span
                      v-show="statistic.status === 1"
                      class="param-label"
                      style="color:blue"
                    >转化成功 !</span>
                    <span
                      v-show="statistic.status === 0"
                      class="param-label"
                      style="color: #ff0000"
                    >转化失败!</span>
                  </div>
                </div>
              </div>

              <!-- <a v-show="result.pdfUrl!==''" @click="downloadFile()">-->
              <a
                v-show="result.pdfUrl!== undefined&&result.pdfUrl !== ''"
                target="_blank"
                download=""
                :href="result.pdfUrl"
              ><div class="download-doc-btn">PDF 下载</div></a>
              <a
                v-show="result.wordUrlKs !== ''&&startPage.exporttype=='pdf2word'"
                target="_blank"
                download=""
                :href="result.wordUrlKs"
              ><div class="download-doc-btn">WORD 下载</div></a>
              <a
                v-show="result.wordUrlKs !== ''&&startPage.exporttype=='pdf2docx'"
                target="_blank"
                download=""
                :href="result.wordUrlKs"
              ><div class="download-doc-btn">DOCX 下载</div> </a>
              <a
                v-show="result.officeUrlKs !== ''&&startPage.exporttype=='pdf2pptx'"
                target="_blank"
                download=""
                :href="result.officeUrlKs"
              ><div class="download-doc-btn">PPTX 下载</div> </a>
              <a
                v-show="result.officeUrlKs !== ''&&startPage.exporttype=='pdf2xlsx'"
                target="_blank"
                download=""
                :href="result.officeUrlKs"
              ><div class="download-doc-btn">XLSX 下载</div> </a>
              <a
                v-show="result.officeUrlKs !== undefined&&result.officeUrlKs !== ''&&startPage.exporttype=='picedit'"
                target="_blank"
                download=""
                :href="result.officeUrlKs"
              ><div class="download-doc-btn">PPTX 下载</div> </a>
              <a
                v-show="result.xmlUrl !== undefined&&result.xmlUrl !== '' &&startPage.exporttype=='picedit'"
                target="_blank"
                download=""
                :href="result.xmlUrl"
              ><div class="download-doc-btn">XML 下载</div> </a>
              <!--          <a-->
              <!--            v-show="result.jsonUrl !== ''&&startPage.exporttype=='pdf2txt'"-->
              <!--            target="_blank"-->
              <!--            download=""-->
              <!--            :href="result.jsonUrl"-->
              <!--          >-->
              <!--            <div class="download-doc-btn">JSON 下载</div>-->
              <!--          </a>-->

              <!--            <div class="download-doc-btn" @click="pdfjson2string()">TXT 下载</div>-->

              <a
                v-show="((result.text != undefined&&result.text !== '')||(result.jsonUrl != undefined&&result.jsonUrl !== ''))&&startPage.exporttype=='pdf2txt'"
                target="_blank"
                download=""
              ><div class="download-doc-btn" @click="res2txt()">查看文本内容</div></a>

              <!-- v-show="result.jsonUrl !== undefined&&result.jsonUrl !== ''&&startPage.exporttype=='pdf2txt'" -->
              <div
                v-show="((result.text != undefined&&result.text !== '')||(result.jsonUrl != undefined&&result.jsonUrl !== ''))&&startPage.exporttype=='pdf2txt'"
                class="download-doc-btn"
                @click="openDialog()"
              >查看文本框</div>
              <!-- <a
                v-show="((result.text != undefined&&result.text !== '')||(result.jsonUrl != undefined&&result.jsonUrl !== ''))&&startPage.exporttype=='pdf2txt'"
                target="_blank"
                download=""
              ><div class="download-doc-btn" @click="openPicPointImg()">查看文本框</div></a> -->
              <el-dialog :visible="showDialog" title="查看文本框" @close="closeDialog">
                <div v-for="(textUrl, index) in imgUrl" :key="index">
                  <!-- <a @click="openPicPointImg()">查看文本框 {{ index }}</a> -->
                  <a
                    target="_blank"
                    download=""
                  ><div class="download-doc-btn" @click="openPicPointImg(textUrl, index)">查看图片 {{ index+1 }} 的文本框</div></a>
                </div>
              </el-dialog>

              <!-- 展示错误信息的无序列表 -->
              <ul>
                <li v-for="(errMsg, index) in result.errMsgs" :key="index">
                  {{ errMsg }}
                </li>
              </ul>
            </el-col>
            <el-col :span="5" class="grid-content">
              <!-- 80px(div+margin)*4+1px(border)*4 -->
              <CVDemo
                :loading="loading"
                :data="urlFiles[0]"
                @click="pdf2word(0, $event)"
              >示例:</CVDemo>
            </el-col>
          </el-row>
        </div>
      </div>
    </div>

    <!-- 底部插槽：未使用，后续若是扩展可以考虑翻页等功能 -->
    <div slot="footer">
      Card Footer
    </div>
  </el-card>

</template>

<script>
import axios from 'axios'
import CVHint from './components/CVHint'
import CVDemo from './components/CVDemo'
import CryptoJS from 'crypto-js'
import {
  urlRegex,
  formatfileSize,
  isValidFile,
  downloadFile
} from '@/api/cv/utils'
import { axios_wait_doc } from '@/api/cv/request'
import {
  httpRequesSignKs,
  uploadKs3Store,
  optPlus,
  pic2WordPdfCreateUrl,
  // picEditCreateForm,
  convertPicEditXml2pptx,
  fetchUrl2Ks
} from '@/api/cv/request-ks3'
import { getHeaders } from '@/utils/get-header'
import { sleep } from '@/api/cv/utils'

export default {
  name: 'LayoutPic',
  components: { CVHint, CVDemo },
  props: {
    maxFileNum: {
      validator(value) {
        if (value <= 0) {
          return 1
        }
        return value
      },
      default: 1
    },
    fileType: {
      type: Array,
      default: function() {
        return ['.png', '.jpg', '.jpeg']
      }
    }
  },
  data() {
    return {
      // 输入方式字典
      choiceInputOptions: [
        { label: '上传图片(默认)', value: 'uploadFile' },
        { label: '输入图片链接', value: 'inputUrl' }
      ],
      choiceInput: 'uploadFile',
      // txt下载url
      txt_url: '',
      // dpi 设置
      dpi: {
        value: 144,
        default: 144,
        min: 1,
        max: 400
      },
      // 弹出层标题（用户导入）
      title: '',
      // 是否显示弹出层（用户导入）
      open: false,
      // 表单参数初始值
      form: {
        pdfDebugFlags: 0,
        getFileMeta: false,
        isScan: false,
        isOptPlus: false,
        isOptPlus_useSr: true,
        isOptPlus_useQuad: true,
        isOptPlus_useHw: false,
        isOptPlus_useWm: false
      },
      // 参数
      pdfDebugFlags: 0,
      getFileMeta: false,
      isScan: false,
      isOptPlus: false,
      isOptPlus_useSr: true,
      isOptPlus_useQuad: true,
      isOptPlus_useHw: false,
      isOptPlus_useWm: false,
      // 是否获取文件信息
      getFileMetaOptions: [
        { label: '是', value: true },
        { label: '否', value: false }
      ],
      // 是否使用扫描模式
      isScanOptions: [
        { label: '是', value: true },
        { label: '否', value: false }
      ],
      // 是否使用一键清晰
      isOptPlusOptions: [
        { label: '是', value: true },
        { label: '否', value: false }
      ],
      // 一键清晰: 是否使用文字超分
      isOptPlusUseSrOptions: [
        { label: '是', value: true },
        { label: '否', value: false }
      ],
      // 一键清晰: 是否使用四边形检测
      isOptPlusUseQuadOptions: [
        { label: '是', value: true },
        { label: '否', value: false }
      ],
      // 一键清晰: 是否使用手写去除模型
      isOptPlusUseHwOptions: [
        { label: '是', value: true },
        { label: '否', value: false }
      ],
      // 一键清晰: 是否使用水印去除模型
      isOptPlusUseWmOptions: [
        { label: '是', value: true },
        { label: '否', value: false }
      ],
      // 是否处理混合件
      switchMixOptions: [
        { label: '是', value: true },
        { label: '否', value: false }
      ],
      // 表单校验
      rules: {
        // 定义规则对象
        // trigger: 'blur' 表示在失去焦点时触发验证
        pdfDebugFlags: [{ type: 'number', message: 'PDFDebugFlags必须为int类型', trigger: ['blur', 'change'] }]
      },
      loading: false,
      url: {
        value: '//pcv-test.wps.cn',
        default: '//pcv-test.wps.cn'
      },
      optPlus_url: '//pcv-test-scan.wps.cn',
      optPlus_result_url: '',
      remark: '',
      fileUrl: '',
      fileObj: {
        name: '',
        size: 0
      },
      uri: '/create',
      fileList: [],
      result: {
        duration: 0,
        pdfUrl: '',
        wordUrlKs: '',
        officeUrlKs: '',
        xmlUrl: '',
        jsonUrl: '',
        errMsgs: [],
        text: '',

        classifier: '',
        keyWords: [],
        summary: ''
      },
      statistic: {
        status: -1,
        duration: 0
      },
      versions: [],
      exporttypeOptions: [],
      startPage: {
        exporttype: 'pdf2docx',
        version: ''
      },
      percentage: 0,
      urlFiles: [
        [
          {
            url: 'http://zhai-datas.ks3-cn-beijing.ksyun.com/layout/people/luhaoxian/picture/示例一.png',
            name: '示例一.png',
            size: '224256'
          }
        ]
      ],
      fileNameList: [],
      // 存储一键清晰得到的url
      optPlusUrl: [],
      // 用于绘制文本框
      imgUrl: [],
      // 查看文本框的弹窗
      showDialog: false,
      textBox: []
    }
  },
  computed: {
    formattedDuration() {
      return this.statistic.duration.toFixed(3) + 's'
    }
    // fileName() {
    //   const { name } = this.fileObj
    //   return name.trim()
    // },
    // fileSize() {
    //   const { size } = this.fileObj
    //   return formatfileSize(size)
    // }
  },
  // 通过watch监听输入值的变化，并将maxConNumPage/pdfDebugFlags由 string类型转为 int类型
  watch: {
    'form.pdfDebugFlags'(val) {
      this.pdfDebugFlagsInt = parseInt(val)
    }
  },
  created() {
    this.getDicts('img_export_type').then(response => {
      this.exporttypeOptions = response.data
    })
    this.getDicts('pdf_convert_version').then(response => {
      this.versions = response.data

      if (this.versions.length > 0) {
        this.url.value = this.versions[0].value
        this.startPage.version = this.versions[0].label
        this.remark = this.versions[0].remark
      }
    })
  },
  methods: {
    // dpi 设置
    numberInputChange(obj, value) {
      if (value === '') {
        obj.value = obj.default
      } else if (value > obj.max) {
        obj.value = obj.max
      } else if (value < obj.min) {
        obj.value = obj.min
      }
      obj.value = parseInt(value)
    },
    dpiChange(value) {
      this.numberInputChange(this.dpi, value)
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.pdfDebugFlags = this.form.pdfDebugFlags
          this.getFileMeta = this.form.getFileMeta
          this.isScan = this.form.isScan
          this.isOptPlus = this.form.isOptPlus
          this.isOptPlus_useSr = this.form.isOptPlus_useSr
          this.isOptPlus_useQuad = this.form.isOptPlus_useQuad
          this.isOptPlus_useHw = this.form.isOptPlus_useHw
          this.isOptPlus_useWm = this.form.isOptPlus_useWm
          this.open = false
        }
      })
    },
    /** 复制链接 */
    copyLink(url) {
      var inputNode = document.getElementById('pdfcv_input')
      if (inputNode != null) {
        inputNode.remove()
      }
      inputNode = document.createElement('input')
      // inputNode.value = this.url
      inputNode.value = url
      inputNode.id = 'pdfcv_input'
      document.body.appendChild(inputNode)
      inputNode.select()
      document.execCommand('Copy')
      inputNode.className = 'oInput'
      inputNode.style.display = 'none'
      this.$message.success('复制成功')
    },
    // 取消按钮
    cancel() {
      this.form.pdfDebugFlags = this.pdfDebugFlags
      this.form.getFileMeta = this.getFileMeta
      this.form.isScan = this.isScan
      this.form.isOptPlus = this.isOptPlus
      this.form.isOptPlus_useSr = this.isOptPlus_useSr
      this.form.isOptPlus_useQuad = this.isOptPlus_useQuad
      this.form.isOptPlus_useHw = this.isOptPlus_useHw
      this.form.isOptPlus_useWm = this.isOptPlus_useWm
      this.open = false
      // this.reset()
    },
    /** 重置按钮 */
    // 表单重置
    reset() {
      this.form = {
        pdfDebugFlags: 0,
        switchMix: true,
        getFileMeta: false,
        isScan: false,
        isOptPlus: false,
        isOptPlus_useSr: true,
        isOptPlus_useQuad: true,
        isOptPlus_useHw: false,
        isOptPlus_useWm: false
      }
      this.resetForm('form')
    },
    /** 高级设置按钮操作 */
    handleSetting() {
      // this.reset()

      this.open = true
      this.title = '高级设置'
      this.form.password = ''
    },
    progress(percentage) {
      this.percentage = percentage
    },
    progressPass(percentage) {
      this.percentage = 100
    },
    handleSelect(item) {
      this.remark = item.remark
    },
    selectVersion(value) {
      // this.startPage.exporttype = 'pdf2docx'
      this.url.value = value

      this.resetResult()
    },
    selectExporttype(value) {
      this.resetResult()
    },
    urlChange(value) {
      // url 检测
      if (urlRegex.test(value)) {
        this.url.value = value
      } else {
        this.url.value = this.url.default
      }
    },
    fileChange(file, fileList) {
      this.fileList = fileList.slice(-this.maxFileNum)
    },
    onSuccess(response, file, fileList) {
      // hook
      this.fileChange(file, fileList)
    },
    resetResult() {
      this.percentage = 0 // 清空进度条
      this.result = {
        duration: 0,
        pdfUrl: '',
        wordUrlKs: '',
        officeUrlKs: '',
        xmlUrl: '',
        // jsonUrl: '',
        errMsgs: [],
        text: '',

        classifier: '',
        keyWords: [],
        summary: ''
      }
      this.statistic = {
        status: -1,
        duration: 0
      }
    },
    // pdfjson2string() {
    //   const proxy_url = this.result.jsonUrl.replace(/^http:\/\/zhai-platereduction\.ks3-cn-beijing(-internal)?\.ksyun\.com/, '/kso')
    //   return axios({
    //     method: 'get',
    //     headers: { 'Cache-Control': 'no-cache', ...getHeaders() },
    //     url: proxy_url
    //   }).then((res) => {
    //     if (res === undefined) {
    //       console.log('undefined res')
    //       return Promise.resolve(-1)
    //     } else {
    //       var txt_array = []
    //       // json数据提取文字到array
    //       res.data.forEach(page => {
    //         txt_array.push('[ 页标识 ] ------------ page ' + page.number + ' ------------ \n')
    //         page.blocks.forEach(block => {
    //           block.lines.forEach(line => {
    //             txt_array.push(line.text)
    //             if (line.text.charCodeAt(line.text.length - 1) <= 255) {
    //               txt_array.push(' ')
    //             }
    //           })
    //           txt_array.push('\n')
    //         })
    //       })
    //       const txt_string = txt_array.join('')

    //       const blob = new Blob([txt_string], { type: 'text/plain;charset=utf-8' })
    //       const txt_url = window.URL.createObjectURL(blob)
    //       window.open(txt_url + '#json.txt', '_blank')
    //       window.URL.revokeObjectURL(txt_url)
    //     }
    //   })
    // },
    /** TXT下载按钮操作 */
    res2txt() {
      var texts = ''
      // console.log('result.text: ', this.result.text)
      for (var item of this.result.text) {
        if (item.sentences !== undefined) {
          for (var sentence of item.sentences) {
            texts += sentence.text
          }
          texts += '\n'
        } else {
          texts = this.result.text
        }
      }
      // 使用Blob对象创建一个包含texts内容的文本文件
      const blob = new Blob([texts], { type: 'text/plain;charset=utf-8' })
      // 创建一个指向这个新创建的文本文件的URL
      const txt_url = window.URL.createObjectURL(blob)
      window.open(txt_url + '#json.txt', '_blank')
      // 释放创建的URL
      window.URL.revokeObjectURL(txt_url)
    },
    /** 转换按钮操作 */
    async pdf2wordconvertclick() {
      if (!this.validateUploadPDFUrl(this.fileUrl)) {
        this.$message({
          showClose: true,
          message: '请输入正确文件地址url ',
          center: true,
          duration: 4000,
          type: 'error'
        })
        return false
      }

      this.loading = true
      this.resetResult()
      // 清空列表
      this.fileNameList = []
      this.optPlusUrl = []
      this.imgUrl = []
      this.textBox = []

      if (this.fileUrl.lastIndexOf('ksyun.com') > 0) {
        // if (this.url.value.lastIndexOf('pcv-test') > 0) {
        //   this.uri = '/create'
        // } else {
        //   this.uri = ''
        // }

        this.fileNameList.push({ fileName: '用户输入图片链接', fileSize: '暂不支持获取文件大小。' })
        // 判断是否需要先进行一键清晰
        if (this.isOptPlus === true) {
          const result = await this.useOptPlus(this.fileUrl)
          if (result !== 0) {
            this.optPlusUrl.push(result)
            this.fileUrl = result
          }
        }
        console.log('转换链接为:', this.fileUrl)
        this.imgUrl.push(this.fileUrl)

        const res_create_url_data = await pic2WordPdfCreateUrl(
          this.url.value,
          [this.fileUrl],
          0,
          0,
          this.startPage.exporttype,
          this.uri,
          this.dpi.value,
          this.pdfDebugFlagsInt,
          this.switchMix,
          this.getFileMeta,
          this.isScan
        )

        if (res_create_url_data.status === 200) {
          // 等待1s
          await sleep(1000)
          const docID = res_create_url_data.data.data['docID']
          const s_time = new Date().getTime()
          await this.waitDocStatus(docID)

          const e_time = new Date().getTime()
          this.statistic = {
            status: 1,
            duration: this.result.duration || (e_time - s_time) / 1000
          }
          this.loading = false
          return Promise.resolve()
        }
      } else {
        this.fetchUrl2Ks3(this.fileUrl)
      }
      this.loading = false
    },
    async fetchUrl2Ks3(sourceurl) {
      fetchUrl2Ks(this, sourceurl).then(async(res) => {
        if (res.data.message === 'ok') {
          const ksurl = res.data.url
          console.log(ksurl)

          const res_create_url_data = await pic2WordPdfCreateUrl(
            this.url.value,
            ksurl,
            0,
            0,
            this.startPage.exporttype
          )

          if (res_create_url_data.status === 200) {
            // 等待1s
            await sleep(1000)
            const docID = res_create_url_data.data.data['docID']
            const s_time = new Date().getTime()
            await this.waitDocStatus(docID)

            const e_time = new Date().getTime()
            this.statistic = {
              status: 1,
              duration: this.result.duration || (e_time - s_time) / 1000
            }
            this.loading = false
            return Promise.resolve(0)
          }
        } else {
          this.$message({
            showClose: true,
            message: res.data.msg,
            center: true,
            duration: 4000,
            type: 'error'
          })
          return Promise.resolve(0)
        }
      }).catch((error) => {
        this.$message({
          showClose: true,
          message: '文件处理失败! ' + error,
          center: true,
          duration: 4000,
          type: 'error'
        })
        return Promise.resolve(0)
      })
    },
    validateUploadPDFUrl(fileurl) {
      if (fileurl.trim() === '') {
        return false
      }
      if (!urlRegex.test(fileurl)) {
        return false
      }
      return true
    },
    beforeUpload(file) {
      // 清理旧结果
      this.fileObj = { name: '', size: 0 }
      this.resetResult()

      // 文件类型判断
      const { name } = file
      var ext = '.' + name.trim().split('.').pop()
      ext = ext.toLowerCase()
      if (!isValidFile(ext, this.fileType)) {
        this.$message({
          showClose: true,
          message: '请选择png/jpg/jpeg文件!',
          center: true,
          duration: 4000,
          type: 'error'
        })
        return false
      } else {
        this.fileObj = file
        return true
      }
    },
    async useOptPlus(url) {
      // 调用一键清晰plus
      const optPlus_result_url = await optPlus(
        this.optPlus_url,
        url,
        this.isOptPlus_useSr,
        this.isOptPlus_useQuad,
        this.isOptPlus_useHw,
        this.isOptPlus_useWm
      )
      if (optPlus_result_url.status === 200) {
        console.log('一键清晰完成:', optPlus_result_url.data.data.result_url)
        return optPlus_result_url.data.data.result_url
      }
      return 0
    },
    async pdf2word(arrayid, id) {
      console.log('array', arrayid, ' index', id)
      // 清空列表
      this.fileNameList = []
      this.optPlusUrl = []
      this.imgUrl = []
      this.textBox = []
      this.clearUpload()
      // 开始加载
      this.loading = true

      const file = this.urlFiles[parseInt(arrayid)][parseInt(id)]
      this.fileObj = { name: file.name, size: file.size }
      this.fileNameList.push({ fileName: file.name, fileSize: formatfileSize(file.size) })
      this.resetResult()
      console.log(file.url, this.startPage.exporttype, this.startPage.exporttype === 'pdf2txt')

      this.fileUrl = file.url
      // 判断是否需要先进行一键清晰
      if (this.isOptPlus === true) {
        const result = await this.useOptPlus(this.fileUrl)
        if (result !== 0) {
          this.optPlusUrl.push(result)
          this.fileUrl = result
        }
      }
      this.imgUrl.push(this.fileUrl)
      const res_create_url_data = await pic2WordPdfCreateUrl(
        this.url.value,
        [this.fileUrl],
        0,
        0,
        this.startPage.exporttype,
        this.uri,
        this.dpi.value,
        this.pdfDebugFlagsInt,
        this.switchMix,
        this.getFileMeta,
        this.isScan
      )
      await this.setResult(res_create_url_data)
      const s_time = new Date().getTime()
      // 结束加载
      this.loading = false
      const e_time = new Date().getTime()
      this.statistic = {
        status: 1,
        duration: this.result.duration || (e_time - s_time) / 1000
      }
      return Promise.resolve()
    },
    getFileNameMD5(name) {
      const userId = this.$store.state.user.userId
      const deptId = this.$store.state.user.deptId
      const useremail = this.$store.state.user.email

      var a = name.lastIndexOf('.')
      var filter = name.substring(a + 1, name.length)
      var fname = name.substring(0, a)

      var mddata = fname + userId + useremail + deptId

      return CryptoJS.MD5(mddata).toString() + '.' + filter
    },
    clearUpload() {
      this.$refs.upload.clearFiles()
      this.fileList = []
    },
    handleRemove(file, fileList) {
      this.fileList = fileList
    },
    pdf2wordUpload(param) {
      this.fileList.push(param.file)
    },
    /** 点击上传按钮操作 */
    async submitUpload(param) {
      // 开始加载
      this.loading = true

      var urlLists = []
      // 清空列表
      this.fileNameList = []
      this.optPlusUrl = []
      this.imgUrl = []
      this.textBox = []
      for (var element of this.fileList) {
        if (!this.beforeUpload(element)) {
          // 结束加载
          this.loading = false
          return Promise.reject()
        }
        this.fileNameList.push({ fileName: element.name, fileSize: formatfileSize(element.size) })
        const filenamebymd5 = this.getFileNameMD5(element.name)
        const res_sign_data = await httpRequesSignKs(filenamebymd5)
        if (res_sign_data.status === 200) {
          const res_uploadks3_data = await uploadKs3Store(res_sign_data, element)
          let imageurl =
              'http://' +
              res_sign_data.data.data['url'] +
              '/' +
              res_sign_data.data.data['key']
          if (res_uploadks3_data.status === 200) {
            // 判断是否需要先进行一键清晰
            if (this.isOptPlus === true) {
              const result = await this.useOptPlus(imageurl)
              if (result !== 0) {
                this.optPlusUrl.push(result)
                imageurl = result
              }
            }
            urlLists.push(imageurl)
            this.imgUrl.push(imageurl)
          } else {
            this.$message({
              showClose: true,
              message: 'Error uploading to Ks3 store: ' + res_uploadks3_data.msg,
              center: true,
              duration: 4000,
              type: 'error'
            })
            return Promise.resolve()
          }
        } else {
          this.$message({
            showClose: true,
            message: 'Error requesting sign data from Ks: ' + res_sign_data.msg,
            center: true,
            duration: 4000,
            type: 'error'
          })
          return Promise.resolve()
        }
      }
      const res_create_url_data = await pic2WordPdfCreateUrl(
        this.url.value,
        urlLists,
        0,
        0,
        this.startPage.exporttype,
        this.uri,
        this.dpi.value,
        this.pdfDebugFlagsInt,
        this.switchMix,
        this.getFileMeta,
        this.isScan
      )

      if (res_create_url_data.status === 200) {
        // 等待1s
        await sleep(1000)
        const docID = res_create_url_data.data.data['docID']
        await this.waitDocStatus(docID).catch((error) => {
          this.$message({
            showClose: true,
            message: '文件处理失败! ' + error,
            center: true,
            duration: 4000,
            type: 'error'
          })
        })
        if (this.startPage.exporttype === 'picedit') {
          const proxy_url = this.result.xmlUrl.replace(/^http:\/\/zhai-platereduction\.ks3-cn-beijing(-internal)?\.ksyun\.com/, '/kso')
          const xmldata = await axios({
            method: 'get',
            headers: { 'Cache-Control': 'no-cache', ...getHeaders() },
            url: proxy_url
          }).then((res) => {
            return Promise.resolve(res)
          }).catch(err => {
            Promise.reject(err)
          })
          if (xmldata !== undefined) {
            const convertDat = await convertPicEditXml2pptx(this.url.value, xmldata.data).catch((error) => {
              console.log(error)
            })
            if (convertDat !== undefined && convertDat.status === 200) {
              var officeUrl = convertDat.data.data['officeUrl']
              this.result.officeUrlKs = officeUrl.replace('ks3-cn-beijing-internal.ksyun.com',
                'ks3-cn-beijing.ksyun.com').replace('http', 'https')
              var pdfUrl = convertDat.data.data['pdfUrl']
              this.result.pdfUrl = pdfUrl.replace('ks3-cn-beijing-internal.ksyun.com',
                'ks3-cn-beijing.ksyun.com').replace('http', 'https')
            }
          }
        }
      }
      const s_time = new Date().getTime()
      // param.onSuccess()
      // 结束加载
      this.loading = false
      const e_time = new Date().getTime()
      this.statistic = {
        status: 1,
        duration: this.result.duration || (e_time - s_time) / 1000
      }
      return Promise.resolve()
    },
    async setResult(res_create_url_data) {
      if (res_create_url_data.status === 200) {
        // 等待1s
        await sleep(1000)
        const docID = res_create_url_data.data.data['docID']
        await this.waitDocStatus(docID).catch((error) => {
          this.$message({
            showClose: true,
            message: '文件处理失败! ' + error,
            center: true,
            duration: 4000,
            type: 'error'
          })
        })
        if (this.startPage.exporttype === 'picedit') {
          const proxy_url = this.result.xmlUrl.replace(/^http:\/\/zhai-platereduction\.ks3-cn-beijing(-internal)?\.ksyun\.com/, '/kso')
          const xmldata = await axios({
            method: 'get',
            headers: { 'Cache-Control': 'no-cache', ...getHeaders() },
            url: proxy_url
          }).then((res) => {
            return Promise.resolve(res)
          }).catch(err => {
            Promise.reject(err)
          })
          if (xmldata !== undefined) {
            const convertDat = await convertPicEditXml2pptx(this.url.value, xmldata.data).catch((error) => {
              console.log(error)
            })
            if (convertDat !== undefined && convertDat.status === 200) {
              var officeUrl = convertDat.data.data['officeUrl']
              this.result.officeUrlKs = officeUrl.replace('ks3-cn-beijing-internal.ksyun.com',
                'ks3-cn-beijing.ksyun.com').replace('http', 'https')
              var pdfUrl = convertDat.data.data['pdfUrl']
              this.result.pdfUrl = pdfUrl.replace('ks3-cn-beijing-internal.ksyun.com',
                'ks3-cn-beijing.ksyun.com').replace('http', 'https')
            }
          }
        }
      }
    },
    async waitDocStatus(docID) {
      // const docID = res_create_url_data.data.data['docID']
      const tmp_exporttype = this.startPage.exporttype === 'pdf2txt' ? 'pic2txt' : this.startPage.exporttype

      const progress_func = tmp_exporttype === 'pic2txt' || tmp_exporttype === 'picedit' ? this.progressPass : this.progress

      await axios_wait_doc({
        method: 'get',
        headers: { 'Cache-Control': 'no-cache', ...getHeaders() },
        url:
          this.url.value + '/layout/scheduler/' + tmp_exporttype + '/query?docID=' + docID,
        handleProgress: progress_func
      }).then((res) => {
        if (res === undefined) {
          console.log('undefined res')
          return Promise.resolve(-1)
        }

        const { data } = res
        this.getMeta(data)
        if (data.errMsgs !== undefined && data.errMsgs.length > 0) {
          // 追加值，若是存在相同的属性则会覆盖
          this.result = Object.assign({}, this.result, {
            duration: data.duration,
            pdfUrl: data.pdfUrl,
            wordUrlKs:
              data.wordUrlKs !== undefined
                ? data.wordUrlKs.replace('http:', 'https:')
                : '',
            officeUrlKs:
              data.officeUrlKs !== undefined
                ? data.officeUrlKs.replace('http:', 'https:')
                : '',
            errMsgs: data.errMsgs
          })
          return Promise.reject('详情查看错误信息')
        }
        console.log(this.startPage.exporttype)
        if (this.startPage.exporttype === 'pdf2txt') {
          // const json_url = data.json_url !== undefined ? data.json_url.replace('ks3-cn-beijing-internal.ksyun.com',
          //   'ks3-cn-beijing.ksyun.com') : ''
          var success_text = []
          for (var key in data.success) {
            // 合并列表
            success_text = success_text.concat(data.success[key])
            // 用于文本框
            this.textBox.push(data.success[key])
          }
          this.result = Object.assign({}, this.result, {
            duration: data.duration / 1000,
            // jsonUrl: json_url,
            // text: data.success['0'],
            text: success_text,
            errMsgs: []
          })
        } else if (this.startPage.exporttype === 'picedit') {
          const xml_url = data.xml_url !== undefined ? data.xml_url.replace('ks3-cn-beijing-internal.ksyun.com',
            'ks3-cn-beijing.ksyun.com') : ''
          this.result = Object.assign({}, this.result, {
            duration: data.duration / 1000,
            xmlUrl: xml_url,
            errMsgs: []
          })
        } else {
          this.result = Object.assign({}, this.result, {
            duration: data.duration,
            pdfUrl: data.pdfUrl,
            wordUrlKs:
              data.wordUrlKs !== undefined
                ? data.wordUrlKs.replace('http:', 'https:')
                : '',
            officeUrlKs:
              data.officeUrlKs !== undefined
                ? data.officeUrlKs.replace('http:', 'https:')
                : '',
            errMsgs: []
          })
        }
        return Promise.resolve(1)

        // this.result.wordUrl =
        //   data.wordUrl !== undefined ? data.wordUrl.replace("http:", "") : "";
        // this.result.duration = data.duration; // 处理用时
        // this.result.pdfUrl = data.pdfUrl !== undefined ? data.pdfUrl : "";
        // this.result.wordUrlKs =
        //   data.wordUrlKs !== undefined
        //     ? data.wordUrlKs.replace("http:", "")
        //     : "";
        // this.handleOpTimes(data.opTimes);

        // if (data.errMsgs !== undefined && data.errMsgs.length > 0) {
        //   this.result.errMsgs = data.errMsgs;
        //   return Promise.reject("详情查看错误信息");
        // } else {
        //   return Promise.resolve(1);
        // }
      })
    },
    // 获取文件信息
    getMeta(data) {
      if (data.fileMeta !== undefined) {
        this.result = {
          classifier: data.fileMeta.Classifier,
          keyWords: data.fileMeta.KeyWords,
          summary: data.fileMeta.Summary
        }
      }
    },
    downloadFile() {
      downloadFile(this.result.pdfUrl)
    },
    openDialog() {
      this.showDialog = true
    },
    closeDialog() {
      this.showDialog = false
    },
    openPicPointImg(url, index) {
      // const texts = this.result.text
      const texts = this.textBox[index]
      // console.log('texts:', texts)

      // 给图片添加点击事件
      function addEventListenerToImg(w, element, sentence) {
        var textElement = document.createElement('div')
        textElement.style.display = 'none'
        textElement.style.position = 'absolute'
        textElement.style.background = 'rgba(0, 0, 0, 0.7)'
        textElement.style.color = 'white'
        textElement.innerText = 'None'
        w.document.body.appendChild(textElement)
        var pos = sentence.points
        var text = sentence.text
        element.addEventListener('click', function(event) {
          // 获取点击位置的坐标，需要加上页面滚动位置
          var x = event.clientX + w.scrollX || w.pageXOffset
          var y = event.clientY + w.scrollY || w.pageYOffset
          if (x >= pos[0][0] && x <= pos[2][0] && y >= pos[0][1] && y <= pos[2][1]) {
            // 设置文字提示的位置，并显示
            textElement.style.left = x + 'px'
            textElement.style.top = y + 'px'
            textElement.style.display = 'block'
            textElement.innerText = text
          } else {
            textElement.style.display = 'none'
          }
        })
      }

      const newWindow = window.open()
      const img = new Image()
      // img.src = this.imageurl
      // img.src = this.imgUrl[index]
      img.src = url
      img.onload = function() {
        const canvas = newWindow.document.createElement('canvas')
        canvas.width = img.width
        canvas.height = img.height

        // 获取2D上下文
        const ctx = canvas.getContext('2d')
        ctx.drawImage(img, 0, 0)
        ctx.strokeStyle = 'red'
        ctx.lineWidth = 1

        for (var item of texts) {
          // 可能出现没有句子的块，所以需要判断是否为null
          if (item.sentences !== null) {
            for (var sentence of item.sentences) {
              var pos = sentence.points
              ctx.beginPath()
              ctx.moveTo(pos[0][0], pos[0][1])
              ctx.lineTo(pos[1][0], pos[1][1])
              ctx.lineTo(pos[2][0], pos[2][1])
              ctx.lineTo(pos[3][0], pos[3][1])
              ctx.closePath()
              ctx.stroke()
              addEventListenerToImg(newWindow, canvas, sentence)
            }
          }
        }
        newWindow.document.body.appendChild(canvas)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/styles/mixin.scss";

/* 对话框底部按钮区域 */
.dialog-footer {
  display: flex;
  justify-content: space-between;
}
.reset-button {
  order: 1;
}
.right-buttons {
  order: 2;
}
/* 调整内边距的数值 */
.list-item {
    padding-left: 15px;
  }
.container {
  padding-bottom: 2%;
  padding-left: 8%;
  padding-right: 8%;
}
.el-row {
  margin-bottom: 20px;
  &:last-child {
    margin-bottom: 0;
  }
}
.el-col {
  border-radius: 8px;
  border: 1px dashed #d9d9d9;
  background-color: #ffffff;
  color: #666666;
  padding: 1%;
}
.grid-content {
  border-radius: 8px;
  vertical-align: middle;
}

.upload-container {
  width: 100%;
  padding: 1%;
  position: relative;
  @include clearfix;
  .params-container {
    width: 50%;
    display: inline-block;
    padding-left: 1%;
    padding-right: 1%;
  }
  .params {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }
  .param-label {
    width: 50%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-bottom: 2%;
  }
  .param-input {
    width: 50%;
    text-align: right;
    margin-bottom: 2%;
  }
}

.download-doc-btn {
  text-align: center;
  width: 100%;
  height: 4vh;
  font-size: 2vh;
  line-height: 4vh;
  color: #fff;
  background-color: #338ef0;
  border: none;
  border-radius: 4px;
  margin-top: 8px;
}

.download-doc-btn a {
  text-decoration: none;
  color: #fff;
}
</style>
