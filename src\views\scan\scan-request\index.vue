
<template>
  <BasicLayout>
    <template #wrapper>
      <el-card class="box-card">
        <el-form ref="queryForm" :model="queryParams" :inline="true" label-width="68px">
          <el-form-item label="requestID">
            <el-input
              v-model="queryParams.requestID"
              placeholder="请输入requestID"
              clearable
              size="small"
            />
          </el-form-item>
          <el-form-item label="任务ID">
            <el-input
              v-model="queryParams.jobID"
              placeholder="请输入任务ID"
              clearable
              size="small"
            />
          </el-form-item>
          <el-form-item label="日期">
            <el-date-picker
              v-model="queryParams.startTime"
              size="small"
              type="date"
              align="right"
              value-format="yyyyMMdd"
            /> </el-form-item>
          <el-form-item label="算法路由" prop="router">
            <!-- <el-input
            v-model="queryParams.router"
            placeholder="请输入算法路由"
            clearable
            size="small"
          /> -->
            <el-select
              v-model="queryParams.router"
              placeholder="请选择算法路由"
              clearable
              filterable
              size="small"
            >
              <el-option
                v-for="dict in routerTypeOptions"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="服务" prop="service">
            <el-input
              v-model="queryParams.service"
              placeholder="请输入服务"
              clearable
              size="small"
            />
          </el-form-item>
          <el-form-item label="状态" prop="statusCode"><el-select
            v-model="queryParams.statusCode"
            placeholder="状态"
            clearable
            filterable
            size="small"
          >
            <el-option
              v-for="dict in statusOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
          </el-form-item>

          <el-form-item label="错误信息" prop="errMsg"><el-input
            v-model="queryParams.errMsg"
            placeholder="请输入错误信息"
            clearable
            size="small"
          />
          </el-form-item>
          <el-form-item label="错误码" prop="errCode"><el-input
            v-model="queryParams.errCode"
            placeholder="请输入错误码"
            clearable
            size="small"
          />
          </el-form-item>
          <el-form-item>
            <el-button v-permisaction="['scan:request:select']" type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button v-permisaction="['scan:request:refresh']" icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
          <el-form-item label="错误详情">
            <el-checkbox
              @change="errShowMsgClick()"
            />
          </el-form-item>
          <el-form-item>
            <CVdataExport
              :query-params="{ ...queryParams, pageIndex: queryParams.pageIndex, pageSize: queryParams.pageSize }"
              :approver-list="approver_list"
              approver-function="scan"
            />
          </el-form-item>
        </el-form>

        <el-table v-loading="loading" :data="scanRequestList" span="1.36">
          <el-table-column v-if="false" type="selection" width="5" align="center" />
          <el-table-column
            label="requestID"
            align="center"
            prop="request_id"
            width="280"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="任务ID"
            align="center"
            prop="job_id"
            width="280"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="日期"
            align="center"
            prop="start_time"
            width="180"
            :formatter="formatDate"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="算法路由"
            align="center"
            prop="router"
            width="150"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="服务"
            align="center"
            prop="service"
            width="160"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="状态"
            align="center"
            prop="status_code"
            :formatter="statusFormat"
            width="60"
          >
            <template slot-scope="scope">
              {{ statusFormat(scope.row) }}
            </template>
          </el-table-column><el-table-column
            label="耗时(s)"
            align="center"
            prop="request_time"
            width="120"
            sortable
            :show-overflow-tooltip="true"
          />
          <el-table-column
            v-if="showErrMsgs"
            label="错误信息"
            align="center"
            prop="err_msg"
            width="250"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            v-if="showErrMsgs"
            label="错误码"
            align="center"
            prop="err_code"
            width="80"
            :show-overflow-tooltip="true"
          />
          <el-table-column label="详情" align="center">
            <template slot-scope="scope">
              <CVdataParameter
                :key="scope.row.request_id"
                :docid="scope.row.request_id"
                :starttime="queryParams.startTime"
                function="scan"
              />
            </template>
          </el-table-column>
          <el-table-column label="下载" width="300" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <ScanOperation
                :approver-list="approver_list"
                :show-result-url="scope.row.result_url !=''"
                :show-origin-url="scope.row.origin_url !=''"
                :requestid="scope.row.request_id"
                :dowdate="queryParams.startTime"
                :show-img="true"
                :img-names="['原图','效果图']"
              />
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageIndex"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>

    </template>
  </BasicLayout>
</template>

<script>
import { getScanRequestList } from '@/api/scan/scan-request'
import ScanOperation from '@/components/ScanOperation/index.vue'
import CVdataParameter from '@/components/Cdata/Parameter.vue'
import CVdataExport from '@/components/Cdata/Export.vue'

export default {
  name: 'ScanRequest',
  components: {
    ScanOperation, CVdataParameter, CVdataExport
  },
  data() {
    return {
      showErrMsgs: false,
      // 总条数
      total: 0,
      // 弹出层标题
      title: '',
      open: false,
      loading: false,
      // 类型数据字典
      typeOptions: [],
      routerTypeOptions: [],
      scanRequestList: [],
      // 状态选项
      statusOptions: [],
      // 错误码选项
      errCodeOptions: [],
      // 审批人
      approver_list: [],
      // 查询参数
      queryParams: {
        pageIndex: 1,
        pageSize: 10,
        errCode: undefined,
        startTime: undefined,
        statusCode: 'noFinished',
        service: undefined,
        requestID: undefined,
        jobID: undefined,
        errMsg: undefined,
        router: undefined
      },
      showViewer: false,
      viewPhotoList: [],
      // 表单参数
      form: {
      },
      // 表单校验
      rules: { startTime: [{ required: true, message: '日期不能为空', trigger: 'blur' }]
      }
    }
  },
  created() {
    if (this.$route.query !== undefined) {
      const startTime = this.$route.query.startTime
      const router = this.$route.query.router
      const srv = this.$route.query.service
      this.queryParams.startTime = startTime
      this.queryParams.service = srv
      this.queryParams.router = router
    }
    this.getDicts('scan_router_type').then(response => {
      this.routerTypeOptions = response.data
    })
    this.getDicts('scan_status').then(response => {
      this.statusOptions = response.data
    })
    this.getDicts('scan_err_code').then(response => {
      this.errCodeOptions = response.data
    })
    this.getDicts('approver_cvdata_list').then((response) => {
      this.approver_list = response.data
    })
    this.getList()
  },
  methods: {
    /** 查询参数列表 */
    getList() {
      this.loading = true
      getScanRequestList(this.addDateRange(this.queryParams)).then(response => {
        this.scanRequestList = response.data.list
        this.total = response.data.count
        this.loading = false
      }, _ => {
        this.scanRequestList = []
        this.total = 0
        this.loading = false
      }
      )
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined
      }
      this.resetForm('form')
    },
    statusFormat(row) {
      return this.selectDictLabel(this.statusOptions, row.status_code)
    },
    errShowMsgClick() {
      this.showErrMsgs = !this.showErrMsgs
    },
    formatDate(row, column) {
      const data = row[column.property]
      if (data == null) {
        return ''
      }
      return data.split('+')[0]
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageIndex = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.jobID = undefined
      this.queryParams.startTime = undefined
      this.queryParams.errMsg = undefined
      this.queryParams.router = undefined
      this.queryParams.statusCode = undefined
      this.queryParams.errCode = undefined
      this.queryParams.service = undefined
      this.resetForm('queryForm')
      this.handleQuery()
    }
  }
}
</script>
