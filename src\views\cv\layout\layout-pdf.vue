<template>
  <!-- 创建一个卡片式的容器，使用主题内容区域将背景色统一 -->
  <el-card>
    <!-- 标题插槽：显示使用说明 -->
    <div slot="header">
      <CVHint>使用说明：（PDF转docx/xlsx/pptx/kdc）选择一个PDF文件，系统会对文件进行处理，点击“下载结果”获取处理的效果</CVHint>
    </div>

    <!-- 主体内容 -->
    <div class="card-content">
      <div>
        <!-- <CVHint>PDF转docx/xlsx/pptx：选择一个PDF文件，系统会对文件进行处理，点击“下载结果”获取处理的效果</CVHint> -->
        <div class="container">
          <el-row type="flex" justify="center" :gutter="20">
            <el-col :span="5" class="grid-content">
              <div class="params-container" style="width: 100% !important">
                <div class="params">
                  <span style="color:blue"> {{ "版本信息：" }}</span>
                </div><br>
                <div class="params">
                  <span v-show="remark === ''" style="color:darkblue"> {{ "已是最新效果。" }}</span>
                </div>
                <div class="params">
                  <!-- 不同版本对应的提示语 -->
                  <span style="color:darkblue"> {{ remark }}</span>
                </div>
              </div>

              <!-- 展示获取到的文件信息 -->
              <br><div>
                <div class="params">
                  <span v-show="form.getFileMeta === true && result.classifier !== '' && result.classifier !== undefined" style="color:blue"> {{ "获取的文件信息：" }}</span>
                </div>
                <div>
                  <ul v-show="form.getFileMeta === true && result.classifier !== '' && result.classifier !== undefined" class="list-item">
                    <li style="color:darkblue"> {{ "分类器：" }}</li>
                    <p style="color:black"> {{ result.classifier }}</p>
                    <li style="color:darkblue"> {{ "关键词：" }}</li>
                    <p v-if="result.keyWords > 0" style="color:black"> {{ result.keyWords }}</p>
                    <p v-else style="color:black"> {{ "未提取到关键词。" }}</p>
                    <li style="color:darkblue"> {{ "总结：" }}</li>
                    <p style="color:black"> {{ result.summary }}</p>
                  </ul>
                </div><br>
              </div>
            </el-col>

            <el-col :span="8" class="grid-content">
              <span style="color:blue"> {{ "参数设置：" }}</span>
              <div class="upload-container">
                <div class="params-container" style="width: 100% !important">
                  <!-- <div class="params">
                    <span
                      class="param-label"
                      style="width: 25% !important"
                    >url :</span>
                    <el-input
                      v-model="url.value"
                      class="param-input"
                      style="width: 75% !important"
                      type="text"
                      @change="urlChange"
                    />
                  </div> -->

                </div>
                <div class="params-container">
                  <div class="params">
                    <span class="param-label">版本 :</span>
                    <el-select
                      v-model="startPage.version"
                      class="param-input"
                      placeholder="版本"
                      size="small"
                      @change="selectVersion"
                    >
                      <el-option
                        v-for="dict in versions"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                        @click.native="handleSelect(dict)"
                      />
                    </el-select>
                  </div>
                  <div class="params">
                    <span class="param-label">dpi :</span>
                    <el-input
                      v-model="dpi.value"
                      class="param-input"
                      type="number"
                      :min="dpi.min"
                      :max="dpi.max"
                      @change="dpiChange"
                    />
                  </div>
                  <div class="params">
                    <span class="param-label">起始 :</span>
                    <el-input
                      v-model="startPage.value"
                      class="param-input"
                      type="number"
                      :min="startPage.min"
                      :max="startPage.max"
                      @change="startPageChange"
                    />
                  </div>
                  <div class="params">
                    <span class="param-label">终止 :</span>
                    <el-input
                      v-model="endPage.value"
                      class="param-input"
                      type="number"
                      :min="endPage.min"
                      :max="endPage.max"
                      @change="endPageChange"
                    />
                  </div>
                  <div class="params">
                    <span class="param-label">导出类型 :</span>
                    <el-select
                      v-model="startPage.exporttype"
                      class="param-input"
                      placeholder="导出类型"

                      size="small"
                    >
                      <el-option
                        v-for="dict in exporttypeOptions"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </div>
                </div>

                <div class="params-container" style="width: 100% !important">
                  <!-- <div class="params">
                    <span
                      class="param-label"
                      style="width: 25% !important"
                    >url of pdf :</span>
                    <el-input
                      v-model="fileUrl"
                      class="param-input"
                      style="width: 75% !important"
                      type="text"
                    />
                  </div> -->
                  <br><div class="params">
                    <el-button
                      type="warning"
                      size="mini"
                      @click="handleSetting"
                    >高级设置</el-button>
                    <el-tooltip content="切换输入方式" placement="right-end">
                      <el-select v-model.number="choiceInput" placeholder="默认上传文件">
                        <el-option
                          v-for="dict in choiceInputOptions"
                          :key="dict.value"
                          :label="dict.label"
                          :value="dict.value"
                        />
                      </el-select>
                    </el-tooltip>
                    <!-- <el-button
                      type="primary"
                      size="mini"
                      @click="pdf2wordconvertclick"
                    >转换</el-button> -->
                  </div>
                </div>

                <hr>
                <div v-if="choiceInput=='inputUrl'">
                  <br><span style="color:blue"> {{ "输入PDF链接：" }}</span><br>
                  <div class="params">
                    <span
                      class="param-label"
                      style="width: 25% !important"
                    >url of pdf :</span>
                    <el-input
                      v-model="fileUrl"
                      class="param-input"
                      style="width: 75% !important"
                      type="text"
                    /><br>
                  </div>
                  <el-button
                    type="primary"
                    size="mini"
                    @click="pdf2wordconvertclick"
                  >开始转换</el-button>
                </div>

                <!-- 转换或点击上传前高级设置对话框 -->
                <el-dialog :title="title" :visible.sync="open" width="650px">
                  <el-form ref="form" :model="form" :rules="rules" label-width="150px" inline-message>
                    <el-row v-show="startPage.exporttype !== 'pdf_export'">
                      <el-col :span="12">
                        <!-- <el-form-item label="最大并发页:" prop="maxConNumPage">
                          <el-input v-model.number="form.maxConNumPage" placeholder="请输入最大并发页" />
                        </el-form-item> -->
                        <el-tooltip content="最大并发页" placement="left-end">
                          <template slot="default">
                            <el-form-item label="maxConNumPage:" prop="maxConNumPage">
                              <el-input v-model.number="form.maxConNumPage" placeholder="请输入maxConNumPage" />
                            </el-form-item>
                          </template>
                        </el-tooltip>
                      </el-col>

                      <el-col :span="12">
                        <el-form-item label="PDFDebugFlags:" prop="pdfDebugFlags">
                          <el-input v-model.number="form.pdfDebugFlags" placeholder="请输入PDFDebugFlags, 默认值为0" />
                        </el-form-item>
                      </el-col>
                    </el-row>

                    <el-row v-show="startPage.exporttype !== 'pdf_export'">
                      <el-col :span="12">
                        <el-tooltip content="只转换为图片" placement="left-end">
                          <template slot="default">
                            <el-form-item label="justToImg:">
                              <el-radio-group v-model="form.justToImg">
                                <el-radio
                                  v-for="dict in justConvertToImgOptions"
                                  :key="dict.value"
                                  :label="dict.value"
                                >{{ dict.label }}</el-radio>
                              </el-radio-group>
                            </el-form-item>
                          </template>
                        </el-tooltip>
                      </el-col>
                      <el-col :span="12">
                        <el-tooltip content="获取文件信息" placement="right-end">
                          <template slot="default">
                            <el-form-item label="getFileMeta:">
                              <el-radio-group v-model="form.getFileMeta">
                                <el-radio
                                  v-for="dict in getFileMetaOptions"
                                  :key="dict.value"
                                  :label="dict.value"
                                >{{ dict.label }}</el-radio>
                              </el-radio-group>
                            </el-form-item>
                          </template>
                        </el-tooltip>
                      </el-col>
                    </el-row>

                    <el-row v-show="startPage.exporttype !== 'pdf_export'">
                      <el-col :span="12">
                        <el-tooltip content="是否处理混合件" placement="left-end">
                          <template slot="default">
                            <el-form-item label="switchMix:">
                              <el-radio-group v-model="form.switchMix">
                                <el-radio
                                  v-for="dict in switchMixOptions"
                                  :key="dict.value"
                                  :label="dict.value"
                                >{{ dict.label }}</el-radio>
                              </el-radio-group>
                            </el-form-item>
                          </template>
                        </el-tooltip>
                      </el-col>
                      <el-col :span="12">
                        <el-tooltip content="是否使用扫描模式" placement="right-end">
                          <template slot="default">
                            <el-form-item label="isScan:">
                              <el-radio-group v-model="form.isScan">
                                <el-radio
                                  v-for="dict in isScanOptions"
                                  :key="dict.value"
                                  :label="dict.value"
                                >{{ dict.label }}</el-radio>
                              </el-radio-group>
                            </el-form-item>
                          </template>
                        </el-tooltip>
                      </el-col>
                    </el-row>

                    <el-row v-show="startPage.exporttype !== 'pdf_export'">
                      <el-col :span="12">
                        <el-tooltip content="输出图片类型" placement="left-end">
                          <template slot="default">
                            <el-form-item label="imgType:">
                              <el-select v-model="form.imgType" placeholder="请选择">
                                <el-option
                                  v-for="dict in imgTypeOptions"
                                  :key="dict.value"
                                  :label="dict.label"
                                  :value="dict.value"
                                />
                              </el-select>
                            </el-form-item>
                          </template>
                        </el-tooltip>
                      </el-col>
                    </el-row>

                    <!-- pdf2kdc -->
                    <el-row v-show="startPage.exporttype === 'pdf_export'">
                      <el-col :span="24">
                        <el-tooltip content="需要得到的元素" placement="left-end">
                          <template slot="default">
                            <el-form-item label="include_elements:">
                              <el-select v-model="form.include_elements" placeholder="请选择" multiple @change="$forceUpdate()">
                                <el-option
                                  v-for="dict in includeElementsOptions"
                                  :key="dict.value"
                                  :label="dict.label"
                                  :value="dict.value"
                                />
                              </el-select>
                            </el-form-item>
                          </template>
                        </el-tooltip>
                      </el-col>
                    </el-row>

                    <!-- <el-row>
                      <el-col :span="24">
                        <el-form-item label="其他参数设置:">
                          <el-input placeholder="敬请期待" disabled />
                        </el-form-item>
                      </el-col>
                    </el-row> -->
                  </el-form>
                  <div slot="footer" class="dialog-footer">
                    <el-button class="reset-button" type="warning" @click="reset">重 置</el-button>
                    <div class="right-buttons">
                      <el-button class="confirm-button" type="primary" @click="submitForm">确 定</el-button>
                      <el-button class="cancel-button" @click="cancel">取 消</el-button>
                    </div>
                  </div>
                </el-dialog>

                <!-- 新增转化结果 -->
                <!-- <div class="params-container">
                  <br><div v-show="fileName !== ''" class="params">
                    <span style="color:blue" class="param-label">{{ '转化结果' }} :</span>
                  </div>
                  <div v-show="fileName !== ''" class="params">
                    <span class="param-label">{{ fileName }} :</span>
                    <span class="param-input">
                      {{ fileSize }}
                    </span>
                  </div>
                  <div v-show="statistic.status !== -1" class="params">
                    <span class="param-label">处理用时 :</span>
                    <span class="param-input">
                      {{ formattedDuration }}
                    </span>
                  </div>
                  <div v-show="statistic.status !== -1" class="params">
                    <span
                      v-show="statistic.status === 1"
                      class="param-label"
                      style="color:blue"
                    >转化成功!</span>
                    <span
                      v-show="statistic.status === 0"
                      class="param-label"
                      style="color: #ff0000"
                    >转化失败!</span>
                  </div>
                </div> -->

                <div v-if="choiceInput=='uploadFile'">
                  <br><span style="color:blue"> {{ "上传PDF文件：" }}</span>
                  <el-upload
                    v-loading="loading"
                    element-loading-text="文件处理中"
                    class="uploader"
                    list-type="text"
                    accept=".pdf"
                    :multiple="false"
                    :show-file-list="false"
                    :file-list="fileList"
                    :http-request="pdf2wordUpload"
                    :on-success="onSuccess"
                    action="placeholder"
                    drag
                  >
                    <i class="el-icon-upload" />
                    <div class="el-upload__text">
                      将文件拖到此处，或
                      <el-button type="text">点击上传</el-button>
                    </div>
                  </el-upload>

                  <el-progress
                    v-show="percentage !== 0 && percentage !== 100"
                    :text-inside="true"
                    :stroke-width="14"
                    :percentage="percentage"
                  />
                </div>

                <br>
                <div v-show="fileName !== ''"><hr>
                  <div class="params-container">
                    <div><span style="color:darkblue"> {{ '转化成功后的结果在页面下方展示。' }}</span></div><br>
                    <div class="params">
                      <span style="color:blue" class="param-label">{{ '转化结果' }} :</span>
                    </div>
                    <div v-show="fileName !== ''" class="params">
                      <span class="param-label">{{ fileName }} :</span>
                      <span class="param-input">
                        {{ fileSize }}
                      </span>
                    </div>
                    <div v-show="statistic.status !== -1" class="params">
                      <span class="param-label">处理用时 :</span>
                      <span class="param-input">
                        {{ formattedDuration }}
                      </span>
                    </div>
                    <div v-show="statistic.status !== -1" class="params">
                      <span
                        v-show="statistic.status === 1"
                        class="param-label"
                        style="color:blue"
                      >转化成功!</span><br>
                      <span
                        v-show="statistic.status === 0"
                        class="param-label"
                        style="color: #ff0000"
                      >转化失败!</span><br>
                    </div>
                    <div v-show="statistic.status !== -1" class="params">
                      <div v-show="startPage.exporttype === 'pdf_export' && showMarkDown">
                        <el-button v-show="showJsonData" type="text" size="small" @click="showJson">复制 pdf2kdc 得到的 json数据</el-button>
                        <br><router-link :to="{ name: 'showJsonResult', params: { data: JSON.stringify(markDownData) }}">
                          <el-button type="success" size="mini">跳转 kdc2md 结果页面</el-button>
                        </router-link>
                      </div>
                    </div>
                  </div>
                </div>

              </div>
            </el-col>
            <el-col :span="5" class="grid-content">
              <CVDemo
                :thumbnail="true"
                :loading="loading"
                :data="urlFiles[0]"
                @click="pdf2word(0, $event)"
              >示例:</CVDemo>
            </el-col>
            <el-col :span="5" class="grid-content">
              <!-- 80px(div+margin)*4+1px(border)*4 -->
              <CVDemo
                :thumbnail="true"
                :loading="loading"
                :data="urlFiles[1]"
                @click="pdf2word(1, $event)"
              >复杂示例:</CVDemo>
            </el-col>
          </el-row>
          <el-row
            v-show="
              percentage === 0 || percentage === 100
            "
            type="flex"
            justify="space-around"
          >
            <el-col v-show="result.pdfUrl !== ''" :span="4" class="grid-content">
              <div>
                <a :href="result.pdfUrl" target="_blank" download="">
                  <img src="@/assets/cv/pdf.png" style="width: 100%">
                </a>
                <div>中间件转换成功，点击图标查看</div>
              </div>
            </el-col>
            <el-col v-show="result.wordUrl !== ''" :span="4" class="grid-content">
              <div>
                >{{ result.wordUrl }}>
                <a :href="result.wordUrl" target="_blank" download="">
                  <img src="@/assets/cv/word.png" style="width: 100%">
                </a>
                <div>solid输出成功，点击图标下载</div>
              </div>
            </el-col>
            <el-col v-show="result.wordUrlKs !== ''" :span="4" class="grid-content">
              <div>
                <a :href="result.wordUrlKs" target="_blank" download="">
                  <!-- <img src="@/assets/cv/word.png" style="width: 100%"> -->
                  <img :src="result.exporttype" style="width: 100%">
                </a>
                <div>PDF自研工具输出成功，点击图标下载</div>
              </div>
            </el-col>
          </el-row>
          <el-row v-show="percentage === 0 || percentage === 100" :gutter="20">
            <el-col
              v-show="result.opTimes[0] !== 0"
              :span="24"
              class="grid-content"
            >
              <ul>
                <li v-for="(errMsg, index) in result.errMsgs" :key="index">
                  {{ errMsg }}
                </li>
              </ul>
              <table
                border="solid"
                cellspacing="0"
                cellpadding="10"
                frame="solid"
                rules="solid"
                style="width: 100%"
              >
                <thead>
                  <tr align="center" valign="center" bgcolor="#e5e9f2">
                    <!-- <tr align="center" valign="center"> -->
                    <th>序号</th>
                    <th>类型</th>
                    <th>耗时/s</th>
                    <th>序号</th>
                    <th>类型</th>
                    <th>耗时/s</th>
                  </tr>
                </thead>
                <tbody v-if="result !== undefined && result.ops !== undefined" align="center" valign="center">
                  <tr>
                    <td>1</td>
                    <td>{{ result.ops[0] }}</td>
                    <td>{{ result.opTimes[1] }}</td>
                    <td>2</td>
                    <td>{{ result.ops[1] }}</td>
                    <td>{{ result.opTimes[2] }}</td>
                  </tr>
                  <tr>
                    <td>3</td>
                    <td>{{ result.ops[2] }}</td>
                    <td>{{ result.opTimes[3] }}</td>
                    <td>4</td>
                    <td>{{ result.ops[3] }}</td>
                    <td>{{ result.opTimes[4] }}</td>
                  </tr>
                  <tr>
                    <td>5</td>
                    <td>{{ result.ops[4] }}</td>
                    <td>{{ result.opTimes[5] }}</td>
                    <td>6</td>
                    <td>{{ result.ops[5] }}</td>
                    <td>{{ result.opTimes[6] }}</td>
                  </tr>
                  <tr>
                    <td>7</td>
                    <td>{{ result.ops[6] }}</td>
                    <td>{{ result.opTimes[7] }}</td>
                    <td>8</td>
                    <td>{{ result.ops[7] }}</td>
                    <td>{{ result.opTimes[8] }}</td>
                  </tr>
                  <tr>
                    <td>9</td>
                    <td>{{ result.ops[8] }}</td>
                    <td>{{ result.opTimes[9] }}</td>
                    <td>10</td>
                    <td>{{ result.ops[9] }}</td>
                    <td>{{ result.opTimes[10] }}</td>
                  </tr>
                  <!-- <tr>
                    <td>9</td>
                    <td>pdf2word</td>
                    <td>{{ result.opTimes[9] }}</td>
                    <td>10</td>
                    <td>/</td>
                    <td>{{ result.opTimes[10] }}</td>
                  </tr> -->
                </tbody>
              </table>
            </el-col>
          </el-row>
        </div>
      </div>
    </div>

    <!-- 底部插槽：未使用，后续若是扩展可以考虑翻页等功能 -->
    <div slot="footer">
      Card Footer
    </div>
  </el-card>
</template>

<script>
import axios from 'axios'
import CVHint from './components/CVHint'
import CVDemo from './components/CVDemo'
// import CVShowJson from './components/CVShowJson'
import CryptoJS from 'crypto-js'
import {
  urlRegex,
  formatfileSize,
  isValidFile,
  convertOpTimes
} from '@/api/cv/utils'
import { axios_wait_doc } from '@/api/cv/request'
import {
  httpRequesSignKs,
  uploadKs3Store,
  pdf2WordCreateUrl,
  kdc2md,
  fetchUrl2Ks
} from '@/api/cv/request-ks3'
import { getHeaders } from '@/utils/get-header'
import { sleep } from '@/api/cv/utils'
// import { VueJsonViewer } from 'vue-json-viewer'

export default {
  name: 'LayoutPDF',
  // components: { CVHint, CVDemo, VueJsonViewer },
  // components: { CVHint, CVDemo, CVShowJson },
  components: { CVHint, CVDemo },
  props: {
    maxFileNum: {
      validator(value) {
        if (value <= 0) {
          return 1
        }
        return value
      },
      default: 1
    },
    fileType: {
      type: Array,
      default: function() {
        return ['.pdf']
      }
    }
  },
  data() {
    return {
      // 输入方式字典
      choiceInputOptions: [
        { label: '上传PDF文件(默认)', value: 'uploadFile' },
        { label: '输入PDF链接', value: 'inputUrl' }
      ],
      choiceInput: 'uploadFile',
      // 弹出层标题（用户导入）
      title: '',
      // 是否显示弹出层（用户导入）
      open: false,
      // 输出图片类型字典
      imgTypeOptions: [
        { label: 'JPG', value: 'jpg' },
        { label: 'PNG', value: 'png' }
      ],
      // pdf2kdc转化后需要得到的元素
      includeElementsOptions: [
        { label: 'textbox', value: 'textbox' },
        { label: 'para', value: 'para' },
        { label: 'table', value: 'table' },
        { label: 'component', value: 'component' },
        { label: 'all', value: 'all' }
      ],
      // 表单参数
      form: {
        maxConNumPageInt: 18,
        maxConNumPage: 18,
        imgType: 'png',
        justToImg: false,
        pdfDebugFlags: 0,
        switchMix: true,
        getFileMeta: false,
        isScan: false,
        include_elements: ['all']
      },
      // 参数
      maxConNumPageInt: 18,
      maxConNumPage: 18,
      imgType: 'png',
      justToImg: false,
      pdfDebugFlags: 0,
      switchMix: true,
      isScan: false,
      getFileMeta: false,
      include_elements: ['all'],
      // 是否只将pdf转为图片字典
      justConvertToImgOptions: [
        { label: '是', value: true },
        { label: '否', value: false }
      ],
      // 是否获取文件信息
      getFileMetaOptions: [
        { label: '是', value: true },
        { label: '否', value: false }
      ],
      // 是否处理混合件
      switchMixOptions: [
        { label: '是', value: true },
        { label: '否', value: false }
      ],
      // 是否使用扫描模式
      isScanOptions: [
        { label: '是', value: true },
        { label: '否', value: false }
      ],
      // 表单校验
      rules: {
        // 定义规则对象
        // trigger: 'blur' 表示在失去焦点时触发验证
        maxConNumPage: [{ type: 'number', message: '最大并发页必须为int类型', trigger: ['blur', 'change'] }],
        pdfDebugFlags: [{ type: 'number', message: 'PDFDebugFlags必须为int类型', trigger: ['blur', 'change'] }]
      },
      loading: false,
      url: {
        value: '//pcv-test.wps.cn',
        default: '//pcv-test.wps.cn'
      },
      fileUrl: '',
      remark: '',
      dpi: {
        value: 144,
        default: 144,
        min: 1,
        max: 400
      },
      startPage: {
        value: 0,
        default: 0,
        min: 0,
        max: 10,
        exporttype: 'pdf2docx',
        version: ''
      },
      endPage: {
        value: 0,
        default: 0,
        min: 0,
        max: 10
      },
      uri: '/create',
      fileObj: {
        name: '',
        size: 0
      },
      fileList: [],
      exporttypeOptions: [],
      versions: [],
      result: {
        wordUrl: '',
        duration: 0,
        pdfUrl: '',
        wordUrlKs: '',
        opTimes: Array(11).fill(0), // 时间戳数组
        ops: Array(11).fill(0), // opTimes对应的类型
        errMsgs: [],
        classifier: '',
        keyWords: [],
        summary: ''
      },
      // json相关
      showJsonData: false,
      jsonData: {},
      // kdc相关
      showMarkDown: false,
      markDownData: [],
      statistic: {
        status: -1,
        duration: 0
      },
      percentage: 0,
      // kdc返回的json数据
      kdcJsonData: {},
      theme: 'dark',
      urlFiles: [
        [
          {
            url: 'http://zhai-datas.ks3-cn-beijing.ksyun.com/layout/people/luhaoxian/standard/normaltable.pdf',
            name: '常规表格.pdf',
            size: '96925'
          },
          {
            url: 'http://zhai-datas.ks3-cn-beijing.ksyun.com/layout/people/luhaoxian/standard/normaldoc.pdf',
            name: '常规公文.pdf',
            size: '384683'
          },
          {
            url: 'http://zhai-datas.ks3-cn-beijing.ksyuncs.com/layout/people/wangluoyan/demo_sample/markdown/cleandoc.pdf',
            name: '干净论文.pdf',
            size: '6735'
          },
          {
            url: 'http://zhai-datas.ks3-cn-beijing.ksyun.com/layout/people/luhaoxian/standard/english.pdf',
            name: '英文论文.pdf',
            size: '170405'
          }
        ],
        [
          {
            url: 'http://zhai-datas.ks3-cn-beijing.ksyun.com/layout/people/luhaoxian/standard/complex/1.pdf',
            name: '示例一',
            size: '60380'
          },
          {
            url: 'http://zhai-datas.ks3-cn-beijing.ksyun.com/layout/people/luhaoxian/standard/complex/2.pdf',
            name: '示例二',
            size: '76539'
          },
          {
            url: 'http://zhai-datas.ks3-cn-beijing.ksyun.com/layout/people/luhaoxian/standard/complex/3.pdf',
            name: '示例三',
            size: '82459'
          },
          {
            url: 'http://zhai-datas.ks3-cn-beijing.ksyun.com/layout/people/luhaoxian/standard/complex/4.pdf',
            name: '示例四',
            size: '237141'
          }
        ]
      ]
    }
  },
  computed: {
    formattedDuration() {
      return this.statistic.duration.toFixed(3) + 's'
    },
    fileName() {
      const { name } = this.fileObj
      return name.trim()
    },
    fileSize() {
      const { size } = this.fileObj
      return formatfileSize(size)
    }
  },
  // 通过watch监听输入值的变化，并将maxConNumPage/pdfDebugFlags由 string类型转为 int类型
  watch: {
    'form.maxConNumPage'(val) {
      this.maxConNumPageInt = parseInt(val)
    },
    'form.pdfDebugFlags'(val) {
      this.pdfDebugFlagsInt = parseInt(val)
    }
  },
  created() {
    this.getDicts('pdf_export_type').then(response => {
      this.exporttypeOptions = response.data
    })
    this.getDicts('pdf_convert_version').then(response => {
      this.versions = response.data
      if (this.versions.length > 0) {
        this.url.value = this.versions[0].value
        this.startPage.version = this.versions[0].label
        this.remark = this.versions[0].remark
      }
    })
  },
  methods: {
    /** 提交按钮 */
    submitForm: function() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.maxConNumPage = this.form.maxConNumPage
          this.imgType = this.form.imgType
          this.justToImg = this.form.justToImg
          this.pdfDebugFlags = this.form.pdfDebugFlags
          this.switchMix = this.form.switchMix
          this.getFileMeta = this.form.getFileMeta
          this.isScan = this.form.isScan
          this.include_elements = this.form.include_elements
          this.open = false
        }
      })
    },
    // 取消按钮
    cancel() {
      this.form.maxConNumPage = this.maxConNumPage
      this.form.imgType = this.imgType
      this.form.justToImg = this.justToImg
      this.form.pdfDebugFlags = this.pdfDebugFlags
      this.form.switchMix = this.switchMix
      this.form.getFileMeta = this.getFileMeta
      this.form.isScan = this.isScan
      this.form.include_elements = this.include_elements
      this.open = false
      // this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        maxConNumPageInt: 18,
        maxConNumPage: 18,
        imgType: 'png',
        justToImg: false,
        pdfDebugFlags: 0,
        switchMix: true,
        getFileMeta: false,
        isScan: false,
        include_elements: ['all']
      }
      this.resetForm('form')
    },
    /** 高级设置按钮操作 */
    handleSetting() {
      // this.reset()

      this.open = true
      this.title = '高级设置'
      this.form.password = ''
    },
    progress(percentage) {
      this.percentage = percentage
    },
    handleSelect(item) {
      this.remark = item.remark
    },
    selectVersion(value) {
      this.url.value = value
    },
    // 上传
    uploadKs(file) {
      var url = process.env.VUE_APP_BASE_API + '/layout/signature'
      const axios_config_upload3 = {
        method: 'get',
        //  headers: { 'Content-Type': 'multipart/form-data' },
        url: url
        //  data: formData
      }
      axios(axios_config_upload3).then((res) => {
        const formData = new FormData()
        formData.append('acl', 'public-read')
        formData.append('key', 'layout_test/godemo/tmp/weiwei')
        formData.append('Signature', res.data.data.signature)
        formData.append('KSSAccessKeyId', 'ddddxxxxxxx')
        // eslint-disable-next-line no-undef
        formData.append('Policy', 'ddd')
        formData.append('file', file)

        const axios_config_upload2 = {
          method: 'post',
          headers: {
            'Content-Type': 'multipart/form-data',
            ...getHeaders()
          },
          url: 'https://zhai-datas.ks3-cn-beijing.ksyun.com/chenzhiwei3',
          data: formData
        }
        axios(axios_config_upload2).then(async(res) => {
          console.log('res: ', res)
        })
      })
    },
    validateUploadPDFUrl(fileurl) {
      if (fileurl.trim() === '') {
        return false
      }
      if (!urlRegex.test(fileurl)) {
        return false
      }
      return true
    },
    getUrlFileName(url) {
      const o = url.lastIndexOf('/')
      if (o > 0) {
        const e = url.lastIndexOf('?')
        if (e > o) return url.substring(o + 1, e)
        else return url.substring(o + 1)
      } else {
        this.message('error', '名字异常url of pdf! ', '')
        return false
      }
    },
    /** 转换按钮操作 */
    async pdf2wordconvertclick() {
      if (!this.validateUploadPDFUrl(this.fileUrl)) {
        this.message('error', '请输入正确文件地址url of pdf! ', '')
        return false
      }

      this.loading = true

      if (this.fileUrl.lastIndexOf('ksyun.com') > 0) {
        // 新增 maxConNumPage、imageurl、justToImg、textUnify、pdfDebugFlags
        // 调用 pdf2WordCreateUrl方法，传递参数，并返回Promise对象，等待执行完成
        const res_create_url_data = await pdf2WordCreateUrl(
          this.url.value,
          this.fileUrl,
          this.startPage.value,
          this.endPage.value,
          this.startPage.exporttype,
          this.uri,
          this.maxConNumPageInt,
          this.imgType,
          this.justToImg,
          this.pdfDebugFlagsInt,
          this.dpi.value,
          this.switchMix,
          this.getFileMeta,
          this.isScan,
          this.include_elements
        )

        if (res_create_url_data.status === 200) {
          // 等待1s
          await sleep(1000)
          const docID = res_create_url_data.data.data['docID']
          const s_time = new Date().getTime()
          await this.waitDocStatus(docID)

          const e_time = new Date().getTime()
          this.statistic = {
            status: 1,
            duration: this.result.duration || (e_time - s_time) / 1000
          }
          this.loading = false
          return Promise.resolve()
        }
      } else {
        this.fetchUrl2Ks3(this.fileUrl)
      }
      this.loading = false
    },
    async fetchUrl2Ks3(sourceurl) {
      const res = await fetchUrl2Ks(this, sourceurl)

      if (res.data.message === 'ok') {
        const ksurl = res.data.url
        const res_create_url_data = await pdf2WordCreateUrl(
          this.url.value,
          ksurl,
          this.startPage.value,
          this.endPage.value,
          this.startPage.exporttype
        )

        if (res_create_url_data.status === 200) {
          // 等待1s
          await sleep(1000)
          const docID = res_create_url_data.data.data['docID']
          const s_time = new Date().getTime()
          await this.waitDocStatus(docID)

          const e_time = new Date().getTime()
          this.statistic = {
            status: 1,
            duration: this.result.duration || (e_time - s_time) / 1000
          }
          this.loading = false
          return Promise.resolve(0)
        }
      } else {
        this.message('error', '错误信息: ', res.data.msg)
        return Promise.resolve(0)
      }
      // })
    },
    pdf2wordconvertclickbak() {
      if (!this.validateUploadPDFUrl(this.fileUrl)) {
        this.message('error', '请输入正确文件地址url of pdf! ', '')
        return false
      }

      this.loading = true
      var url = process.env.VUE_APP_BASE_API + '/layout/download'
      let blob
      const filename = this.getUrlFileName(this.fileUrl)
      const uploadform2 = new FormData()
      uploadform2.append('fileurl', this.fileUrl)
      const axios_config_upload2 = {
        method: 'post',
        headers: { 'Cache-Control': 'no-cache', ...getHeaders() },
        url: url,
        data: uploadform2,
        responseType: 'blob',
        processData: false, // 必须
        contentType: false
      }

      return axios(axios_config_upload2)
        .then(async(res) => {
          console.log(res)
          if (res.data.type !== 'application/pdf') {
            console.log(res)
            this.loading = false
            this.message('error', '文件处理失败! ', res.msg)
            return Promise.resolve(0)
          }

          blob = new Blob([res.data], { type: 'application/pdf' })

          const uploadform = new FormData()
          uploadform.append('pdf', blob)
          const axios_config_upload = {
            method: 'post',
            headers: { 'Cache-Control': 'no-cache', ...getHeaders() },
            url: process.env.VUE_APP_BASE_API + '/layout/uploadks/' + filename,
            data: uploadform,
            responseType: 'json',
            processData: false, // 必须
            contentType: false
          }

          this.httpUploadRequest(axios_config_upload)

          const form = new FormData()
          form.append('pdf', blob)
          form.append('textUnify', 1)
          form.append('dpi', this.dpi.value)
          form.append('pdfDebugFlags', 0)
          form.append('PageNumBegin', this.startPage.value)
          form.append('PageNumEnd', this.endPage.value)
          form.append('wordConvertTool', 3)

          const axios_config_get_docID = {
            method: 'post',
            headers: { 'Cache-Control': 'no-cache', ...getHeaders() },
            url: this.url.value + '/layout/scheduler/' + this.startPage.exporttype + '/upload',
            data: form,
            responseType: 'json',
            processData: false, // 必须
            contentType: false
          }

          const s_time = new Date().getTime()
          const status = await this.httpRequest(axios_config_get_docID)
          if (status === 1) {
            // param.onSuccess()
          }
          // 结束加载
          this.loading = false
          const e_time = new Date().getTime()
          this.statistic = {
            status,
            duration: this.result.duration || (e_time - s_time) / 1000
          }
          return Promise.resolve()
        })
        .catch((error) => {
          this.loading = false
          this.message('error', '文件处理失败2! ', error)
          return Promise.resolve(0)
        })
    },
    handleOpTimes(times, names) {
      this.result.ops = names
      // 1.时间戳转换
      const opTimes = this.result.opTimes
      convertOpTimes(opTimes, times)
      // opTimes.fill(0) // 存放时间戳
      // for (let i = 0; i < times.length; i++) {
      //   if (i === 0) {
      //     opTimes.splice(i, 1, times[i] / 1000)
      //   }
      //   if (i > 0) {
      //     // let _t = (timeStamp[i] - timeStamp[i-1]) / 1000;
      //     opTimes.splice(i, 1, (times[i] - times[i - 1]) / 1000)
      //   }
      // }
    },
    urlChange(value) {
      // url 检测
      if (urlRegex.test(value)) {
        this.url.value = value
      } else {
        this.url.value = this.url.default
      }
    },
    numberInputChange(obj, value) {
      if (value === '') {
        obj.value = obj.default
      } else if (value > obj.max) {
        obj.value = obj.max
      } else if (value < obj.min) {
        obj.value = obj.min
      }
      obj.value = parseInt(value)
    },
    dpiChange(value) {
      this.numberInputChange(this.dpi, value)
    },
    startPageChange(value) {
      this.numberInputChange(this.startPage, value)
    },
    endPageChange(value) {
      this.numberInputChange(this.endPage, value)
    },
    fileChange(file, fileList) {
      this.fileList = fileList.slice(-this.maxFileNum)
    },
    onSuccess(response, file, fileList) {
      // hook
      this.fileChange(file, fileList)
    },
    resetResult() {
      this.percentage = 0
      this.result = {
        wordUrl: '',
        duration: 0,
        pdfUrl: '',
        wordUrlKs: '',
        opTimes: Array(11).fill(0), // 时间戳数组
        errMsgs: [],
        exporttype: '',
        classifier: '',
        keyWords: [],
        summary: ''
      }
      this.statistic = {
        status: -1,
        duration: 0
      }
    },
    beforeUpload(file) {
      // 清理旧结果
      this.fileObj = { name: '', size: 0 }
      this.resetResult()

      // 文件类型判断
      const { name } = file
      var ext = '.' + name.trim().split('.').pop()
      ext = ext.toLowerCase()
      if (!isValidFile(ext, this.fileType)) {
        this.message('error', '请选择pdf文件! ', '')
        return false
      } else {
        this.fileObj = file
        return true
      }
    },
    message(type, msg, dataMsg) {
      this.$message({
        showClose: true,
        message: msg + dataMsg,
        center: true,
        duration: 4000,
        type: type
      })
    },
    // 示例 和 复杂示例调用
    async pdf2word(arrayid, id) {
      console.log('array', arrayid, ' index', id)
      // 开始加载
      this.loading = true

      const file = this.urlFiles[parseInt(arrayid)][parseInt(id)]
      this.fileObj = { name: file.name, size: file.size }
      this.resetResult()

      const res_create_url_data = await pdf2WordCreateUrl(
        this.url.value,
        file.url,
        this.startPage.value,
        this.endPage.value,
        this.startPage.exporttype,
        this.uri,
        this.maxConNumPageInt,
        this.imgType,
        this.justToImg,
        this.pdfDebugFlagsInt,
        this.dpi.value,
        this.switchMix,
        this.getFileMeta,
        this.isScan,
        this.include_elements
      )

      if (res_create_url_data.status === 200) {
        // 等待1s
        await sleep(1000)
        const docID = res_create_url_data.data.data['docID']
        const s_time = new Date().getTime()
        await this.waitDocStatus(docID)

        const e_time = new Date().getTime()
        this.statistic = {
          status: 1,
          duration: this.result.duration || (e_time - s_time) / 1000
        }
        this.loading = false
        return Promise.resolve(0)
      }
    },
    httpUploadRequest(axios_config) {
      return axios(axios_config).then((res) => {
        console.log(res)
        if (res.status === 200) {
          console.log('上传成功')
        } else {
          return Promise.reject('转化请求失败!')
        }
      })
    },
    pdf2wordCreate(url) {
      const form = new FormData()
      const urls = []
      urls.push(url)
      console.log(urls)
      form.append('PageNumBegin', this.startPage.value)
      form.append('PageNumEnd', this.endPage.value)
      form.append('imgUrls', urls)
      const obj = {
        PageNumBegin: this.startPage.value,
        PageNumEnd: this.endPage.value,
        justConvertToImg: false,
        url: url
      }
      const json = JSON.stringify(obj)
      console.log(json)
      const axios_config_get_docID = {
        method: 'post',
        headers: { 'Cache-Control': 'no-cache', ...getHeaders() },
        url: this.url.value + '/layout/scheduler/pdf2docx',
        data: json,
        responseType: 'json',
        processData: false, // 必须
        contentType: false
      }
      axios(axios_config_get_docID).then((res) => {
        // console.log(res)
        const docID = res.data.data.docID
        axios_wait_doc({
          method: 'get',
          headers: { 'Cache-Control': 'no-cache', ...getHeaders() },
          url:
            this.url.value +
            '/layout/scheduler/query?docID=' +
            docID,
          handleProgress: this.progress
        }).then((res) => {
          // console.log(res)
          if (res === undefined) {
            console.log('undefined res')
            return Promise.resolve(-1)
          }
          // 处理完成
          // console.log('文件处理完成: ' + JSON.stringify(res))

          const { data } = res
          if (this.startPage.exporttype === 'pdf2pptx' || this.startPage.exporttype === 'pdf2xlsx') {
            this.result.exporttype = require('../../../assets/cv/' + this.startPage.exporttype + '.png')
          } else {
            this.result.exporttype = require('../../../assets/cv/word.png')
          }
          this.result.wordUrl =
            data.wordUrl !== undefined ? data.wordUrl.replace('http:', '') : ''
          this.result.duration = data.duration // 处理用时
          this.result.pdfUrl = data.pdfUrl !== undefined ? data.pdfUrl : ''
          this.result.wordUrlKs =
            data.wordUrlKs !== undefined
              ? data.wordUrlKs.replace('http:', '')
              : ''
          this.handleOpTimes(data.opTimes, data.ops)

          if (data.errMsgs !== undefined && data.errMsgs.length > 0) {
            this.result.errMsgs = data.errMsgs
            return Promise.reject('详情查看错误信息')
          } else {
            return Promise.resolve(1)
          }
        })
      })
    },
    async waitDocStatus(docID) {
      // const docID = res_create_url_data.data.data['docID']

      await axios_wait_doc({
        method: 'get',
        headers: { 'Cache-Control': 'no-cache', ...getHeaders() },
        url:
          this.url.value + '/layout/scheduler/query?docID=' + docID,
        handleProgress: this.progress
      }).then(async(res) => {
        if (res === undefined) {
          console.log('undefined res')
          return Promise.resolve(-1)
        }

        const { data } = res
        if (this.startPage.exporttype === 'pdf2pptx' || this.startPage.exporttype === 'pdf2xlsx') {
          this.result.exporttype = require('../../../assets/cv/' + this.startPage.exporttype + '.png')
        } else if (this.startPage.exporttype === 'pdf_export') {
          await this.kdc2Markdown(data)
        } else {
          this.result.exporttype = require('../../../assets/cv/word.png')
        }
        this.result.wordUrl =
          data.wordUrl !== undefined ? data.wordUrl.replace('http:', '') : ''
        this.result.duration = data.duration // 处理用时
        this.result.pdfUrl = data.pdfUrl !== undefined ? data.pdfUrl : ''
        this.result.wordUrlKs =
          data.wordUrlKs !== undefined
            ? data.wordUrlKs.replace('http:', '')
            : ''
        this.handleOpTimes(data.opTimes, data.ops)

        this.getMeta(data)

        if (data.errMsgs !== undefined && data.errMsgs.length > 0) {
          this.result.errMsgs = data.errMsgs
          this.message('error', '服务报错: ', data.errMsgs)
          return Promise.reject('详情查看错误信息')
        } else {
          return Promise.resolve(1)
        }
      })
    },
    async kdc2Markdown(data) {
      // this.kdcJsonData = data
      console.log('data.kdc:', data.kdc)
      this.jsonData = data
      this.showJsonData = true
      if (data.kdc.doc !== undefined) {
        const req = {
          'doc': data.kdc.doc
        }
        const res_data = await kdc2md(
          this.url.value,
          req
        )

        if (res_data.status === 200) {
          // this.markDownData = res_data.data.data
          this.markDownData = res_data.data.data.replace(/-internal/g, '')
          // this.markDownData = res_data.data.data.replace(/http:\/\/zhai-datas.ks3-cn-beijing-internal\.ksyun\.com/g, 'http://zhai-datas.ks3-cn-beijing.ksyun.com')
          this.showMarkDown = true
        } else {
          this.message('error', '转换markdown错误: ', res_data.msg)
        }
      }
    },
    showJson() {
      this.copyData(this.jsonData)
    },
    copyData(data) {
      var data_value = JSON.stringify(data)
      console.log('data_value:', data_value)

      var textArea = document.createElement('textarea')
      textArea.value = data_value
      document.body.appendChild(textArea)
      textArea.select()
      textArea.focus()
      document.execCommand('copy')
      document.body.removeChild(textArea)
      this.$message.success('复制成功')
    },
    getFileNameMD5(name) {
      const userId = this.$store.state.user.userId
      const deptId = this.$store.state.user.deptId
      const useremail = this.$store.state.user.email
      var a = name.lastIndexOf('.')
      var filter = name.substring(a + 1, name.length)
      var fname = name.substring(0, a)

      var mddata = fname + userId + useremail + deptId

      return CryptoJS.MD5(mddata).toString() + '.' + filter
    },
    /** 点击上传按钮操作 */
    async pdf2wordUpload(param) {
      // 开始加载
      this.loading = true
      // 清空
      this.jsonData = {}

      const { file } = param

      if (!this.beforeUpload(file)) {
        // 结束加载
        this.loading = false
        return Promise.reject()
      }
      const { name } = file

      const filenamebymd5 = this.getFileNameMD5(name)

      const res_sign_data = await httpRequesSignKs(filenamebymd5)

      if (res_sign_data.status === 200) {
        const res_uploadks3_data = await uploadKs3Store(res_sign_data, file)
        if (res_uploadks3_data.status === 200) {
          const imageurl =
            'https://' +
            res_sign_data.data.data['url'] +
            '/' +
            res_sign_data.data.data['key']

          // if (this.url.value.lastIndexOf('pcv-test') > 0) {
          //   this.uri = '/create'
          // } else {
          //   this.uri = ''
          // }
          // 新增 maxConNumPage、imageurl、justToImg、textUnify、pdfDebugFlags
          const res_create_url_data = await pdf2WordCreateUrl(
            this.url.value,
            imageurl,
            this.startPage.value,
            this.endPage.value,
            this.startPage.exporttype,
            this.uri,
            this.maxConNumPageInt,
            this.imgType,
            this.justToImg,
            this.pdfDebugFlagsInt,
            this.dpi.value,
            this.switchMix,
            this.getFileMeta,
            this.isScan,
            this.include_elements
          )

          if (res_create_url_data.status === 200) {
            // 等待1s
            await sleep(1000)
            const docID = res_create_url_data.data.data['docID']
            await this.waitDocStatus(docID).catch((error) => {
              console.log(error)
            })
          }
        }
      }

      const s_time = new Date().getTime()
      // const status = await this.httpRequest(axios_config_get_docID)
      param.onSuccess()

      // 结束加载
      this.loading = false
      const e_time = new Date().getTime()
      this.statistic = {
        status: 1,
        duration: this.result.duration || (e_time - s_time) / 1000
      }
      return Promise.resolve()
    },
    async pdf2wordUploadbak(param) {
      // 开始加载
      this.loading = true

      const { file } = param

      if (!this.beforeUpload(file)) {
        // 结束加载
        this.loading = false
        return Promise.reject()
      }
      const { name } = file
      // eslint-disable-next-line no-undef
      const expiration = new Date(getExpires(60 * 10) * 1000).toISOString()

      const signatureform = new FormData()

      signatureform.append('filename', name)
      signatureform.append('expiration', expiration)

      const axios_config_get_policy_signature = {
        method: 'post',
        headers: { 'Cache-Control': 'no-cache', ...getHeaders() },
        url: process.env.VUE_APP_BASE_API + '/layout/signature',
        data: signatureform,
        responseType: 'json',
        processData: false, // 必须
        contentType: false
      }
      // const res_sign_data= await httpRequesSignKs('sdf.pdf')

      await this.httpRequesSign(axios_config_get_policy_signature, file)

      const s_time = new Date().getTime()
      // const status = await this.httpRequest(axios_config_get_docID)
      const status2 = 1
      // if (status === 1) {
      param.onSuccess()
      // }
      // 结束加载
      this.loading = false
      const e_time = new Date().getTime()
      this.statistic = {
        status2,
        duration: this.result.duration || (e_time - s_time) / 1000
      }
      return Promise.resolve()
    },
    httpRequesSign(axios_config, file) {
      axios(axios_config).then((res) => {
        const formData = new FormData()
        const key = res.data.data['key']
        formData.append('acl', res.data.data['acl'])
        formData.append('key', res.data.data['key'])
        formData.append('Signature', res.data.data['signature'])
        formData.append('KSSAccessKeyId', res.data.data['kSSAccessKeyId'])
        // eslint-disable-next-line no-undef
        formData.append('Policy', res.data.data['policy'])
        formData.append('file', file)
        console.log(res.data.data['url'])
        const url = 'https://' + res.data.data['url']

        const axios_config_post_ks3 = {
          method: 'post',
          headers: { 'Cache-Control': 'no-cache', ...getHeaders() },
          url: url,
          data: formData,
          responseType: 'json',
          processData: false, // 必须
          contentType: false
        }

        return axios(axios_config_post_ks3)
          .then((res) => {
            if (res.status === 200) {
              this.pdf2wordCreate(url + '/' + key)
            }
          })
          .catch((error) => {
            console.log(error)
          })
      })
    },
    httpRequest(axios_config) {
      return axios(axios_config)
        .then((res) => {
          console.log(res)
          res = res.data
          if (res.code === 200) {
            const docID = res.data['docID']
            return axios_wait_doc({
              method: 'get',
              headers: { 'Cache-Control': 'no-cache', ...getHeaders() },
              url:
                this.url.value +
                '/layout/scheduler/query?docID=' +
                docID,
              handleProgress: this.progress
            })
          } else {
            return Promise.reject('转化请求失败!')
          }
        })
        .then((res) => {
          console.log(res)
          if (res === undefined) {
            console.log('undefined res')
            return Promise.resolve(-1)
          }
          // 处理完成
          console.log('文件处理完成: ' + JSON.stringify(res))
          console.log(this.startPage.exporttype)
          if (this.startPage.exporttype === 'pdf2pptx' || this.startPage.exporttype === 'pdf2xlsx') {
            this.result.exporttype = require('../../../assets/cv/' + this.startPage.exporttype + '.png')
          } else {
            this.result.exporttype = require('../../../assets/cv/word.png')
          }

          const { data } = res
          this.result.wordUrl =
            data.wordUrl !== undefined ? data.wordUrl.replace('http:', '') : ''
          this.result.duration = data.duration // 处理用时
          this.result.pdfUrl = data.pdfUrl !== undefined ? data.pdfUrl : ''
          this.result.wordUrlKs =
            data.wordUrlKs !== undefined
              ? data.wordUrlKs.replace('http:', '')
              : ''
          this.handleOpTimes(data.opTimes, data.ops)

          this.getMeta(data)

          if (data.errMsgs !== undefined && data.errMsgs.length > 0) {
            this.result.errMsgs = data.errMsgs
            return Promise.reject('详情查看错误信息')
          } else {
            return Promise.resolve(1)
          }
        })
        .catch((error) => {
          this.message('error', '文件处理失败! ', error)
          return Promise.resolve(0)
        })
    },
    // 获取文件信息
    getMeta(data) {
      if (data.fileMeta !== undefined) {
        this.classifier = data.fileMeta.Classifier
        this.keyWords = data.fileMeta.KeyWords
        this.summary = data.fileMeta.Summary
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/styles/mixin.scss";

/* 对话框底部按钮区域 */
.dialog-footer {
  display: flex;
  justify-content: space-between;
}
.reset-button {
  order: 1;
}
.right-buttons {
  order: 2;
}
.container {
  padding-bottom: 2%;
  padding-left: 8%;
  padding-right: 8%;
}
.el-row {
  margin-bottom: 20px;
  &:last-child {
    margin-bottom: 0;
  }
}
.el-col {
  border-radius: 8px;
  border: 1px dashed #d9d9d9;
  background-color: #ffffff;
  color: #666666;
  padding: 1%;
}
.bg-purple-dark {
  background: #99a9bf;
}
.bg-purple {
  background: #d3dce6;
}
.bg-purple-light {
  background: #e5e9f2;
}
.grid-content {
  border-radius: 8px;
  vertical-align: middle;
}
.row-bg {
  padding: 10px 0;
  background-color: #f9fafc;
}

.upload-container {
  width: 100%;
  padding: 1%;
  position: relative;
  @include clearfix;
  .params-container {
    width: 70%;
    display: inline-block;
    padding-left: 1%;
    padding-right: 1%;
  }
  .params {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }
  .param-label {
    width: 50%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-bottom: 2%;
  }
  .param-input {
    width: 50%;
    text-align: right;
    margin-bottom: 2%;
  }
}
</style>
