/* eslint-disable no-unused-vars */
import axios from 'axios'
import CryptoJS from 'crypto-js'
import { getHeaders } from '@/utils/get-header'

export const axios_transform = axios.create({})

// http request 拦截器
axios_transform.interceptors.request.use(
  config => {
    return config
  },
  err => {
    return Promise.reject(err)
  }
)

// http response 拦截器
axios_transform.interceptors.response.use(
  async response => {
    return response
  },
  error => {
    console.log('err' + error) // for debug
    return Promise.reject(error)
  }
)

function ks3_post(url, data = {}) {
  return new Promise((resolve, reject) => {
    axios_transform({
      headers: {
        'Content-Type': 'multipart/form-data',
        ...getHeaders()
      },
      method: 'post',
      url,
      data
    }).then((data) => {
      resolve(data)
    }).catch(err => {
      reject(err)
    })
  })
}

async function uploadKS3(_this, file, file_md5, dir) {
  const key = dir + '/' + file_md5
  // eslint-disable-next-line no-undef
  const expiration = new Date(getExpires(60 * 10) * 1000).toISOString()
  // 服务端获取签名
  const res = await _this.$http.get(_this.$api.ks3SignatureApi,
    {
      method: 'POST',
      headers: {
        ...getHeaders()
      },
      key: key,
      // policy: stringToSign,
      date: expiration
    })
  if (res.code === 200) {
    const formData = new FormData()
    formData.append('acl', res.data['acl'])
    formData.append('key', res.data['Key'])
    formData.append('Signature', res.data['Signature'])
    formData.append('KSSAccessKeyId', res.data['KSSAccessKeyId'])
    formData.append('success_action_status', '200')
    // eslint-disable-next-line no-undef
    formData.append('Policy', res.data['policy'])
    formData.append('file', file)
    console.log(res.data['url'], res.data['Key'])
    const resKs3 = await ks3_post(res.data['url'], formData)
    return resKs3.status
  }
  _this.$message.warning('获取ks3签名失败')
  return res.code
}

function fileHandler(_this, data, loading) {
  const reader = new FileReader()
  const reader_imgfile = new FileReader()
  reader.readAsBinaryString(data.raw_model_file)
  reader.md5 = ''
  reader.onload = function() {
    this.md5 = CryptoJS.MD5(reader.result).toString() + '.' + data.raw_model_file.name.split('.').pop()
  }
  if (data.task !== 'transform') {
    reader.onloadend = async function() {
      reader_imgfile.readAsArrayBuffer(data.img_file)
      reader_imgfile.md5 = ''
      reader_imgfile.onload = function() {
        this.md5 = CryptoJS.MD5(reader_imgfile.result).toString() + '.' + data.img_file.name.split('.').pop()
      }
      reader_imgfile.onloadend = async function() {
        const resKs3 = await uploadKS3(_this, data.raw_model_file, reader.md5, 'raw_model_files')
        const resKs3_imgfile = await uploadKS3(_this, data.img_file, reader_imgfile.md5, 'img_files')
        if (resKs3 === 200 && resKs3_imgfile === 200) {
          const formData = new FormData()
          Object.keys(data).forEach((key) => {
            if (key === 'raw_model_file') {
              formData.append(key, data.raw_model_file.name)
              formData.append('raw_model_file_md5', reader.md5)
            } else if (key === 'img_file') {
              formData.append(key, data.img_file.name)
              formData.append('img_file_md5', reader_imgfile.md5)
            } else {
              formData.append(key, data[key])
            }
          })
          const res = await _this.$http.post(_this.$api.transformApi, formData, 'multipart/form-data', {
            headers: {
              ...getHeaders()
            }
          })
          if (res.code === 200) {
            _this.toRecords()
          } else {
            loading = false
            _this.$message.error(res.message)
          }
        } else {
          _this.$message.warning('文件上传ks3失败， 请重试')
          loading = false
        }
      }
    }
  } else {
    reader.onloadend = async function() {
      const resKs3 = await uploadKS3(_this, data.raw_model_file, reader.md5, 'raw_model_files')
      if (resKs3 === 200) {
        const formData = new FormData()
        Object.keys(data).forEach((key) => {
          if (key === 'raw_model_file') {
            formData.append(key, data.raw_model_file.name)
            formData.append('raw_model_file_md5', reader.md5)
          } else {
            formData.append(key, data[key])
          }
        })
        const res = await _this.$http.post(_this.$api.transformApi, formData, 'multipart/form-data', {
          headers: {
            ...getHeaders()
          }
        })
        if (res.code === 200) {
          _this.toRecords()
        } else {
          loading = false
          _this.$message.error(res.message)
        }
      } else {
        _this.$message.warning('文件上传ks3失败， 请重试')
        loading = false
      }
    }
  }
}

export { fileHandler, ks3_post }

