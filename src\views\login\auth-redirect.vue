<script>
export default {
  name: 'AuthRedirect',
  data: function() {
    return {
      code: ''
    }
  },
  created() {
    const hash = window.location.search.slice(1)
    console.log('hash:' + hash)
    let code = ''
    if (hash.length > 0) {
      const kvs = hash.split('&')
      for (const kv of kvs) {
        const pair = kv.split('=')
        if (pair[0] === 'code') {
          code = pair[1]
        }
      }
    }
    if (window.localStorage) {
      window.localStorage.setItem('x-admin-oauth-code', code)
      window.close()
    }
    // window.opener.postMessage({ type: 'code', data: code })
    // Scripts may not close windows that were not opened by script
    // window.close()
  },
  render: function(h) {
    return h() // avoid warning message
  }
}
</script>
