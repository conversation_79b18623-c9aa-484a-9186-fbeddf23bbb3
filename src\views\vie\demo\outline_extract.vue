<template>
  <el-card>
    <div slot="header">
      <CVHint>使用说明：（PDF转大纲提取）选择一个PDF文件，系统会对文件进行处理，处理完成下方查看处理的效果</CVHint>
    </div>
    <!-- 主体内容 -->
    <div class="card-content">
      <div>
        <div class="container">
          <el-row type="flex" justify="center" :gutter="20">

            <el-col :span="8" class="grid-content">
              <span style="color:blue"> {{ "参数设置：" }}</span>
              <div class="upload-container">
                <div class="params-container" style="width: 100% !important">
                </div>
                <div class="params-container">
                  <div class="params">
                    <span class="param-label">服务版本 :</span>
                    <el-select v-model="startPage.version" class="param-input" placeholder="版本" size="small"
                      @change="selectVersion">
                      <el-option v-for="dict in versions" :key="dict.value" :label="dict.label" :value="dict.value"
                        @click.native="handleSelect(dict)" />
                    </el-select>
                  </div>
                  <div class="params">
                    <span class="param-label">启用LLM :</span>
                    <el-select v-model="form.isLLM" class="param-input" placeholder="版本" size="small"
                      @change="selectLLM">
                      <el-option v-for="dict in isLLMOps" :key="dict.value" :label="dict.label" :value="dict.value"/>
                    </el-select>
                  </div>
                </div>

                <div class="params-container" style="width: 100% !important">
                  <br>
                  <div class="params">

                    <el-tooltip content="切换输入方式" placement="right-end">
                      <el-select v-model.number="choiceInput" placeholder="默认上传文件">
                        <el-option v-for="dict in choiceInputOptions" :key="dict.value" :label="dict.label"
                          :value="dict.value" />
                      </el-select>
                    </el-tooltip>
                  </div>
                </div>
                <hr>
                <div v-if="choiceInput == 'inputUrl'">
                  <br><span style="color:blue"> {{ "输入PDF链接：" }}</span><br>
                  <div class="params">
                    <span class="param-label" style="width: 25% !important">url of pdf :</span>
                    <el-input v-model="fileUrl" class="param-input" style="width: 75% !important" type="text" /><br>
                  </div>
                  <el-button type="primary" size="mini" @click="outlineExtractconvertclick">开始转换</el-button>
                </div>
                <div v-if="choiceInput == 'uploadFile'">
                  <br><span style="color:blue"> {{ "上传PDF文件：" }}</span>
                  <el-upload v-loading="loading" element-loading-text="文件处理中" class="uploader" list-type="text"
                    accept=".pdf" :multiple="false" :show-file-list="false" :file-list="fileList"
                    :http-request="outlineExtractUpload" :on-success="onSuccess" action="placeholder" drag>
                    <i class="el-icon-upload" />
                    <div class="el-upload__text">
                      将文件拖到此处，或
                      <el-button type="text">点击上传</el-button>
                    </div>
                  </el-upload>

                  <el-progress v-show="percentage !== 0 && percentage !== 100" :text-inside="true" :stroke-width="14"
                    :percentage="percentage" />
                </div>

                <br>
                <div v-show="fileName !== ''">
                  <hr>
                  <div class="params-container">
                    <div><span style="color:darkblue"> {{ '转化成功后的结果在页面下方展示。' }}</span></div><br>
                    <div class="params">
                      <span style="color:blue" class="param-label">{{ '转化结果' }} :</span>
                    </div>
                    <div v-show="fileName !== ''" class="params">
                      <span class="param-label">{{ fileName }} :</span>
                      <span class="param-input">
                        {{ fileSize }}
                      </span>
                    </div>
                    <div v-show="statistic.status !== -1" class="params">
                      <span class="param-label">处理用时 :</span>
                      <span class="param-input">
                        {{ formattedDuration }}
                      </span>
                    </div>
                    <div v-show="statistic.status !== -1" class="params">
                      <span v-show="statistic.status === 1" class="param-label" style="color:blue">转化成功!</span>
                      <span v-show="statistic.status === 0" class="param-label" style="color: #ff0000">转化失败!</span>
                    </div>
                    <a
                      v-show="result.titles.length != 0"
                      href="javascript:void(0);"
                      :download="downloadFileName"
                      @click="handleDownload"
                    ><div class="download-doc-btn">大纲结果下载</div> </a>
                  </div>
                </div>

              </div>
            </el-col>
            <el-col :span="5" class="grid-content">
              <CVDemo :thumbnail="true" :loading="loading" :data="urlFiles[0]" @click="outlineExtract(0, $event)">示例:
              </CVDemo>
            </el-col>
          </el-row>
          <el-row v-show="percentage === 0 || percentage === 100
            " type="flex" justify="space-around">
          </el-row>
          <el-row v-show="result.titles.length != 0" type="flex" justify="space-around">
          </el-row>
          <el-row v-show="percentage === 0 || percentage === 100" :gutter="20">
            <el-col v-show="result.opTimes[0] !== 0" :span="24" class="grid-content">
              <ul>
                <li v-for="(errMsg, index) in result.errMsgs" :key="index">
                  {{ errMsg }}
                </li>
              </ul>
              <el-row type="flex" justify="center" :gutter="20">
                <el-col :span="13" class="grid-content-outline">
                  <div>
                    <embed :src="fileUrl" type="application/pdf" width="100%" height="800px" />
                  </div>
                </el-col>
                <el-col :span="13" class="grid-content-outline">
                  <div v-for="(node, index) in result.titles" :key="index">
                    <div style="display: flex; align-items: flex-end;">
                      <h1 class="title-level-1" style="white-space: nowrap;">{{ node.nodes[0].text }}</h1>
                      <span class="page-number" style="margin-left: 5px;">页码：{{ node.nodes[0].pageIndex }}</span>
                    </div>
                    <TitleItem :node="node" :level="2" />
                  </div>
                </el-col>
              </el-row>
            </el-col>
          </el-row>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script>
import axios from 'axios'
import CVHint from '@/views/cv/layout/components/CVHint'
import CVDemo from '@/views/cv/layout/components/CVDemo'
import TitleItem from '@/views/cv/layout/components/TitleNode'
import CryptoJS from 'crypto-js'
import {
  urlRegex,
  formatfileSize,
  isValidFile,
  convertOpTimes
} from '@/api/cv/utils'
import { axios_wait_doc } from '@/api/cv/request'
import {
  httpRequesSignKs,
  uploadKs3Store,
  outlineExtractCreateUrl
} from '@/api/cv/request-ks3'
import { getHeaders } from '@/utils/get-header'
import { sleep } from '@/api/cv/utils'

export default {
  name: 'LayoutOutLineExtract',
  components: { CVHint, CVDemo,TitleItem},
  props: {
    maxFileNum: {
      validator(value) {
        if (value <= 0) {
          return 1
        }
        return value
      },
      default: 1
    },
    fileType: {
      type: Array,
      default: function () {
        return ['.pdf']
      }
    }
  },
  data() {
    return {
      // 输入方式字典
      choiceInputOptions: [
        { label: '上传PDF文件(默认)', value: 'uploadFile' },
        { label: '输入PDF链接', value: 'inputUrl' }
      ],
      choiceInput: 'uploadFile',
      // 弹出层标题（用户导入）
      title: '',
      // 是否显示弹出层（用户导入）
      open: false,
      // 表单参数
      form: {
        maxConNumPageInt: 18,
        maxConNumPage: 18,
        imgType: 'png',
      },
      // 参数
      maxConNumPageInt: 18,
      maxConNumPage: 18,
      imgType: 'png',
      justToImg: false,
      pdfDebugFlags: 0,
      switchMix: true,
      getFileMeta: false,
      // 表单校验
      rules: {
        // 定义规则对象
        // trigger: 'blur' 表示在失去焦点时触发验证
        maxConNumPage: [{ type: 'number', message: '最大并发页必须为int类型', trigger: ['blur', 'change'] }],
        pdfDebugFlags: [{ type: 'number', message: 'PDFDebugFlags必须为int类型', trigger: ['blur', 'change'] }]
      },
      loading: false,
      url: {
        value: '//pcv-test.wps.cn',
        default: '//pcv-test.wps.cn'
      },
      fileUrl: '',
      remark: '',
      // 是否对标题进行分类
      isLLMOps: [
        { label: '是', value: true },
        { label: '否', value: false }
      ],
      // 表单参数
      form: {
        isLLM: false,
      },
      isLLM: false,
      startPage: {
        value: 0,
        default: 0,
        min: 0,
        max: 10,
        exporttype: 'outlineextract',
        version: ''
      },
      endPage: {
        value: 0,
        default: 0,
        min: 0,
        max: 10
      },
      uri: '/create',
      fileObj: {
        name: '',
        size: 0
      },
      downloadFileName:'result.json',
      fileList: [],
      exporttypeOptions: [],
      versions: [],
      result: {
        wordUrl: '',
        duration: 0,
        pdfUrl: '',
        wordUrlKs: '',
        opTimes: Array(11).fill(0), // 时间戳数组
        errMsgs: [],
        classifier: '',
        keyWords: [],
        titles: [],
        summary: ''
      },
      statistic: {
        status: -1,
        duration: 0
      },
      percentage: 0,
      urlFiles: [
        [
          {
            url: 'https://zhai-datas.ks3-cn-beijing.ksyuncs.com/zhaoliang7/outline/demo/%E5%8A%B3%E5%8A%A8%E5%90%88%E5%90%8C.pdf',
            name: '劳动合同.pdf',
            size: '96925'
          },
          {
            url: 'https://zhai-datas.ks3-cn-beijing.ksyuncs.com/zhaoliang7/outline/demo/%E5%9F%BA%E6%9C%AC%E4%BA%A4%E6%98%93%E5%90%88%E5%90%8C.pdf',
            name: '交易合同.pdf',
            size: '96925'
          },
          {
            url: 'https://zhai-datas.ks3-cn-beijing.ksyuncs.com/zhaoliang7/outline/demo/%E5%A4%96%E6%B1%87%E4%BA%A4%E6%98%93.pdf',
            name: '外汇交易.pdf',
            size: '96925'
          },
          {
            url: 'https://zhai-datas.ks3-cn-beijing.ksyuncs.com/zhaoliang7/outline/demo/%E6%9C%9F%E5%88%8A%E8%AE%BA%E6%96%87.pdf',
            name: '期刊论文.pdf',
            size: '96925'
          },
          {
            url: 'https://zhai-datas.ks3-cn-beijing.ksyuncs.com/zhaoliang7/outline/demo/%E8%8B%B1%E6%96%87%E8%AE%BA%E6%96%87.pdf',
            name: '英文论文.pdf',
            size: '96925'
          },
          {
            url: 'https://zhai-datas.ks3-cn-beijing.ksyuncs.com/zhaoliang7/outline/demo/%E8%AE%BA%E6%96%87%E4%BB%BB%E5%8A%A1%E4%B9%A6.pdf',
            name: '论文任务书.pdf',
            size: '96925'
          },
        ]
      ]
    }
  },
  computed: {
    formattedDuration() {
      return this.statistic.duration.toFixed(3) + 's'
    },
    fileName() {
      const { name } = this.fileObj
      return name.trim()
    },
    fileSize() {
      const { size } = this.fileObj
      return formatfileSize(size)
    }
  },
  // 通过watch监听输入值的变化，并将maxConNumPage/pdfDebugFlags由 string类型转为 int类型
  watch: {
    'form.maxConNumPage'(val) {
      this.maxConNumPageInt = parseInt(val)
    }
    
  },
  created() {
    this.getDicts('pdf_convert_version').then(response => {
      this.versions = response.data
      if (this.versions.length > 0) {
        //暂时不开放线上
        this.versions.shift(); // 移除数组的第一个元素
        this.url.value = this.versions[0].value
        this.startPage.version = this.versions[0].label
        this.remark = this.versions[0].remark
      }
    })
  },
  methods: {
      handleDownload() {
        // 将JSON数据转换为字符串
        const jsonDataString = JSON.stringify(this.result.titles, null, 2);
        // 创建Blob对象
        const blob = new Blob([jsonDataString], { type: 'application/json' });
        // 创建下载链接
        const url = window.URL.createObjectURL(blob);
        const downloadLink = document.createElement('a');
        downloadLink.href = url;
        downloadLink.download = this.downloadFileName;
        
        // 模拟点击下载
        document.body.appendChild(downloadLink);
        downloadLink.click();
        document.body.removeChild(downloadLink);
        
        // 释放URL对象
        window.URL.revokeObjectURL(url);
      },

    /** 提交按钮 */
    submitForm: function () {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.isLLM = this.form.isLLM
          this.open = false
        }
      })
    },
    // 取消按钮
    cancel() {
      this.form.isLLM = this.isLLM
      this.open = false
      // this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        isLLM: false,
      }
      this.resetForm('form')
    },
    progress(percentage) {
      this.percentage = percentage
    },
    handleSelect(item) {
      this.remark = item.remark
    },
    selectVersion(value) {
      this.url.value = value
    },
    selectLLM(value) {
      this.isLLM = value
    },
    validateUploadPDFUrl(fileurl) {
      if (fileurl.trim() === '') {
        return false
      }
      if (!urlRegex.test(fileurl)) {
        return false
      }
      return true
    },
    validFileType(ext) {
      var isValid
      if (!isValidFile(ext, this.fileType)) {
        this.message('error', '请选择PDF文件/ 图片! ', '')
        return Promise.reject()
      } else {
        return true
      }
    },
    message(type, msg, dataMsg) {
      this.$message({
        showClose: true,
        message: msg + dataMsg,
        center: true,
        duration: 4000,
        type: type
      })
    },
    getUrlFileName(url) {
      const o = url.lastIndexOf('/')
      if (o > 0) {
        const e = url.lastIndexOf('?')
        if (e > o) return url.substring(o + 1, e)
        else return url.substring(o + 1)
      } else {
        this.$message({
          showClose: true,
          message: '名字异常url of pdf!',
          center: true,
          duration: 4000,
          type: 'error'
        })
        return false
      }
    },
        // 获取url的后缀名
    getFileExtension(url) {
      const regex = /[^.]+$/
      const extension = url.match(regex)[0]
      return extension
    },
    /** 转换按钮操作 */
    async outlineExtractconvertclick() {
        this.inputUrl()
    },
    // 输入url的情况
    async inputUrl() {
      if (!this.validateUploadPDFUrl(this.fileUrl)) {
        this.message('error', '请输入正确文件地址! ', '')
        this.originUrl = ''
        return false
      }
      const ext = this.getFileExtension(this.fileUrl)
      const isValid = this.validFileType('.' + ext)
      if (!isValid) {
        // 结束加载
        this.originUrl = ''
        this.message('error', '参数设置的‘输入文件类型’没有与输入的链接相匹配！', '')
        return
      }
      this.loading = true

      if (this.fileUrl.lastIndexOf('ksyuncs.com') > 0 || this.fileUrl.lastIndexOf('ksyun.com') > 0) {
        this.originUrl = this.fileUrl.replace(/http:\/\//g, 'https://')
        console.log('url:', this.originUrl)
        await this._outlineExtract(undefined, this.fileUrl)
      } else {
        this.message('warn', '目前只支持特定链接，暂不支持该链接。', '')
      }
      const s_time = new Date().getTime()

      // 结束加载
      this.loading = false
      const e_time = new Date().getTime()
      this.statistic = {
        status: 1,
        duration: this.result.duration || (e_time - s_time) / 1000
      }
      return Promise.resolve()
    },
    handleOpTimes(times) {
      const opTimes = this.result.opTimes
      convertOpTimes(opTimes, times)
    },
    urlChange(value) {
      // url 检测
      if (urlRegex.test(value)) {
        this.url.value = value
      } else {
        this.url.value = this.url.default
      }
    },
    numberInputChange(obj, value) {
      if (value === '') {
        obj.value = obj.default
      } else if (value > obj.max) {
        obj.value = obj.max
      } else if (value < obj.min) {
        obj.value = obj.min
      }
      obj.value = parseInt(value)
    },
    startPageChange(value) {
      this.numberInputChange(this.startPage, value)
    },
    endPageChange(value) {
      this.numberInputChange(this.endPage, value)
    },
    fileChange(file, fileList) {
      this.fileList = fileList.slice(-this.maxFileNum)
    },
    onSuccess(response, file, fileList) {
      // hook
      this.fileChange(file, fileList)
    },
    resetResult() {
      this.percentage = 0
      this.result = {
        wordUrl: '',
        duration: 0,
        pdfUrl: '',
        wordUrlKs: '',
        opTimes: Array(11).fill(0), // 时间戳数组
        errMsgs: [],
        exporttype: '',
        classifier: '',
        keyWords: [],
        titles: [],
        summary: ''
      }
      this.statistic = {
        status: -1,
        duration: 0
      }
    },
    beforeUpload(file) {
      // 清理旧结果
      this.fileObj = { name: '', size: 0 }
      this.resetResult()

      // 文件类型判断
      const { name } = file
      var ext = '.' + name.trim().split('.').pop()
      ext = ext.toLowerCase()
      if (!isValidFile(ext, this.fileType)) {
        this.$message({
          showClose: true,
          message: '请选择pdf文件!',
          center: true,
          duration: 4000,
          type: 'error'
        })
        return false
      } else {
        this.fileObj = file
        return true
      }
    },
    // 点击示例
    async outlineExtract(arrayid, id) {
      this.loading = true

      const file = this.urlFiles[parseInt(arrayid)][parseInt(id)]
      this.fileObj = { name: file.name, size: file.size }
      this.resetResult()

      const data = {
        url: file.url,
        isLLM: this.isLLM,
      }
      this.fileUrl = file.url
      const axios_config_get_docID = {
        method: 'post',
        headers: {
          'Cache-Control': 'no-cache',
          'Content-Type': 'application/json',
          ...getHeaders()
        },
        url: this.url.value + '/layout/scheduler/' + 'outlineextract' + '/create',
        data,
        responseType: 'json',
        processData: false // 必须
      }
      const s_time = new Date().getTime()
      const status = await this.httpRequest(axios_config_get_docID)
      // 结束加载
      this.loading = false
      const e_time = new Date().getTime()
      this.statistic = {
        status,
        duration: this.result.duration || (e_time - s_time) / 1000
      }
      return Promise.resolve()
    },
    outlineExtractCreate(url) {
      const form = new FormData()
      const urls = []
      urls.push(url)
      form.append('isLLM', this.isLLM)
      form.append('imgUrls', urls)
      const obj = {
        isLLM: this.isLLM,
        url: url
      }
      this.fileUrl = url
      const json = JSON.stringify(obj)
      const axios_config_get_docID = {
        method: 'post',
        headers: { 'Cache-Control': 'no-cache', ...getHeaders() },
        url: this.url.value + '/layout/scheduler/outlineextract',
        data: json,
        responseType: 'json',
        processData: false, // 必须
        contentType: false
      }
      axios(axios_config_get_docID).then((res) => {
        const docID = res.data.data.docID
        axios_wait_doc({
          method: 'get',
          headers: { 'Cache-Control': 'no-cache', ...getHeaders() },
          url:
            this.url.value +
            '/layout/scheduler/query?docID=' +
            docID,
          handleProgress: this.progress
        }).then((res) => {
          if (res === undefined) {
            console.log('undefined res')
            return Promise.resolve(-1)
          }
          const { data } = res
          this.result.duration = data.duration // 处理用时
          this.handleOpTimes(data.opTimes)

          if (data.errMsgs !== undefined && data.errMsgs.length > 0) {
            this.result.errMsgs = data.errMsgs
            return Promise.reject('详情查看错误信息')
          } else {
            return Promise.resolve(1)
          }
        })
      })
    },
    async waitDocStatus(docID) {
      await axios_wait_doc({
        method: 'get',
        headers: { 'Cache-Control': 'no-cache', ...getHeaders() },
        url:
          this.url.value + '/layout/scheduler/query?docID=' + docID,
        handleProgress: this.progress
      }).then((res) => {
        if (res === undefined) {
          console.log('undefined res')
          return Promise.resolve(-1)
        }

        const { data } = res
        this.result.titles = data.titles || [] // 处理用时
        this.result.duration = data.duration // 处理用时
        this.handleOpTimes(data.opTimes)

        this.getMeta(data)

        if (data.errMsgs !== undefined && data.errMsgs.length > 0) {
          this.result.errMsgs = data.errMsgs
          return Promise.reject('详情查看错误信息')
        } else {
          return Promise.resolve(1)
        }
      })
    },
    getFileNameMD5(name) {
      const userId = this.$store.state.user.userId
      const deptId = this.$store.state.user.deptId
      const useremail = this.$store.state.user.email
      var a = name.lastIndexOf('.')
      var filter = name.substring(a + 1, name.length)
      var fname = name.substring(0, a)

      var mddata = fname + userId + useremail + deptId

      return CryptoJS.MD5(mddata).toString() + '.' + filter
    },
    /** 点击上传按钮操作 */
    async outlineExtractUpload(param) {
      // 开始加载
      this.loading = true
      // 清空
      this.jsonData = {}

      const { file } = param

      if (!this.beforeUpload(file)) {
        // 结束加载
        this.loading = false
        return Promise.reject()
      }
      const { name } = file

      const filenamebymd5 = this.getFileNameMD5(name)

      const res_sign_data = await httpRequesSignKs(filenamebymd5)

      if (res_sign_data.status === 200) {
        const res_uploadks3_data = await uploadKs3Store(res_sign_data, file)
        if (res_uploadks3_data.status === 200) {
          const imageurl =
            'https://' +
            res_sign_data.data.data['url'] +
            '/' +
            res_sign_data.data.data['key']

          this.originUrl = imageurl.replace(/http:\/\//g, 'https://')
          this.fileUrl = imageurl.replace(/http:\/\//g, 'https://')
          this._outlineExtract(file, imageurl)
        }
      }

      const s_time = new Date().getTime()
      param.onSuccess()

      // 结束加载
      this.loading = false
      const e_time = new Date().getTime()
      this.statistic = {
        status: 1,
        duration: this.result.duration || (e_time - s_time) / 1000
      }
      return Promise.resolve()
    },
    async _outlineExtract(file, imageurl) {
      const res_create_url_data = await outlineExtractCreateUrl(
          this.url.value,
          imageurl,
          this.uri,
          'outlineextract',
          this.isLLM,
      )

      if (res_create_url_data.status === 200) {
        // 等待1s
        await sleep(1000)
        const docID = res_create_url_data.data.data['docID']
        await this.waitDocStatus(docID).catch((error) => {
          console.log(error)
        })
      }
    },
    httpRequesSign(axios_config, file) {
      axios(axios_config).then((res) => {
        const formData = new FormData()
        const key = res.data.data['key']
        formData.append('acl', res.data.data['acl'])
        formData.append('key', res.data.data['key'])
        formData.append('Signature', res.data.data['signature'])
        formData.append('KSSAccessKeyId', res.data.data['kSSAccessKeyId'])
        formData.append('Policy', res.data.data['policy'])
        formData.append('file', file)
        console.log(res.data.data['url'])
        const url = 'https://' + res.data.data['url']

        const axios_config_post_ks3 = {
          method: 'post',
          headers: { 'Cache-Control': 'no-cache', ...getHeaders() },
          url: url,
          data: formData,
          responseType: 'json',
          processData: false, // 必须
          contentType: false
        }

        return axios(axios_config_post_ks3)
          .then((res) => {
            if (res.status === 200) {
              this.outlineExtractCreate(url + '/' + key)
            }
          })
          .catch((error) => {
            console.log(error)
          })
      })
    },
    httpRequest(axios_config) {
      return axios(axios_config)
        .then((res) => {
          res = res.data
          if (res.code === 200) {
            const docID = res.data['docID']
            return axios_wait_doc({
              method: 'get',
              headers: { 'Cache-Control': 'no-cache', ...getHeaders() },
              url:
                this.url.value +
                '/layout/scheduler/query?docID=' +
                docID,
              handleProgress: this.progress
            })
          } else {
            return Promise.reject('转化请求失败!')
          }
        })
        .then((res) => {
          if (res === undefined) {
            console.log('undefined res')
            return Promise.resolve(-1)
          }
          // 处理完成

          const { data } = res
          this.result.duration = data.duration // 处理用时
          this.result.titles = data.titles || [] // 处理用时

          this.handleOpTimes(data.opTimes)
          this.getMeta(data)

          if (data.errMsgs !== undefined && data.errMsgs.length > 0) {
            this.result.errMsgs = data.errMsgs
            return Promise.reject('详情查看错误信息')
          } else {
            return Promise.resolve(1)
          }
        })
        .catch((error) => {
          this.$message({
            showClose: true,
            message: '文件处理失败! ' + error,
            center: true,
            duration: 4000,
            type: 'error'
          })
          return Promise.resolve(0)
        })
    },
    // 获取文件信息
    getMeta(data) {
      if (data.fileMeta !== undefined) {
        this.classifier = data.fileMeta.Classifier
        this.keyWords = data.fileMeta.KeyWords
        this.summary = data.fileMeta.Summary
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/styles/mixin.scss";


.grid-content-outline {
  border-radius: 8px;
  vertical-align: middle;
  overflow-y: auto;
  height: 810px;
}

/* 对话框底部按钮区域 */
.dialog-footer {
  display: flex;
  justify-content: space-between;
}

.reset-button {
  order: 1;
}

.right-buttons {
  order: 2;
}

.container {
  padding-bottom: 2%;
  padding-left: 8%;
  padding-right: 8%;
}

.el-row {
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

.el-col {
  border-radius: 8px;
  border: 1px dashed #d9d9d9;
  background-color: #ffffff;
  color: #666666;
  padding: 1%;
}

.bg-purple-dark {
  background: #99a9bf;
}

.bg-purple {
  background: #d3dce6;
}

.bg-purple-light {
  background: #e5e9f2;
}

.grid-content {
  border-radius: 8px;
  vertical-align: middle;
}

.row-bg {
  padding: 10px 0;
  background-color: #f9fafc;
}

.upload-container {
  width: 100%;
  padding: 1%;
  position: relative;
  @include clearfix;

  .params-container {
    width: 70%;
    display: inline-block;
    padding-left: 1%;
    padding-right: 1%;
  }

  .params {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }

  .param-label {
    width: 50%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-bottom: 2%;
  }

  .param-input {
    width: 50%;
    text-align: right;
    margin-bottom: 2%;
  }
  .download-doc-btn {
  text-align: center;
  width: 150%;
  height: 4vh;
  font-size: 2vh;
  line-height: 4vh;
  color: #fff;
  background-color: #338ef0;
  border: none;
  border-radius: 4px;
  margin-top: 8px;
}
}</style>
