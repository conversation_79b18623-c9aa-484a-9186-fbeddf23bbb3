import request from '@/utils/request'

// 查询白名单列表
export function listWhiteList(query) {
  return request({
    url: '/api/v1/chatfile/white_list',
    method: 'get',
    params: query
  })
}

// 新增WhiteList
export function addWhiteList(data) {
  console.log('data:', data)
  return request({
    url: '/api/v1/chatfile/white_list',
    method: 'post',
    data: data
  })
}

// 修改WhiteList
export function updateWhiteList(data) {
  return request({
    url: '/api/v1/chatfile/white_list/' + data.id,
    method: 'put',
    data: { status: data.status, updateBy: data.updateBy }
  })
}

// 编辑WhiteList
export function editWhiteList(data) {
  console.log('data:', data)
  return request({
    url: '/api/v1/chatfile/white_list/edit/' + data.id,
    method: 'post',
    data: { model_name: data.model_name, provider: data.provider }
  })
}

// 删除WhiteList
export function delWhiteList(id) {
  return request({
    url: '/api/v1/chatfile/white_list/' + id,
    method: 'delete'
  })
}

