import request from '@/utils/request'

// 查询TableExtractRequests列表
export function listTableExtractRequests(query) {
  return request({
    url: '/api/v1/request',
    method: 'get',
    params: query
  })
}

// 查询TableExtractRequests详细
export function getTableExtractRequests(docid) {
  return request({
    url: '/api/v1/table-extract-requests/' + docid,
    method: 'get'
  })
}

// 新增TableExtractRequests
export function addTableExtractRequests(data) {
  return request({
    url: '/api/v1/table-extract-requests',
    method: 'post',
    data: data
  })
}

// 修改TableExtractRequests
export function updateTableExtractRequests(data) {
  return request({
    url: '/api/v1/table-extract-requests/' + data.docid,
    method: 'put',
    data: data
  })
}

// 删除TableExtractRequests
export function delTableExtractRequests(data) {
  return request({
    url: '/api/v1/table-extract-requests',
    method: 'delete',
    data: data
  })
}

