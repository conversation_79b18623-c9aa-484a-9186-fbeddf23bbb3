import request from '@/utils/request'

// accesstoken 获取
export function accesstoken(data) {
  return request({
    url: '/api/v1/access_token',
    method: 'post',
    data
  })
}

// login 登陆
export function login(data) {
  return request({
    url: '/api/v1/login',
    method: 'post',
    data
  })
}

// logout 退出
export function logout() {
  return request({
    url: '/api/v1/logout',
    method: 'post'
  })
}

// refreshtoken 刷新token
// 客户端目前没有对token进行刷新的功能，有需要可以微调代码就能支持
export function refreshtoken() {
  return request({
    url: '/api/v1/refresh_token',
    method: 'get'
  })
}

// getInfo 获取用户基本信息
export function getInfo() {
  return request({
    url: '/api/v1/getinfo',
    method: 'get'
  })
}

