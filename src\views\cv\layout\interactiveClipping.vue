<template>
  <!-- 创建一个卡片式的容器，使用主题内容区域将背景色统一 -->
  <el-card>
    <!-- 标题插槽：显示使用说明 -->
    <div slot="header">
      <CVHint>使用说明：（交互式抠图）选择一张图片，在左侧原图上用框/点标出需要抠图的区域，点击相应的按钮，抠图结果会在右侧显示</CVHint>
      <CVCint :hint-content="['按钮说明:', '1) add box: 用蓝色框在原图上标出需要抠图的区域； 2) add point: 鼠标在原图上点击需要抠图的区域； 3) delete point: 鼠标在原图上点击不需要抠图的区域。', '4) 开始精修：进行过抠图，有Clipping Result时，会显示该按钮，点击获取精修mask结果。', '注意: add box只在一开始有效，并且如果使用了框，后续add point只能选择框附近的区域进行添加才有效。']" />
    </div>

    <!-- 主体内容 -->
    <div>
      <div class="result-row">
        <el-row>
          <el-col v-if="containerHeight && containerWidth" :md="12" :style="{height: containerHeight, width: containerWidth}">
            <span class="image-label">Original Image:</span>
            <vue-cropper
              ref="cropper"
              :img="options.img"
              :info="true"
              :can-scale="false"
              :view-mode-options="{ aspectRatio: containerWidth / containerHeight }"
              :auto-crop="options.autoCrop"
              :auto-crop-width="containerWidth"
              :auto-crop-height="containerHeight"
              :fixed-box="options.fixedBox"
              :can-move="false"
              @click.native="getClickCoordinates($event)"
            >
              <!-- <canvas ref="canvas" :width="containerWidth" :height="containerHeight" /> -->
            <!-- <canvas id="myCanvas" style="position: absolute; top: 0; left: 0;" /> -->
            </vue-cropper>
          </el-col>
          <el-col v-if="showResult" :md="12" :style="{height: containerHeight, width: containerWidth}">
            <span class="image-label">Clipping Result:</span>
            <!-- <el-image :src="url" /> -->
            <div class="image-container">
              <el-image :src="options.img" />
              <div class="segmentation-result" :style="{ backgroundImage: 'url(' + url + ')' }" />
              <div v-for="(y, index) in scaleAddPointImgY" :key="index" class="marker-addpoint" :style="{ top: y + 'px', left: scaleAddPointImgX[index] + 'px' }" />
              <div v-for="(y, index) in scaleDeletePointImgY" :key="index" class="marker-deletepoint" :style="{ top: y + 'px', left: scaleDeletePointImgX[index] + 'px' }" />
            </div>
          </el-col>
        </el-row>
      </div>
      <br>

      <!-- action="placeholder" -->
      <br>
      <div class="result-row">
        <el-upload action="#" :http-request="submitUpload" :show-file-list="false" :before-upload="beforeUpload">
          <el-button type="primary" size="small" style="width: 100px;">
            选择图片
            <i class="el-icon-upload el-icon--right" />
          </el-button>
        </el-upload>
        <el-row>
          <el-col :span="8">
            <el-button type="success" size="small" style="margin-left: 10px;" :disabled="disabled" @click="getCoordinates()">add box</el-button>
          </el-col>
          <el-col :span="8">
            <el-button type="success" size="small" @click="addpoint()">add point</el-button>
          </el-col>
          <el-col :span="8">
            <el-button type="warning" size="small" :disabled="deleteDisabled" @click="deletepoint()">delete point</el-button>
          </el-col>
        </el-row>
        <el-row style="margin-left: 10px;">
          <el-button v-if="origin_url!=''&&mask_url!=''" type="primary" size="small" :disabled="deleteDisabled" @click="maskRefine()">开始精修</el-button>
        </el-row>
      </div>
      <br>

      <!-- <div class="result-row" style="height: 2000px;"> -->
      <div v-if="showResult" class="result-row" :style="{height: maskHeight + 'px'}">
        <el-row>
          <el-col :md="12" :style="{height: containerHeight, width: containerWidth}">
            <span class="image-label" style="margin-right: 20px;">Mask:</span>
            <el-button type="text" size="large" @click="copyLink(url)">复制链接</el-button>
            <el-image :src="url" class="image-with-margin" />
          </el-col>
          <el-col v-if="showRefineResult" :md="12" :style="{height: containerHeight, width: containerWidth}">
            <span class="image-label" style="margin-right: 20px;">Mask-Refine:</span>
            <el-button type="text" size="large" @click="copyLink(refine_url)">复制链接</el-button>
            <el-image :src="refine_url" class="image-with-margin" />
          </el-col>
        </el-row>
      </div>
    </div>
    <br>

    <!-- 底部插槽：未使用，后续若是扩展可以考虑翻页等功能 -->
    <div slot="footer">
      footer
    </div>
  </el-card>
</template>

<script>
// import store from '@/store'
import CVHint from './components/CVHint'
import CVCint from './components/CVCint'
import { VueCropper } from 'vue-cropper'
// import VueCanvas from 'vue-canvas'
import axios from 'axios'
import { getHeaders } from '@/utils/get-header'

export default {
  components: { CVHint, CVCint, VueCropper },
  props: {
    // eslint-disable-next-line vue/require-default-prop
    user: { type: Object }
  },
  data() {
    return {
      // 保存点击位置的数组
      imgX: 0,
      imgY: 0,
      // 保存缩放后点击位置的数组
      scaleAddPointImgX: [],
      scaleAddPointImgY: [],
      scaleDeletePointImgX: [],
      scaleDeletePointImgY: [],
      // 是否显示弹出层
      open: true,
      // 弹出层标题
      title: '修改头像',
      options: {
        // img: store.getters.avatar, // 裁剪图片的地址
        img: 'https://zhai-datas.ks3-cn-beijing.ksyuncs.com/layout/people/wangluoyan/segmentation/%E9%80%89%E6%8B%A9%E4%B8%80%E5%BC%A0%E5%9B%BE%E7%89%87.png',
        autoCrop: true, // 是否默认生成截图框
        autoCropWidth: 10, // 默认生成截图框宽度
        autoCropHeight: 10, // 默认生成截图框高度
        // fixedBox: true // 固定截图框大小 不允许改变
        fixedBox: false // 固定截图框大小 不允许改变
      },
      previews: {},
      // 图片的缩放比例
      scale: 1,
      scaleFlag: false,
      // 图片展示宽度
      imageWidth: 600,
      url: 'https://zhai-datas.ks3-cn-beijing.ksyuncs.com/layout/people/wangluoyan/segmentation/%E9%80%89%E6%8B%A9%E4%B8%80%E5%BC%A0%E5%9B%BE%E7%89%87.png',
      containerHeight: '',
      containerWidth: '',
      uploadurl: {
        value: '//pcv-test.wps.cn',
        default: '//pcv-test.wps.cn'
      },
      clippingurl: {
        value: '//pcv-test-scan.wps.cn',
        default: '//pcv-test-scan.wps.cn'
      },
      // 图片唯一标识
      key_id: '',
      // 显示结果
      showResult: false,
      // points: [],
      // labels: [],
      // 标志位
      addpointFlag: false,
      deletepointFlag: false,
      disabled: false,
      deleteDisabled: true,
      // mask所在高度
      maskHeight: 0,
      // 表示画框的左上角坐标
      left_top: [],
      // 表示画框的右下角坐标
      right_bottom: [],
      // 添加点
      add: [],
      // 消除点
      delete: [],
      // 原图url
      origin_url: '',
      // 抠图url
      mask_url: '',
      // 精修url
      refine_url: '',
      // 显示精修结果
      showRefineResult: false
    }
  },
  mounted() {
    this.getImageSize()
  },
  methods: {
    /** 开始精修按钮 */
    maskRefine() {
      // 禁用 add box
      this.disabled = true
      if (this.origin_url !== '' && this.mask_url !== '') {
        var data_dict = {
          'image_url': this.origin_url,
          'mask_url': this.mask_url
        }
        return axios({
          method: 'post',
          headers: { 'Cache-Control': 'no-cache', 'Content-Type': 'application/json', ...getHeaders() },
          url: this.clippingurl.value + '/matting/sam/mask-refine',
          data: data_dict,
          responseType: 'json',
          processData: false,
          contentType: false,
          timeout: 80000 // 设置超时时间为80秒
        }).then(res => {
          if (res.data.code !== 200 || res.data.data === undefined || res.data.data.result_url === undefined) {
            return Promise.reject('上传图片失败!')
          }

          // 处理完成
          console.log('上传图片完成: ' + JSON.stringify(res.data.data.result_url))
          this.refine_url = res.data.data.result_url

          // 显示精修结果
          this.showRefineResult = true
        })
      }
    },
    copyLink(url) {
      var inputNode = document.getElementById('pdfcv_input')
      if (inputNode != null) {
        inputNode.remove()
      }
      inputNode = document.createElement('input')
      // inputNode.value = this.url
      inputNode.value = url
      inputNode.id = 'pdfcv_input'
      document.body.appendChild(inputNode)
      inputNode.select()
      document.execCommand('Copy')
      inputNode.className = 'oInput'
      inputNode.style.display = 'none'
      this.$message.success('复制成功')
    },
    getImageSize() {
      var image = new Image()
      // image.src = this.url
      image.src = this.options.img

      image.onload = () => {
        var imageWidth = image.width
        var imageHeight = image.height

        if (imageWidth > 660) {
          this.scaleFlag = true
          var originalAspectRatio = imageWidth / imageHeight
          // 四舍五入保留一位小数
          this.scale = parseFloat((650 / imageWidth).toFixed(1))
          imageWidth = imageWidth * this.scale
          imageHeight = imageWidth / originalAspectRatio
        }

        this.containerHeight = imageHeight + 'px'
        this.containerWidth = imageWidth + 'px'
        // 在原始图像高度上预留100px
        this.maskHeight = imageHeight + 100
      }
    },
    getCanvasSize() {
      this.$nextTick(() => {
        // const container = this.$refs.cropper.$el
        const canvas = this.$refs.canvas
        // 设置canvas的大小与容器元素一致
        canvas.width = this.containerWidth
        canvas.height = this.containerHeight
      })
    },
    // 图片缩小放大
    computedImageWidth() {
      return this.imageWidth * this.scale
    },
    // 编辑头像
    editCropper() {
      this.open = true
    },
    // 覆盖默认的上传行为
    requestUpload() {
    },
    // 向左旋转
    rotateLeft() {
      this.$refs.cropper.rotateLeft()
    },
    // 向右旋转
    rotateRight() {
      this.$refs.cropper.rotateRight()
    },
    // 图片缩放
    changeScale(num) {
      num = num || 1
      this.$refs.cropper.changeScale(num)
    },
    // 上传预处理
    beforeUpload(file) {
      // 复位
      this.resetResult()
      if (file.type.indexOf('image/') === -1) {
        this.msgError('文件格式错误，请上传图片类型,如：JPG，PNG后缀的文件。')
      } else {
        const reader = new FileReader()
        reader.readAsDataURL(file)
        reader.onload = () => {
          this.options.img = reader.result
          this.getImageSize()
        }
      }
    },
    // 重置
    resetResult() {
      // 开启add box
      this.disabled = false
      // 禁用delete point
      this.deleteDisabled = true
      // 缩放比例复位
      this.scaleFlag = false
      // this.labels = []
      // this.points = []
      this.left_top = []
      this.right_bottom = []
      this.add = []
      this.delete = []
      this.showResult = false
      this.showRefineResult = false
      this.scaleAddPointImgX = []
      this.scaleAddPointImgY = []
      this.scaleDeletePointImgX = []
      this.scaleDeletePointImgY = []
      // mask显示高度复位
      this.maskHeight = 0
    },
    // 上传到服务器
    async submitUpload(param) {
      this.fileName = param.file.name
      const form = new FormData()
      form.append('file', param.file)
      form.append('origin', 'pdf-cv-demo')
      const axios_config_upload = {
        method: 'post',
        headers: { 'Cache-Control': 'no-cache', ...getHeaders() },
        url: this.uploadurl.value + '/layout/scheduler/samplecollect/upload',
        data: form,
        responseType: 'json',
        processData: false,
        contentType: false
      }
      console.log('等待上传...')
      // 上传
      await axios(axios_config_upload).then(res => {
        this.url = res.data.data.url
      })
      // 请求
      const s_time = new Date().getTime()
      const status = await this.clippingEncoder(this.url)
      const e_time = new Date().getTime()
      // 状态和处理用时
      this.statistic = {
        status,
        duration: this.result.duration || (e_time - s_time) / 1000
      }
      return Promise.resolve()
    },
    // 获取上传图片的key_id
    clippingEncoder(url) {
      // 对传入的url进行处理
      this.origin_url = url.toString()
      // urlString = urlString.replace(/-internal/g, '')
      var data_dict = {}
      data_dict = {
        'image_url': this.origin_url,
        'use_cache': true
      }
      return axios({
        method: 'post',
        headers: { 'Cache-Control': 'no-cache', 'Content-Type': 'application/json', ...getHeaders() },
        url: this.clippingurl.value + '/matting/sam/interact-encoder',
        data: data_dict,
        responseType: 'json',
        processData: false,
        contentType: false,
        timeout: 80000 // 设置超时时间为80秒
      }).then(res => {
        if (res.data.code !== 200 || res.data.data === undefined || res.data.data.key_id === undefined) {
          return Promise.reject('上传图片失败!')
        }

        // 处理完成
        console.log('上传图片完成: ' + JSON.stringify(res.data.data))
        this.key_id = res.data.data.key_id
        this.deletepointFlag = false
        this.addpointFlag = false
      }).catch((error) => {
        this.$message({
          showClose: true,
          message: '上传图片失败! ' + error,
          center: true,
          duration: 4000,
          type: 'error'
        })
        return Promise.resolve(0)
      })
    },
    // 获取框坐标值
    async getCoordinates() {
      // 获取图片基于容器的坐标点,包含左上角和右下角坐标信息
      const imgAxis = this.$refs.cropper.getImgAxis()
      // 获取截图框基于容器的坐标点
      const cropAxis = this.$refs.cropper.getCropAxis()

      // 判断是否缩放
      var scale = 1
      if (this.scaleFlag) {
        scale = this.scale
      }
      // 左上角坐标点, 图片基于容器的坐标点若是为负数, 则设置为0
      var cropBoxLeftX = (cropAxis.x1 - Math.max(0, imgAxis.x1)) / scale
      var cropBoxLeftY = (cropAxis.y1 - Math.max(0, imgAxis.y1)) / scale
      // 右下角坐标点
      var cropBoxRightX = (cropAxis.x2 - Math.max(0, imgAxis.x1)) / scale
      var cropBoxRightY = (cropAxis.y2 - Math.max(0, imgAxis.y1)) / scale
      // 四舍五入坐标值, 保留一位小数
      cropBoxLeftX = parseFloat(cropBoxLeftX.toFixed(1))
      cropBoxLeftY = parseFloat(cropBoxLeftY.toFixed(1))
      cropBoxRightX = parseFloat(cropBoxRightX.toFixed(1))
      cropBoxRightY = parseFloat(cropBoxRightY.toFixed(1))

      // this.labels.push(2, 3)
      // this.points.push([cropBoxLeftX, cropBoxLeftY], [cropBoxRightX, cropBoxRightY])
      this.left_top.push(cropBoxLeftX, cropBoxLeftY)
      this.right_bottom.push(cropBoxRightX, cropBoxRightY)

      // 启用delete point
      this.deleteDisabled = false
      // 获取抠图结果
      await this.clippingDecoder()
    },
    clippingDecoder() {
      // 若是还没有获得key_id
      if (this.key_id === '') {
        return Promise.reject('请先选择一张上传图片!')
      }
      var data_dict = {}
      if (this.left_top.length !== 0) {
        // 若是一开始画框
        data_dict = {
          'key_id': this.key_id,
          // 'points': this.points,
          // 'labels': this.labels
          'action': {
            'left_top': this.left_top,
            'right_bottom': this.right_bottom,
            'add': this.add,
            'delete': this.delete
          }
        }
      } else {
        // 一开始没画框
        data_dict = {
          'key_id': this.key_id,
          'action': {
            'add': this.add,
            'delete': this.delete
          }
        }
      }
      return axios({
        method: 'post',
        headers: { 'Cache-Control': 'no-cache', 'Content-Type': 'application/json', ...getHeaders() },
        url: this.clippingurl.value + '/matting/sam/interact-decoder',
        data: data_dict,
        responseType: 'json',
        processData: false,
        contentType: false,
        timeout: 80000 // 设置超时时间为80秒
      }).then(res => {
        if (res.data.code !== 200 || res.data.data === undefined || res.data.data.result_url === undefined) {
          return Promise.reject('抠图失败!')
        }

        // 处理完成
        console.log('抠图完成: ' + JSON.stringify(res.data.data))
        this.url = res.data.data.result_url
        this.mask_url = res.data.data.result_url

        // 显示结果
        this.showResult = true
      }).catch((error) => {
        this.$message({
          showClose: true,
          message: '抠图失败! ' + error,
          center: true,
          duration: 4000,
          type: 'error'
        })
        return Promise.resolve(0)
      })
    },
    // 获取鼠标点击的坐标值
    getClickCoordinates(event) {
      // 获取容器元素
      const container = event.currentTarget
      // 获取容器元素相对于视口的位置信息
      const rect = container.getBoundingClientRect()
      // 获取图片基于容器的坐标点
      const imgAxis = this.$refs.cropper.getImgAxis()

      // 获取鼠标点击点相对于容器元素的坐标值
      const clickX = event.clientX - rect.left
      const clickY = event.clientY - rect.top

      // 计算鼠标点击点相对于原图的坐标值
      var imgX = imgAxis.x1 + (clickX * (imgAxis.x2 - imgAxis.x1) / rect.width)
      var imgY = imgAxis.y1 + (clickY * (imgAxis.y2 - imgAxis.y1) / rect.height)

      // 判断是否缩放
      var scale = 1
      if (this.scaleFlag) {
        scale = this.scale
      }
      // 四舍五入坐标值, 保留一位小数
      this.imgX = parseFloat((imgX / scale).toFixed(1))
      this.imgY = parseFloat((imgY / scale).toFixed(1))

      // 判断是否调用add point或者delete point
      if (this.addpointFlag === true) {
        // 存储add point的绿点
        this.scaleAddPointImgX.push(imgX)
        this.scaleAddPointImgY.push(imgY)
        this.point(1)
      } else if (this.deletepointFlag === true) {
        // 存储delete point的黄点
        this.scaleDeletePointImgX.push(imgX)
        this.scaleDeletePointImgY.push(imgY)
        this.point(0)
      }
    },
    drawRedDot() {
      const canvas = this.$refs.canvas
      const ctx = canvas.getContext('2d')

      // 清空画布
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      // 绘制红点
      ctx.fillStyle = 'red'
      ctx.beginPath()
      ctx.arc(this.imgX, this.imgY, 5, 0, Math.PI * 2)
      ctx.closePath()
      ctx.fill()
    },
    async point(labels) {
      // 若是用户未在图片上点击, 默认坐标为(0, 0)
      // var point = [this.imgX, this.imgY]
      // this.labels.push(labels)
      // this.points.push(point)
      var point = [this.imgX, this.imgY]
      if (labels === 1) {
        this.add.push(point)
      } else if (labels === 0) {
        this.delete.push(point)
      }

      // 获取抠图结果
      await this.clippingDecoder()
    },
    deletepoint() {
      // this.point(0)
      // 禁用add box
      this.disabled = true
      this.addpointFlag = false
      this.deletepointFlag = true
      this.resetPosition()
    },
    addpoint() {
      // this.point(1)
      // 禁用add box
      this.disabled = true
      this.deletepointFlag = false
      this.addpointFlag = true
      // 启用delete point
      this.deleteDisabled = false
      this.resetPosition()
    },
    resetPosition() {
      // 重置原图位置
      this.ImgX = 0
      this.ImgY = 0
    },
    // 实时预览
    realTime(data) {
      this.previews = data
    }
  }
}
</script>
<style lang="scss" scoped>
@import "~@/styles/mixin.scss";

.vue-cropper-preview {
position: relative;
width: 180px;
height: 180px;
border-radius: 50%;
box-shadow: 0 0 4px #ccc;
overflow: hidden;
}

.img-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.result-row {
  display: flex;
  justify-content: center;
  align-items: center;
}

.image-label {
  margin-top: 10px;
  font-size: 16px;
  background-color: #333;
  color: #fff;
  padding: 5px 10px;
}

.color-marker {
  position: absolute;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: red;
}

.button-row {
  display: flex;
  justify-content: space-between;
}

.image-container {
  position: relative;
}

.segmentation-result {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-repeat: no-repeat;
  background-size: cover;
  z-index: 2;
  filter: invert(0%) sepia(100%) saturate(100%) brightness(1000%) opacity(70%) hue-rotate(90deg);
}

.coordinate-marker {
  position: absolute;
  color: purple;
  font-size: 20px;
  z-index: 2;
  filter: unset; /* 重置 filter 属性 */
}

.marker-addpoint {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 10px;
  height: 10px;
  background-color: rgb(4, 248, 4);
  border-radius: 50%;
  z-index: 2;
  filter: unset; /* 重置 filter 属性 */
}

.marker-deletepoint {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 10px;
  height: 10px;
  background-color: rgb(248, 244, 4);
  border-radius: 50%;
  z-index: 2;
  filter: unset; /* 重置 filter 属性 */
}

.image-with-margin {
  margin-bottom: 20px;
}

</style>
