const default_param = {
  protocol: 'https',
  base: 'pcv-test-ceph.wps.cn'
}

export function testsetUrlMaker(param) {
  const rest = param.set === 'testset'
    ? ('/testset/testset/' + param.app + '/' + param.version)
    : ('/testset/tempset/' + param.app + '/' + param.group)
  const protocol = param.protocol !== undefined ? param.protocol : default_param.protocol
  const base = param.base !== undefined ? param.base : default_param.base
  return protocol + '://' + base + rest
}
