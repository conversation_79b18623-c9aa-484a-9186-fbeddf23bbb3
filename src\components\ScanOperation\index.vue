<template>
  <div>
    <el-button
      v-if="showOriginUrl"
      size="mini"
      type="text"
      @click="showCopyDiag('originUrl')"
    > 原图 |
    </el-button>
    <el-button
      v-if="showResultUrl"
      size="mini"
      type="text"
      @click="showCopyDiag('resultUrl')"
    > 效果图
    </el-button>
    <el-dialog :visible.sync="open" width="500px">
      <div>
        <ul>
          <li v-for="(item, index) in imgUrlsCopy" :key="index">
            <el-link
              slot="reference"
              type="primary"
              :href="formatUrl(item)"
              :disabled="item===''"
              download="a"
              target="_blank"
            >
              {{ imgNames[index] }}
            </el-link>
          </li>
        </ul>
      </div>
    </el-dialog>

    <el-dialog :visible.sync="copyOpen" width="300px">
      <div>
        <div :style="styleObject">
          <ul>
            <li v-for="(item, index) in imgUrlsCopy" :key="index">
              <el-button
                type="text"
                size="mini"
                :disabled="item===''"
                @click="copyLink(item)"
              >复制图片:{{ imgNames[index] }}</el-button>
            </li>
          </ul>
        </div>
      </div>
    </el-dialog>

    <div v-if="showExtraInfoData">
      <el-dialog :visible.sync="extraOpen" :height="extraInfoDataHeight">
        <el-table :data="extraInfoData" border :height="extraInfoDataHeight">
          <el-table-column fixed prop="name" label="名称" width="100px" />
          <el-table-column prop="value" label="信息" max-width="700px">
            <template slot-scope="scope">
              <div v-if="dataObject(scope.row.value)">
                <pre style="overflow:auto;"><code>{{ JSON.stringify(scope.row.value, null, 4).replace(/\"/g, "") }}</code></pre>
              </div>
              <div v-else>{{ scope.row.value }}</div>
            </template>
          </el-table-column>
        </el-table>
      </el-dialog>
    </div>

    <!-- 添加或修改对话框 -->
    <el-dialog :visible.sync="approvalOpen" :title="title" width="500px">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="申请目的" prop="purposes">
          <div>
            <el-input v-model="form.purposes" placeholder="默认申请目的为：获取[scan]下载链接" type="textarea" rows="1" />
          </div>
        </el-form-item>
        <el-form-item label="审批人" prop="approver_id">
          <div>
            <el-radio v-for="option in approverList" :key="option.value" v-model="form.approver_id" :label="option.value">
              {{ option.label }}
            </el-radio>
          </div>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="填写需要补充的信息" type="textarea" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-image-viewer
      v-if="showViewer"
      style="width: 98%; height: 98%"
      :on-close="closeViewer"
      :url-list="viewPhotoList"
    />
  </div>
</template>
<script>
import { string } from 'clipboard'
import elImageViewer from 'element-ui/packages/image/src/image-viewer.vue'
import { getScanCopyLink } from '@/api/scan/scan-request'
import { postCvApproval } from '@/api/layout'

export default {
  name: 'ScanOperation',
  components: {
    elImageViewer
  },
  props: {
    approverList: {
      type: Array,
      required: true
    },
    showImg: {
      type: Boolean,
      default: false
    },
    // showUrl: {
    //   type: Boolean,
    //   default: false
    // },
    showOriginUrl: {
      type: Boolean,
      default: false
    },
    showResultUrl: {
      type: Boolean,
      default: false
    },
    imgUrls: {
      type: Array,
      default: () => []
    },
    imgNames: {
      type: Array,
      default: () => []
    },
    dialog: {
      type: Boolean,
      default: false
    },
    requestid: {
      type: string,
      default: ''
    },
    dowdate: {
      type: string,
      default: ''
    }
  },
  data() {
    return {
      scan_url: 21,
      scan_extra_info: 22,
      imgUrlsCopy: [...this.imgUrls],
      extraOpen: false,
      open: false,
      copyOpen: false,
      showViewer: false,
      viewPhotoList: [],
      styleObject: {
        overflow: 'auto',
        height: this.imgUrls.length > 20 ? '500px' : '200px'
      },
      urlButtonDisabled: false,
      // extraInfoData: {}
      extraInfoData: [],
      extraInfoDataLength: 0,
      // 高度阈值
      height: 200,
      // 初始高度
      extraInfoDataHeight: 250,
      showExtraInfoData: false,
      // 是否开启审批表单
      approvalOpen: false,
      // 标题
      title: '创建审批-获取图片链接',
      // 表单参数
      form: {
        purposes: '获取[scan]下载链接',
        doc_id: ''
      },
      // 表单校验
      rules: {
        approver_id: [{ required: true, message: '选择一个审批人', trigger: 'blur' }]
      },
      // 文件类型
      fileType: ''
    }
  },
  created() {
  },
  methods: {
    // 获取图片链接
    getImgLink() {
      return new Promise((resolve, reject) => {
        getScanCopyLink(this.scan_url, this.requestid).then(response => {
          this.imgUrlsCopy = response.data
          resolve()
        }).catch(error => {
          console.log('Error:', error)
          reject(error)
        })
      })
    },
    // 获取表单参数
    getFormParams() {
      this.form.doc_id = this.requestid
      this.form.function = 'scan'
      this.form.approver_id = parseInt(this.form.approver_id)
      this.form.file_type = this.fileType
      if (this.dowdate !== '') {
        const formatted = `${this.dowdate.substring(0, 4)}-${this.dowdate.substring(4, 6)}-${this.dowdate.substring(6, 8)}`
        this.form.starttime = formatted
      }
    },
    /** 提交按钮 */
    submitForm: function() {
      this.getFormParams()
      this.$refs['form'].validate((valid) => {
        if (valid) {
          postCvApproval(this.form).then((response) => {
            this.reset()
            if (response.code === 200) {
              this.msgSuccess(response.msg)
              this.approvalOpen = false
              // this.getList()
            } else {
              this.msgError(response.msg)
            }
          })
        } else {
          this.$message({
            showClose: true,
            message: '参数设置有误，审批表单提交失败! ',
            center: true,
            type: 'info'
          })
        }
      })
    },
    // 取消按钮
    cancel() {
      this.approvalOpen = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        remark: '',
        purposes: '获取[scan]下载链接',
        approver_id: undefined,
        starttime: '',
        file_type: '',
        function: '',
        doc_id: undefined
      }
      this.resetForm('form')
    },
    // 获取图片链接
    showCopyDiag(fileType) {
      this.fileType = fileType
      // 开启审批表单
      this.approvalOpen = true
    },
    /* 复制链接按钮 */
    // showCopyDiag() {
    //   if (this.showImg === true) {
    //     this.getImgLink()
    //   }
    //   this.copyOpen = true
    // },
    copyLink(url) {
      var inputNode = document.getElementById('scan_input')
      if (inputNode != null) {
        inputNode.remove()
      }
      inputNode = document.createElement('input')
      inputNode.value = url
      inputNode.id = 'scan_input'
      document.body.appendChild(inputNode)
      inputNode.select()
      document.execCommand('Copy')
      inputNode.className = 'oInput'
      inputNode.style.display = 'none'
      this.$message.success('复制成功')
    },
    formatUrl(url) {
      var s = url === undefined ? '' : url
      return s.replaceAll('"', '').replace('-internal', '')
    },
    handleCancel() {
      this.$emit('cancel')
    },
    handleConfirm() {
      this.$emit('confirm')
    },
    /* 图片明细：图片弹出框 */
    async handlePopImgs(row) {
      if (this.imgUrlsCopy.length === 0) {
        await this.getImgLink()
      }
      this.open = true
    },
    // 获取额外信息
    getExtraInfo() {
      return new Promise((resolve, reject) => {
        getScanCopyLink(this.scan_extra_info, this.jobid).then(response => {
          // 获取额外信息长度
          this.extraInfoDataLength = response.data[0].length
          // 复位高度
          this.extraInfoDataHeight = 250
          this.getExtraInfoDataHeight()
          this.extraInfo = JSON.parse(response.data)
          resolve()
        }).catch(error => {
          console.log('Error:', error)
          reject(error)
        })
      })
    },
    /* 额外信息 */
    async openExtraInfo() {
      this.extraOpen = true
      await this.getExtraInfo()
      // 绑定表格数据
      if (this.extraInfo !== undefined && this.extraInfo !== null) {
        this.extraInfoData = this.getExtraInfoData()
      } else {
        this.extraInfoData = []
      }
    },
    closeViewer() {
      this.showViewer = false
    },
    /* 图片预览 */
    async onPreview() {
      await this.getImgLink()
      const temp = []
      this.imgUrlsCopy.forEach((e) => {
        if (e !== undefined && e !== '') {
          temp.push(this.formatUrl(e))
        }
      })
      this.viewPhotoList = temp
      this.showViewer = true
    },
    dataObject(info) {
      try {
        return Object.prototype.toString.call(info) === '[object Object]'
      } catch (e) {
        return false
      }
    },
    // 得到额外信息显示框的高度
    getExtraInfoDataHeight() {
      // 额外信息长度大于高度阈值
      if (this.extraInfoDataLength > this.height) {
        // 向下取整
        const floorMultiple = Math.floor(this.extraInfoDataLength / this.height)
        if (floorMultiple !== 1) {
          // 每个倍数增加50
          this.extraInfoDataHeight = 250 + (floorMultiple - 1) * 50
        }
      }
      this.showExtraInfoData = true
    },
    // 转换额外信息表格显示的格式
    getExtraInfoData() {
      return Object.entries(this.extraInfo).map(([name, value]) => {
        return { name, value }
      })
    }
  }
}
</script>
