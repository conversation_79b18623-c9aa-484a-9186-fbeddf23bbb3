import request from '@/utils/request'

// 查询RequestsDayStatistics列表
export function listRequestsDayStatistics(query) {
  return request({
    url: '/api/v1/requests-day-statistics',
    method: 'get',
    params: query
  })
}

// 查询RequestsDayStatistics详细
export function getRequestsDayStatistics(date) {
  return request({
    url: '/api/v1/requests-day-statistics/' + date,
    method: 'get'
  })
}

// 新增RequestsDayStatistics
export function addRequestsDayStatistics(data) {
  return request({
    url: '/api/v1/requests-day-statistics',
    method: 'post',
    data: data
  })
}

// 修改RequestsDayStatistics
export function updateRequestsDayStatistics(data) {
  return request({
    url: '/api/v1/requests-day-statistics/' + data.date,
    method: 'put',
    data: data
  })
}

// 删除RequestsDayStatistics
export function delRequestsDayStatistics(data) {
  return request({
    url: '/api/v1/requests-day-statistics',
    method: 'delete',
    data: data
  })
}

