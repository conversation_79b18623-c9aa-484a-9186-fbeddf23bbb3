<template>
  <BasicLayout>
    <template #wrapper>
      <el-card class="box-card">
        <el-row :gutter="24">
          <!--模型数据-->
          <el-col :span="24" :xs="24">
            <el-form ref="queryForm" :model="queryParams" :inline="true" label-width="68px">
              <el-form-item label="模型名称" prop="name">
                <el-input v-model="queryParams.name" placeholder="请输入模型名称" clearable size="small" style="width: 100%"
                  @keyup.enter.native="handleQuery" />
              </el-form-item>
              <el-form-item label="标签" prop="labels">
                <el-select
                  v-model="query_lables"
                  multiple
                  placeholder="请选择"
                  style="width: 100%">
                  <el-option
                    v-for="item in model_labels"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                <el-button icon="el-icon-s-home" size="mini" @click="resetQuery" v-if="FoldersPid != 1">根目录</el-button>
                <el-button icon="el-icon-back" size="mini" v-if="FoldersPid != 1"
                  @click="handleLastFolderClick">返回上层文件夹</el-button>
                <el-button v-permisaction="['admin:sysmodel:add']" icon="el-icon-document-add" size="mini"
                  @click="handleAddModel">上传模型</el-button>
                <el-button v-permisaction="['admin:sysmodel:add']" icon="el-icon-folder-add" size="mini"
                  @click="handleAddModelFolder">新建文件夹</el-button>
              </el-form-item>
            </el-form>
            <el-table v-loading="loading" :data="modelList" @selection-change="handleSelectionChange">
              <el-table-column type="selection" width="55" align="center" v-if="ShowColumn" />
              <el-table-column label="ID" align="center" prop="id" v-if="ShowColumn" />
              <el-table-column label="PID" align="center" prop="pid" v-if="ShowColumn" />
              <el-table-column label="名称" align="left" prop="name" :show-overflow-tooltip="true">
                <template slot-scope="scope">
                  <div @click="handleModeNameClick(scope.row)">
                    <i v-if="scope.row.is_folder" class="el-icon-folder"></i>
                    <i v-else-if="scope.row.status === 0 && !scope.row.is_folder" class="el-icon-loading"></i>
                    <i v-else-if="scope.row.status === 4 && !scope.row.is_folder" class="el-icon-warning"></i>
                    <i v-else class="el-icon-document"></i>
                    {{ scope.row.name }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="路径" align="center" prop="object_key" :show-overflow-tooltip="true" />

              <el-table-column label="大小" align="center" :show-overflow-tooltip="true">
                <template slot-scope="scope">
                  <span>{{ humanReadableSize(scope.row.file_size) }}</span>
                </template>
              </el-table-column>

              <!-- <el-table-column label="备注" align="center" prop="remark" :show-overflow-tooltip="true" /> -->
              <el-table-column label="更新时间" align="center" prop="updatedAt">
                <template slot-scope="scope">
                  <span>{{ parseTime(scope.row.updatedAt) }}</span>
                </template>
              </el-table-column>
              <!-- <el-table-column label="标签" align="center" prop="labels" :show-overflow-tooltip="true">
                <template slot-scope="scope">
                <el-tag v-for="label in scope.row.labels.split(';')" :key="label">
                  {{ label.trim() }}
                </el-tag>
              </template>
              </el-table-column> -->
              <el-table-column label="归档" width="80" sortable="custom">
                <template slot-scope="scope">
                  <el-switch
                    v-model="scope.row.archive"
                    :active-value=true
                    :inactive-value=false
                    @change="handleArchiveChange(scope.row)"
                  />
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                  <el-button v-if="scope.row.status != 0 && !scope.row.is_folder" v-permisaction="['admin:sysDictType:edit']"
                    size="mini" type="text" icon="el-icon-download" :disabled="scope.row.status != 1"
                    @click="handleDowloadModel(scope.row)">下载</el-button>
                  <el-button v-if="scope.row.status != 0 && !scope.row.is_folder" v-permisaction="['admin:sysDictType:edit']"
                    size="mini" type="text" icon="el-icon-copy-document" :disabled="scope.row.status != 1"
                    @click="copyToClipboard(scope.row.object_key)">复制路径</el-button>
                  <!-- <el-button  v-permisaction="['admin:sysDictType:edit']" size="mini"
                    type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">修改</el-button> -->
                  <el-button v-if="scope.row.is_folder" v-permisaction="['admin:sysDictType:remove']" size="mini"
                    type="text" icon="el-icon-place" @click="handleModeNameClick(scope.row)">进入文件夹</el-button>
                  <el-button v-permisaction="['admin:sysDictType:remove']" size="mini" type="text" icon="el-icon-delete"
                    @click="handleDelete(scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
            <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageIndex"
              :limit.sync="queryParams.pageSize" @pagination="getList" />
          </el-col>
        </el-row>
      </el-card>
      <!-- 添加或修改参数配置对话框 -->
      <el-dialog :title="title" :visible.sync="open" width="600px">
        <el-form ref="form" :model="form" :rules="rules" label-width="80px">
          <el-row>
            <el-col :span="24">
              <el-form-item label="上传方式" prop="upload_type" v-if="!isFolder && !isEdit">
                <div>
                  <el-radio v-model="form.upload_type" :label="upload_type1">链接</el-radio>
                  <el-radio v-model="form.upload_type" :label="upload_type2">保密环境</el-radio>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="24" v-if="!isEdit">
              <el-form-item label="名称" prop="name">
                <el-input v-model="form.name" placeholder="请输入名称"
                  :disabled="form.upload_type === upload_type2 && !isFolder" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="URL地址" prop="upload_url"
                v-if="form.upload_type === upload_type1 && !isFolder && !isEdit">
                <el-input v-model="form.upload_url" placeholder="URL地址" />
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="保密环境" prop="sec_path" v-if="form.upload_type === upload_type2 && !isFolder && !isEdit">
                <el-input v-model="form.sec_path" placeholder="保密环境/data/share目录下的文件绝对路径" />
              </el-form-item>
            </el-col>
            <!-- <el-col :span="24">
              <el-form-item label="标签" prop="labels">
                <el-select v-model="form_lables" multiple placeholder="请选择" style="width: 100%;">
                  <el-option v-for="item in model_labels" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col> -->
            <!-- <el-col :span="24">
              <el-form-item label="备注" prop="remark">
                <el-input v-model="form.remark" type="textarea" />
              </el-form-item>
            </el-col> -->
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog>
      <!-- 下载对话框 -->
      <el-dialog :title="downloadTitle" :visible.sync="downloadOpen" width="500px">
        <el-form ref="downlod_form" :model="form" :rules="rules" label-width="80px">
          <el-form-item label="模型" prop="name">
            <div>
              <el-input v-model="form.name" disabled />
            </div>
          </el-form-item>
          <el-form-item label="目录" prop="object_key">
            <div>
              <el-input v-model="form.object_key" disabled />
            </div>
          </el-form-item>
          <el-form-item label="用途描述" prop="purposes">
            <el-input v-model="form.purposes" placeholder="用途" type="textarea" />
          </el-form-item>

          <el-form-item label="审批人" prop="approver">
            <div>
              <el-radio v-for="option in approver_list" :key="option.value" v-model="form.approver" :label="option.value">
                {{ option.label }}
              </el-radio>
            </div>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="form.remark" placeholder="" type="textarea" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="downloadSubmitForm">确 定</el-button>
          <el-button @click="downloadCancel">取 消</el-button>
        </div>
      </el-dialog>
    </template>
  </BasicLayout>
</template>

<script>
import { addModel, updateModel,updateModelArchive, getModelList, getModel, delModel,getBase } from '@/api/model/model'
import { addUpload2Ks3 } from '@/api/tools/upload2ks3'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'

export default {
  name: 'SysmodelManage',
  components: { Treeselect },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 模型表格数据
      modelList: [],
      // 弹出层标题
      title: '',

      FoldersPid: 1,
      ObjectKey: '',
      upload_type1: 1,
      upload_type2: 2,
      isEdit: false,
      isFolder: false,
      // 是否显示弹出层
      open: false,
      downloadTitle: '',
      downloadOpen: false,
      ShowColumn: false,
      // 日期范围
      dateRange: [],
      // 表单参数
      form: {},
      approver_list: [],
      model_labels: [],
      query_lables:[],
      form_lables:[],
      // 查询参数
      queryParams: {
        pageIndex: 1,
        pageSize: 10,
        name: undefined,
        status: undefined,
        path: undefined,
        labels: undefined,
        pid: 1,
      },
      // 表单校验
      rules: {
        name: [
          { required: true, message: '名称不能为空', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '类型不能为空', trigger: 'blur' }
        ],
        sec_path: [
          { required: true, message: '保密地址不能为空', trigger: 'blur' }
        ],
        upload_url: [
          { required: true, message: '上传地址不能为空', trigger: 'blur' }
        ],
        upload_type: [
          { required: true, message: '上传类型不能为空', trigger: 'blur' }
        ],
        approver: [
          { required: true, message: '审批人必选', trigger: 'blur' }
        ],
        purposes: [
          { required: true, message: '下载模型目的不能为空', trigger: 'blur' }
        ],
      },
    }
  },
  watch: {
    'form.sec_path': function (newVal) {
      this.getPathFileName(newVal);
    }
  },
  created() {
    this.getList()
    this.getDicts('approver_list').then((response) => {
      this.approver_list = response.data
    })
    this.getDicts('model_labels').then((response) => {
      this.model_labels = response.data
    })
    this.getBaseFloder()
  },
  props: {
  },
  methods: {
    copyToClipboard(text) {
      const textarea = document.createElement('textarea');
      textarea.value = text;
      document.body.appendChild(textarea);
      textarea.select();
      document.execCommand('copy');
      document.body.removeChild(textarea);
      this.$message({
        type: 'success',
        message: '路径已复制到剪贴板!'
      });
    },
    handleArchiveChange(row) {
      const text = row.archive === true ? '归档' : '解冻';
      this.$confirm('确认要"' + text + '" ' + row.name + ' 吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        this.loading = true; // 显示加载指示器

        try {
          // 设置超时时间，例如5000毫秒
          const timeoutPromise = new Promise((resolve, reject) => {
            setTimeout(() => {
              reject(new Error('请求超时'));
            }, 15000); // 15秒超时
          });

          // 使用Promise.race来决定最先解决的Promise
          const result = await Promise.race([
            updateModelArchive(row),
            timeoutPromise
          ]);

          // 如果是超时Promise解决，则result将是undefined
          if (result === undefined) {
            throw new Error('请求超时');
          }

          this.msgSuccess(text + '成功');
        } catch (error) {
          // 捕获并处理异常
          this.msgError('操作失败：' + (error.message || '未知错误'));
          row.archive = row.archive === true ? false : true; // 回滚状态
        } finally {
          this.getList();
          this.loading = false; // 隐藏加载指示器
        }
      }).catch(() => {
        // 用户点击取消按钮
        row.archive = row.archive === true ? false : true;
      });
    },
    getLabelsString(labels) {
       return labels.join(';');
    },
    getLabelsArray(labelsString) {
      return labelsString.split(';');
    },
    getPathFileName(url) {
      const decodedURL = decodeURIComponent(url);
      // 拆分URL  
      const urlParts = decodedURL.split("/");
      // 获取文件名  
      let fileName = urlParts[urlParts.length - 1];
      // 拆分文件名和参数  
      const fileNameParts = fileName.split("?");
      // 获取纯净的文件名  
      fileName = fileNameParts[0];
      this.form.name = fileName
    },
    // 下载按钮操作
    handleDowloadModel(row) {
      // this.reset()
      this.form = row
      this.downloadOpen = true
      this.downloadTitle = '下载申请'
    },
    handleModeNameClick(row) {
      if (row.is_folder) {
        this.FoldersPid = row.id
        this.ObjectKey = row.object_key
        this.queryParams.pid = this.FoldersPid
        this.getList()
      } else if (row.status === 0 && !row.is_folder) {
        this.msgError("尚未上传成功")
      } else if (row.status === 4 && !row.is_folder) {
        this.msgError("上传失败:", row.remark)
      }
      else {
        this.handleDowloadModel(row)
      }
    },
    handleLastFolderClick() {
      this.queryParams.pid = undefined
      this.queryParams.id = this.FoldersPid
      getModelList(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        const data = response.data.list
        const total = response.data.count
        this.FoldersPid = total > 0 ? data[0].pid : 1
        this.ObjectKey = total > 0 ? data[0].object_key : ""
        this.queryParams.id = undefined
        this.queryParams.pid = this.FoldersPid
        this.getList()
      })
    },
    downloadCancel() {
      this.downloadOpen = false
      this.loading = false
      this.reset()
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.loading = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        pid: undefined,
        name: undefined,
        sort: 10,
        path: undefined,
        status: 0,
        remark: undefined,
        sec_path: undefined,
        upload_url: undefined,
        upload_type: undefined,
        labels:undefined,
      }
      this.queryParams = {
        pageIndex: 1,
        pageSize: 10,
        name: undefined,
        status: undefined,
        path: undefined,
        labels: undefined,
        pid: 1,
      },
      this.resetForm('dowloadForm')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pid = this.queryParams.name ? undefined : this.FoldersPid;
      if (this.query_lables && this.query_lables.length > 0){
        this.queryParams.labels = this.getLabelsString(this.query_lables);
      }
      this.getList();
    },
    /** 查询模型列表 */
    getList() {
      this.loading = true
      getModelList(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.modelList = response.data.list
        this.total = response.data.count
        this.queryParams.labels = []
        this.loading = false
      }
      )
    },
    getBaseFloder() {
      // this.loading = true
      getBase().then(response => {
        this.ObjectKey = response.data.ObjectKey
        this.pid = response.data.id
        // this.loading = false
      }
      )
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.getBaseFloder()
      this.FoldersPid = 1
      this.dateRange = []
      // this.resetForm('queryForm')
      this.reset()
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAddModel() {
      this.reset()
      this.open = true
      this.isFolder = false
      this.isEdit = false
      this.title = '上传模型'
    },
    handleAddModelFolder() {
      this.reset()
      this.isFolder = true
      this.open = true
      this.isEdit = false
      this.title = '新建文件夹'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getModel(id).then(response => {
        this.form = response.data
        this.form_lables = this.getLabelsArray(response.data.labels)
        this.open = true
        this.isEdit = true
        this.title = '修改'
      })
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.form.pid = this.FoldersPid;
          this.form.labels = this.getLabelsString(this.form_lables);
          const handleResponse = (response) => {
            if (response.code === 200) {
              if (!this.isFolder) {
                return this._addUpload2Ks3(response.data).then(() => {
                  this.open = false;
                  this.getList();
                  this.msgSuccess(response.msg)
                });
              } else {
                this.open = false;
                this.getList();
                this.msgSuccess(response.msg)
              }
            } else {
              this.msgError(response.msg);
            }
          };

          if (this.form.id !== undefined) {
            updateModel(this.form).then((response) => {
              if (response.code === 200) {
                this.open = false;
                this.getList();
                this.msgSuccess(response.msg)
            } else {
              this.msgError(response.msg);
            }
            });
          } else {
            this.form.is_folder = this.isFolder;
            const item = this.nameExists(this.form.name);

            if (item.exists && item.isFolder && this.isFolder) {
              this.$message({
                message: '同名文件夹已存在',
                type: 'warning'
              });
            } else if (item.exists && !item.isFolder && !this.isFolder) {
              this.$confirm(`同名模型已存在"${this.form.name}"是否覆盖?`, '警告', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }).then(() => {
                addModel(this.form).then(handleResponse).catch(error => {
                  this.msgError(error.message);
                });
              }).catch(() => {
                // 处理取消按钮的情况
              });
            } else {
              addModel(this.form).then(handleResponse).catch(error => {
                this.msgError(error.message);
              });
            }
          }
        }
      });
    },
    nameExists(targetName) {
      const item = this.modelList.find(item => item.name === targetName);
      return {
        exists: !!item,
        isFolder: item ? item.is_folder : false
      };
    },
    // 走保密环境上传（利用已有的定时任务轮询 上传状态）
    _addUpload2Ks3(resp_data) {
      // 使用解构赋值提取需要的数据
      const { object_key, id } = resp_data;

      // 获取最后一个斜杠的索引
      const lastIndex = object_key.lastIndexOf('/');

      // 提取指定存储路径
      const specify_storage = object_key.substring(0, lastIndex);

      // 确定 application 的值
      const application = this.form.upload_type === this.upload_type1 ? 4 : 1;

      // 构建数据对象
      const data = {
        purposes: "上传模型:" + this.form.name,
        path: this.form.upload_type === this.upload_type1 ? object_key : this.form.sec_path,
        url: this.form.upload_url,
        alg_model_id: id,
        remark: this.form.remark,
        application: application,
        approver: undefined,
        is_encrypt: 0,
        default_storage: 1,
        specify_storage: specify_storage,
        object_key: object_key,
      };

      // 调用 addUpload2Ks3 并返回其 Promise
      return addUpload2Ks3(data).then((response) => {
        return response;
      });
    },
    downloadSubmitForm: function () {
      this.$refs['downlod_form'].validate(valid => {
        if (valid) {
          const data = {
            purposes: this.form.purposes,
            alg_model_id: this.form.id,
            application: 3,
            approver: this.form.approver,
            is_encrypt: 0,
            status: 0,
            default_storage: 1,
            specify_storage: this.ObjectKey,
            object_key: this.form.object_key,
          }
          addUpload2Ks3(data).then(response => {
            if (response.code === 200) {
              this.msgSuccess(response.msg)
              this.downloadOpen = false
            } else {
              this.msgError(response.msg)
            }
          })
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const Ids = (row.id && [row.id]) || this.ids
      this.$confirm('是否确认删除模型编号为"' + Ids + '"的数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function () {
        return delModel({ 'ids': Ids })
      }).then((response) => {
        if (response.code === 200) {
          this.msgSuccess(response.msg)
          this.open = false
          this.getList()
        } else {
          this.msgError(response.msg)
        }
      }).catch(function () { })
    },
    humanReadableSize(size) {
      const KB = 1024;
      const MB = KB * 1024;
      const GB = MB * 1024;

      if (size >= GB) {
        return (size / GB).toFixed(2) + ' GB';
      } else if (size >= MB) {
        return (size / MB).toFixed(2) + ' MB';
      } else if (size >= KB) {
        return (size / KB).toFixed(2) + ' KB';
      } else {
        return size + ' bytes';
      }
    },
  },

}
</script>
<style>
.button-container {
  margin-left: 68px;
  /* 调整左边距 */
}

.button-container button {
  margin-right: 5px;
  /* 调整按钮之间的间距 */
}

.el-icon-folder {
  color: blue;
}

.el-icon-document {
  color: blue;
}
</style>
