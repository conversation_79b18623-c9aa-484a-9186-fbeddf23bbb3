<template>
  <el-collapse v-model="activeNames" style="width: 90%; min-width: 600px; margin: 20px 40px">
    <el-collapse-item title="临时组内已校验样张" name="1">
      <el-link v-loading="loadingUrl" type="primary" :href="archiveUrl" target="_blank">{{ archiveUrl }}</el-link>
    </el-collapse-item>
    <el-collapse-item title="临时组所有校验结果" name="2">
      <el-row v-loading="loadingMsg" :gutter="20">
        <el-col :span="2">结果: </el-col>
        <el-col :span="6">{{ resultMsg }}</el-col>
      </el-row>
      <el-table
        v-loading="loadingTable"
        :data="tableData"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="requestId" label="请求id" width="180" />
        <el-table-column prop="status" label="状态" width="60">
          <template scope="scope">
            <span v-if="scope.row.status===1" style="color: green">成功</span>
            <span v-else-if="scope.row.status===2" style="color: orange">校验失败</span>
            <span v-else-if="scope.row.status===3" style="color: orange">参数错误</span>
            <span v-else-if="scope.row.status===4" style="color: orange">下载失败</span>
            <span v-else style="color: red">内部错误</span>
          </template>
        </el-table-column>
        <el-table-column prop="baselineVer" label="基线版本" width="100" />
        <el-table-column prop="sourceUrl" label="源文件地址" min-width="360" />
        <el-table-column prop="resultUrl" label="结果地址" min-width="360" />
        <el-table-column prop="msg" label="详情" min-width="560" />
      </el-table>
    </el-collapse-item>
    <el-collapse-item title="将临时组提升为基线" name="3">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-input v-model="input" placeholder="请为基线版本命名" size="small" clearable />
        </el-col>
        <el-col :span="3">
          <el-button type="primary" size="mini" @click="handleTemp2Test()">提升</el-button>
        </el-col>
      </el-row>
    </el-collapse-item>
  </el-collapse>
</template>

<script>
import { testsetUrlMaker } from '@/api/autotest/common'

export default {
  name: 'TempsetDetail',
  data() {
    return {
      activeNames: ['1', '2', '3'],
      archiveUrl: 'http://test',
      tableData: [],
      input: '',
      resultMsg: '',
      loadingUrl: true,
      loadingTable: true,
      loadingMsg: true
    }
  },
  mounted() {
    this.getArchiveUrl()
    this.getTableData()
  },
  methods: {
    // async getArchiveUrl() {
    //   const response = await this.$autotest_http.autotest_get(this.$autotest_api.archiveApi,
    //     { appName: this.$route.params.app, groupId: this.$route.params.group })
    //   if (response.status === 1) {
    //     this.msgSuccess(response.msg)
    //     this.archiveUrl = response.url
    //     this.loadingUrl = false
    //   } else {
    //     this.msgError(response.msg)
    //   }
    // },
    getArchiveUrl() {
      this.archiveUrl = testsetUrlMaker({ set: 'tempset', app: this.$route.params.app, group: this.$route.params.group })
      this.loadingUrl = false
    },
    async getTableData() {
      const response = await this.$autotest_http.autotest_get(this.$autotest_api.resultsApi,
        { appName: this.$route.params.app, groupId: this.$route.params.group })
      if (response.status === 1) {
        // this.msgSuccess(response.msg)
        this.resultMsg = response.msg
        this.tableData = response.results
        this.loadingTable = false
        this.loadingMsg = false
      } else {
        this.msgError(response.msg)
      }
    },
    async temp2Test() {
      const response = await this.$autotest_http.autotest_post(this.$autotest_api.testsetApi,
        { appName: this.$route.params.app, groupId: this.$route.params.group, baselineVer: this.input })
      if (response.status === 1) {
        this.msgSuccess(response.msg)
        this.toTestset()
      } else {
        this.msgError(response.msg)
      }
    },
    toTestset() {
      this.$router.replace({ name: 'TestSet' })
    },
    handleTemp2Test() {
      if (this.input === '') {
        this.$message.warning('基线名称不能为空')
      } else {
        this.temp2Test()
      }
    }
  }
}
</script>

<style scoped>
/deep/ .el-collapse-item__header{
  background-color: #f0f1f5 !important;
}
/deep/ .el-collapse-item__wrap{
  background-color: #f0f1f5 !important;
}
</style>
