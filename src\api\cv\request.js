import axios from 'axios'
import store from '@/store'
import { sleep } from './utils'

// 发起axios请求，设置响应的拦截器
// 若状态码不等于200，则进行一定次数的重试（每次失败次数都会累计）
// 若状态码等于200，则查询{data.val}并执行回调 handleProgress 直至进度等于100
export const axios_wait_doc = axios.create({
  timeout: 10000,
  __queryCount: 0,
  __retryCount: 0,
  query: 2000, // 调用查询接口的最大次数
  retry: 3,
  retryDelay: 2000, // 请求失败时，重试延时
  requestDelay: 2000, // 进度查询，重试延时
  handleProgress: (val) => {}
})

axios_wait_doc.interceptors.request.use(config => {
  if (config.cancelToken === undefined) {
    config.cancelToken = new axios.CancelToken(function executor(cancel) {
      store.commit('request/addHttpRequest', cancel)// 存储 cancle
    })
  }
  return config
})
axios_wait_doc.interceptors.response.use(
  async(response) => {
    const { data: res, config } = response
    if (res.code === 200) {
      config.__retryCount = 0

      // status不等于 未完成 和 排队中
      if (res.data.val === 100 || (res.data.status !== 2 && res.data.status !== 5)) {
        config.__queryCount = 0
        config.handleProgress(100)
        return Promise.resolve(res)
      } else {
        const progress = parseInt(res.data.val)
        config.handleProgress(progress)
        console.log('进度: ', progress, '%')
        config.__queryCount += 1
        if (config.__queryCount >= config.query) {
          return Promise.reject('超过查询次数! ')
        }
        await sleep(config.requestDelay)
        return axios_wait_doc(config)
      }
    } else {
      return Promise.reject(res.msg)
    }
  },
  async(error) => {
    if (error.message === 'interrupt') {
      console.log('文件处理中断')
      return Promise.resolve()
    }

    const config = error.config
    if (config.__retryCount >= config.retry) {
      return Promise.reject('超过重试次数! ' + error)
    }
    config.__retryCount += 1

    console.log('错误重试...')
    await sleep(config.retryDelay)
    return axios_wait_doc(config)
  }
)
