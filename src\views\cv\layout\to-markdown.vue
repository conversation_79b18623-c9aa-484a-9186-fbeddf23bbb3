<template>
  <!-- 创建一个卡片式的容器，使用主题内容区域将背景色统一 -->
  <el-card>
    <!-- 标题插槽：显示使用说明 -->
    <div slot="header">
      <!-- <CVHint>使用说明：选择一个PDF文件/图片/KDC文件，系统会对文件进行处理，处理结果在页面下方展示</CVHint> -->
      <CVHint>使用说明：选择一个PDF文件/图片，系统会对文件进行处理，处理结果在页面下方展示</CVHint>
    </div>

    <!-- 主体内容 -->
    <div class="card-content">
      <div>
        <div class="container">
          <el-row type="flex" justify="center" :gutter="20">
            <el-col :span="5" class="grid-content">
              <div class="params-container" style="width: 100% !important">
                <div class="params">
                  <span style="color:blue"> {{ "版本信息：" }}</span>
                </div><br>
                <div class="params">
                  <span v-show="remark === ''" style="color:darkblue"> {{ "已是最新效果。" }}</span>
                </div>
                <div class="params">
                  <span style="color:darkblue"> {{ remark }}</span>
                </div>
              </div>

              <br><div>
                <div class="params">
                  <span v-show="form.getFileMeta === true && result.classifier !== '' && result.classifier !== undefined" style="color:blue"> {{ "获取的文件信息：" }}</span>
                </div>
                <div>
                  <ul v-show="form.getFileMeta === true && result.classifier !== '' && result.classifier !== undefined" class="list-item">
                    <li style="color:darkblue"> {{ "分类器：" }}</li>
                    <p style="color:black"> {{ result.classifier }}</p>
                    <li style="color:darkblue"> {{ "关键词：" }}</li>
                    <p v-if="result.keyWords > 0" style="color:black"> {{ result.keyWords }}</p>
                    <p v-else style="color:black"> {{ "未提取到关键词。" }}</p>
                    <li style="color:darkblue"> {{ "总结：" }}</li>
                    <p style="color:black"> {{ result.summary }}</p>
                  </ul>
                </div><br>
              </div>
            </el-col>

            <el-col :span="8" class="grid-content">
              <span style="color:blue"> {{ "参数设置：" }}</span>
              <div class="upload-container">
                <div class="params-container" style="width: 100% !important" />

                <div class="params-container">
                  <div class="params">
                    <span class="param-label">版本 :</span>
                    <el-select
                      v-model="startPage.version"
                      class="param-input"
                      placeholder="版本"
                      size="medium"
                      @change="selectVersion"
                    >
                      <el-option
                        v-for="dict in versions"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                        @click.native="handleSelect(dict)"
                      />
                    </el-select>
                  </div>
                  <div class="params">
                    <span class="param-label">输入文件类型 :</span>
                    <el-select
                      v-model="startPage.inputtype"
                      class="param-input"
                      placeholder="输入文件类型"

                      size="medium"
                    >
                      <el-option
                        v-for="dict in inputtypeOptions"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </div>
                </div>

                <div class="params-container" style="width: 100% !important">
                  <br><div class="params">
                    <el-button
                      type="warning"
                      size="mini"
                      :disabled="startPage.inputtype=='kdc'"
                      @click="handleSetting"
                    >高级设置</el-button>
                    <!-- choiceInputOptions -->
                    <el-tooltip content="切换输入方式" placement="right-end">
                      <el-select v-model.number="choiceInput" placeholder="默认上传文件">
                        <el-option
                          v-for="dict in dynamicChoiceInputOptions"
                          :key="dict.value"
                          :label="dict.label"
                          :value="dict.value"
                        />
                      </el-select>
                    </el-tooltip>
                  </div>
                </div>

                <hr>
                <div v-if="choiceInput=='inputUrl' || choiceInput=='inputTxt'">
                  <div v-if="choiceInput=='inputUrl'">
                    <br><span style="color:blue"> {{ "输入PDF/ Image/ KDC链接：" }}</span><br>
                    <div class="params">
                      <span
                        class="param-label"
                        style="width: 25% !important"
                      >url of pdf/image/kdc :</span>
                      <el-input
                        v-model="fileUrl"
                        class="param-input"
                        style="width: 75% !important"
                        type="text"
                      /><br>
                    </div>
                  </div>
                  <!-- <div v-else>
                    <br><span style="color:blue"> {{ "输入KDC文件内容(json)：" }}</span><br>
                    <div class="params">
                      <span
                        class="param-label"
                        style="width: 25% !important"
                      >kdc :</span>
                      <el-input
                        v-model="kdcTxt"
                        class="param-input"
                        style="width: 75% !important"
                        type="textarea"
                        :rows="4"
                      /><br>
                    </div>
                  </div> -->
                  <br><el-button
                    v-if="loading"
                    type="primary"
                    size="mini"
                    disabled
                  >加载中...</el-button>
                  <el-button
                    v-else
                    type="primary"
                    size="mini"
                    @click="KdcTxtAndKdcUrl"
                  >开始转换</el-button>
                </div>

                <!-- 转换或点击上传前高级设置对话框 -->
                <el-dialog :title="title" :visible.sync="open" width="650px">
                  <el-form ref="form" :model="form" label-width="150px" inline-message>
                    <!-- pdf2kdc -->
                    <el-row>
                      <el-col :span="12">
                        <el-tooltip content="需要得到的元素" placement="left-end">
                          <template slot="default">
                            <el-form-item label="include_elements:">
                              <el-select v-model="form.include_elements" placeholder="请选择" multiple @change="$forceUpdate()">
                                <el-option
                                  v-for="dict in includeElementsOptions"
                                  :key="dict.value"
                                  :label="dict.label"
                                  :value="dict.value"
                                />
                              </el-select>
                            </el-form-item>
                          </template>
                        </el-tooltip>
                      </el-col>
                      <el-col :span="12">
                        <el-tooltip content="控制大纲是否对标题进行分类" placement="right-end">
                          <template slot="default">
                            <el-form-item label="isTocCls:">
                              <el-select v-model="form.isTocCls" placeholder="请选择" @change="$forceUpdate()">
                                <el-option
                                  v-for="dict in isTocClsaOptions"
                                  :key="dict.value"
                                  :label="dict.label"
                                  :value="dict.value"
                                />
                              </el-select>
                            </el-form-item>
                          </template>
                        </el-tooltip>
                      </el-col>
                    </el-row>
                    <el-row>
                      <el-col :span="12">
                        <el-tooltip content="是否开启公式" placement="left-end">
                          <template slot="default">
                            <el-form-item label="get_formula:">
                              <el-select v-model="form.get_formula" placeholder="请选择" @change="$forceUpdate()">
                                <el-option
                                  v-for="dict in isFormulaOptions"
                                  :key="dict.value"
                                  :label="dict.label"
                                  :value="Number(dict.value)"
                                />
                              </el-select>
                            </el-form-item>
                          </template>
                        </el-tooltip>
                      </el-col>
                      <el-col :span="12">
                        <el-tooltip content="表格是否html格式" placement="right-end">
                          <template slot="default">
                            <el-form-item label="isMdTableHtml:">
                              <el-select v-model="form.isMdTableHtml" placeholder="请选择" @change="$forceUpdate()">
                                <el-option
                                  v-for="dict in isTableHtmlOptions"
                                  :key="dict.value"
                                  :label="dict.label"
                                  :value="dict.value"
                                />
                              </el-select>
                            </el-form-item>
                          </template>
                        </el-tooltip>
                      </el-col>
                      <!-- <el-col :span="12">
                        <el-tooltip content="是否开启extKdc" placement="right-end">
                          <template slot="default">
                            <el-form-item label="extKdc:">
                              <el-select v-model="form.extKdc" placeholder="请选择" @change="$forceUpdate()">
                                <el-option
                                  v-for="dict in isTableHtmlOptions"
                                  :key="dict.value"
                                  :label="dict.label"
                                  :value="dict.value"
                                />
                              </el-select>
                            </el-form-item>
                          </template>
                        </el-tooltip>
                      </el-col> -->
                    </el-row>

                  </el-form>
                  <div slot="footer" class="dialog-footer">
                    <el-button class="reset-button" type="warning" @click="reset">重 置</el-button>
                    <div class="right-buttons">
                      <el-button class="confirm-button" type="primary" @click="submitForm">确 定</el-button>
                      <el-button class="cancel-button" @click="cancel">取 消</el-button>
                    </div>
                  </div>
                </el-dialog>

                <div v-if="choiceInput=='uploadFile'">
                  <!-- <br><span style="color:blue"> {{ "上传PDF/ Image/ KDC文件：" }}</span> -->
                  <br><span style="color:blue"> {{ "上传PDF文件/ Image：" }}</span>
                  <el-upload
                    v-loading="loading"
                    element-loading-text="文件处理中"
                    class="uploader"
                    list-type="text"
                    accept=".pdf,.json,.png,.jpeg,.jpg"
                    :multiple="false"
                    :show-file-list="false"
                    :file-list="fileList"
                    :http-request="pdf2wordUpload"
                    :on-success="onSuccess"
                    action="placeholder"
                    drag
                  >
                    <i class="el-icon-upload" />
                    <div class="el-upload__text">
                      将文件拖到此处，或
                      <el-button type="text">点击上传</el-button>
                    </div>
                  </el-upload>

                  <el-progress
                    v-show="percentage !== 0 && percentage !== 100"
                    :text-inside="true"
                    :stroke-width="14"
                    :percentage="percentage"
                  />
                </div>

                <br>
                <div v-show="fileName !== ''"><hr>
                  <div class="params-container">
                    <div><span style="color:darkblue"> {{ '转化成功后的结果在页面下方展示。' }}</span></div><br>
                    <div class="params">
                      <span style="color:blue" class="param-label">{{ '转化结果' }} :</span>
                    </div>
                    <div v-show="fileName !== ''" class="params">
                      <span class="param-label">{{ fileName }} :</span>
                      <span class="param-input">
                        {{ fileSize }}
                      </span>
                    </div>
                    <div v-show="statistic.status !== -1" class="params">
                      <span class="param-label">处理用时 :</span>
                      <span class="param-input">
                        {{ formattedDuration }}
                      </span>
                    </div>
                    <div v-show="statistic.status !== -1" class="params">
                      <span
                        v-show="statistic.status === 1"
                        class="param-label"
                        style="color:blue"
                      >转化成功!</span><br>
                      <span
                        v-show="statistic.status === 0"
                        class="param-label"
                        style="color: #ff0000"
                      >转化失败!</span><br>
                    </div>
                    <div v-show="statistic.status !== -1" class="params">
                      <el-button v-show="showJsonData && startPage.inputtype !== 'kdc'" type="text" size="small" @click="showJson">复制 pdf2kdc 得到的 json数据</el-button><br>
                      <el-button v-show="showJsonData && startPage.inputtype !== 'kdc'" type="success" size="small" @click="dialogVisible = true">显示服务耗时</el-button>
                    </div>
                  </div>
                </div>

              </div>
            </el-col>
            <el-col :span="5" class="grid-content">
              <CVDemo
                :thumbnail="true"
                :loading="loading"
                :data="urlFiles[0]"
                @click="pdf2word('pdf', 0, $event)"
              >PDF文件示例:</CVDemo>
            </el-col>
            <el-col :span="5" class="grid-content">
              <CVDemo
                :thumbnail="true"
                :loading="loading"
                :data="urlFiles[1]"
                @click="pdf2word('image', 1, $event)"
              >图片示例:</CVDemo>
            </el-col>
            <!-- <el-col :span="5" class="grid-content">
              <CVDemo
                :thumbnail="true"
                :loading="loading"
                :data="urlFiles[2]"
                @click="pdf2word('kdc', 2, $event)"
              >KDC文件示例:</CVDemo>
            </el-col> -->
          </el-row>

        </div>
        <div class="show-button">
          <el-button v-show="statistic.status !== -1 && showMarkDown" type="primary" size="small" @click="showMarkdownRender = true">显示markdown预览效果</el-button>
          <el-button v-show="statistic.status !== -1 && showMarkDown" type="warning" size="small" @click="showMarkdownRender = false">显示markdown原始数据</el-button>
          <el-button v-show="statistic.status !== -1 && showMarkDown" type="success" size="small" @click="copyMarkDownData">复制markdown原始数据</el-button>
        </div>
        <div class="result-container">
          <div class="left-column">
            <h3>原文件：</h3>
            <!-- <vue-pdf :src="testPdfUrl" /> -->
            <!-- {{ pdfSourceUrl[0] }} -->
            <div v-show="statistic.status !== -1 && showMarkDown" v-if="startPage.inputtype === 'pdf'" class="image-container">
              <iframe :src="originUrl+'#navpanes=0'" width="100%" height="100%" />
              <!-- <iframe :src="originUrl+'#navpanes=0'" width="100%" height="1000px" style="height:100%;" /> -->
            </div>
            <div v-show="statistic.status !== -1 && showMarkDown" v-if="startPage.inputtype === 'image'" class="image-container">
              <!-- <img :src="originUrl" class="image" :style="{ transform: 'scale(0.5)' }"> -->
              <img :src="originUrl" class="image">
            </div>
            <div v-show="statistic.status !== -1 && showMarkDown" v-if="startPage.inputtype === 'kdc'" class="image-container">
              <json-viewer :value="json_data" theme="dark" :expand-depth="1000" />
            </div>
          </div>
          <div class="right-column">
            <h3>转换为md：</h3>
            <div v-show="statistic.status !== -1 && showMarkDown" class="image-container">
              <!-- <vue-markdown :source="markDownData" /> -->
              <div v-if="showMarkdownRender">
                <!-- <markdown-it-vue :use="plugins" :content="testData" class="md-body" /> -->
                <!-- <markdown-it-vue :options="options" :use="plugins" :content="markDownData" class="md-body" /> -->
                <markdown-it-vue :options="options" :content="markDownData" class="md-body" />
              </div>
              <div v-else>
                <json-viewer :value="markDownData" theme="dark" :expand-depth="1000" />
              </div>
            </div>
          </div>
        </div>
        <el-dialog :visible.sync="dialogVisible">
          <div v-show="(percentage === 0 || percentage === 100) && result.opTimes[0] !== 0">
            <h3>转kdc的服务耗时：</h3>
            <ul>
              <li v-for="(errMsg, index) in result.errMsgs" :key="index">
                {{ errMsg }}
              </li>
            </ul>
            <table
              border="solid"
              cellspacing="0"
              cellpadding="10"
              frame="solid"
              rules="solid"
              style="width: 100%"
            >
              <thead>
                <tr align="center" valign="center" bgcolor="#e5e9f2">
                  <th>序号</th>
                  <th>类型</th>
                  <th>耗时/s</th>
                  <th>序号</th>
                  <th>类型</th>
                  <th>耗时/s</th>
                </tr>
              </thead>
              <tbody v-if="result !== undefined && result.ops !== undefined" align="center" valign="center">
                <tr>
                  <td>1</td>
                  <td>{{ result.ops[0] }}</td>
                  <td>{{ result.opTimes[1] }}</td>
                  <td>2</td>
                  <td>{{ result.ops[1] }}</td>
                  <td>{{ result.opTimes[2] }}</td>
                </tr>
                <tr>
                  <td>3</td>
                  <td>{{ result.ops[2] }}</td>
                  <td>{{ result.opTimes[3] }}</td>
                  <td>4</td>
                  <td>{{ result.ops[3] }}</td>
                  <td>{{ result.opTimes[4] }}</td>
                </tr>
                <tr>
                  <td>5</td>
                  <td>{{ result.ops[4] }}</td>
                  <td>{{ result.opTimes[5] }}</td>
                  <td>6</td>
                  <td>{{ result.ops[5] }}</td>
                  <td>{{ result.opTimes[6] }}</td>
                </tr>
                <tr>
                  <td>7</td>
                  <td>{{ result.ops[6] }}</td>
                  <td>{{ result.opTimes[7] }}</td>
                  <td>8</td>
                  <td>{{ result.ops[7] }}</td>
                  <td>{{ result.opTimes[8] }}</td>
                </tr>
                <tr>
                  <td>9</td>
                  <td>{{ result.ops[8] }}</td>
                  <td>{{ result.opTimes[9] }}</td>
                  <td>10</td>
                  <td>{{ result.ops[9] }}</td>
                  <td>{{ result.opTimes[10] }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </el-dialog>
      </div>
    </div>

    <!-- 底部插槽：未使用，后续若是扩展可以考虑翻页等功能 -->
    <div slot="footer">
      Card Footer
    </div>
  </el-card>
</template>

<script>
import axios from 'axios'
import CVHint from './components/CVHint'
import CVDemo from './components/CVDemo'
// import CVShowJson from './components/CVShowJson'
import CryptoJS from 'crypto-js'
import {
  urlRegex,
  formatfileSize,
  isValidFile,
  convertOpTimes
} from '@/api/cv/utils'
import { axios_wait_doc } from '@/api/cv/request'
import {
  httpRequesSignKs,
  uploadKs3Store,
  pdf2WordCreateUrl,
  kdc2md
} from '@/api/cv/request-ks3'
import { getHeaders } from '@/utils/get-header'
import { sleep } from '@/api/cv/utils'
// import VueMarkdown from 'vue-markdown'
import MarkdownItVue from 'markdown-it-vue'
import 'markdown-it-vue/dist/markdown-it-vue.css'

export default {
  name: 'ToMarkdown',
  // components: { CVHint, CVDemo, VueMarkdown, MarkdownItVue },
  components: { CVHint, CVDemo, MarkdownItVue },
  props: {
    maxFileNum: {
      validator(value) {
        if (value <= 0) {
          return 1
        }
        return value
      },
      default: 1
    },
    fileType: {
      type: Array,
      default: function() {
        return ['.pdf', '.json', '.png', '.jpg', '.jpeg']
      }
    }
  },
  data() {
    return {
      // 打开服务耗时弹窗
      dialogVisible: false,
      // 切换显示markdown预览效果
      showMarkdownRender: true,
      // 输入方式字典
      // choiceInputOptions: [
      //   { label: '上传文件(默认)', value: 'uploadFile' },
      //   { label: '输入链接', value: 'inputUrl' },
      //   { label: '输入KDC内容', value: 'inputTxt' }
      // ],
      choiceInput: 'uploadFile',
      // 弹出层标题（用户导入）
      title: '',
      // 是否显示弹出层（用户导入）
      open: false,
      // pdf2kdc转化后需要得到的元素
      includeElementsOptions: [
        { label: 'textbox', value: 'textbox' },
        { label: 'para', value: 'para' },
        { label: 'table', value: 'table' },
        { label: 'component', value: 'component' },
        { label: 'all', value: 'all' }
      ],
      // 表格是否html格式
      isTableHtmlOptions: [
        { label: '是', value: true },
        { label: '否', value: false }
      ],
      // 是否开启公式
      isFormulaOptions: [
        { label: '是', value: '1' },
        { label: '否', value: '0' }
      ],
      // 是否对标题进行分类
      isTocClsaOptions: [
        { label: '是', value: true },
        { label: '否', value: false }
      ],
      // 用户输入文件类型
      inputtypeOptions: [
        { label: 'PDF文件', value: 'pdf' },
        { label: '图片', value: 'image' }
        // { label: 'KDC文件', value: 'kdc' }
      ],
      // 表单参数
      form: {
        include_elements: ['all'],
        get_formula: 0,
        isMdTableHtml: false,
        isTocCls: true
      },
      // 参数
      include_elements: ['all'],
      get_formula: 0,
      isMdTableHtml: false,
      isTocCls: true,
      loading: false,
      url: {
        value: '//pcv-test.wps.cn',
        default: '//pcv-test.wps.cn'
      },
      fileUrl: '',
      kdcTxt: '',
      remark: '',
      startPage: {
        inputtype: 'pdf',
        version: ''
      },
      uri: '/create',
      fileObj: {
        name: '',
        size: 0
      },
      fileList: [],
      versions: [],
      result: {
        wordUrl: '',
        duration: 0,
        pdfUrl: '',
        wordUrlKs: '',
        opTimes: Array(11).fill(0), // 时间戳数组
        ops: Array(11).fill(0), // opTimes对应的类型
        errMsgs: [],
        classifier: '',
        keyWords: [],
        summary: ''
      },
      // json相关
      showJsonData: false,
      jsonData: {},
      // kdc相关
      showMarkDown: false,
      // markDownData: [],
      markDownData: '',
      options: {
        markdownIt: {
          linkify: true,
          html: true
        },
        katex: {
          throwOnError: false,
          errorColor: '#cc0000'
        }
      },
      // plugins: [MarkdownItLatex],
      statistic: {
        status: -1,
        duration: 0
      },
      pdfSourceUrl: [],
      currentPage: 0,
      pageCount: 0,
      originUrl: '',
      json_data: undefined,
      pageNum: 0,
      scale: 1.0,
      percentage: 0,
      // kdc返回的json数据
      kdcJsonData: {},
      theme: 'dark',
      urlFiles: [
        [
          {
            url: 'http://zhai-datas.ks3-cn-beijing.ksyun.com/layout/people/luhaoxian/standard/normaldoc.pdf',
            name: '常规公文.pdf',
            size: '384683'
          },
          {
            url: 'http://zhai-datas.ks3-cn-beijing.ksyuncs.com/layout/people/wangluoyan/demo_sample/markdown/cleandoc.pdf',
            name: '干净论文.pdf',
            size: '6735'
          },
          {
            url: 'http://zhai-datas.ks3-cn-beijing.ksyuncs.com/layout/people/wangluoyan/demo_sample/markdown/md_data%20%281%29.pdf',
            name: '红头文件.pdf'
          },
          {
            url: 'http://zhai-datas.ks3-cn-beijing.ksyuncs.com/layout/people/wangluoyan/demo_sample/markdown/md_data%20%282%29.pdf',
            name: '通知.pdf'
          },
          {
            url: 'http://zhai-datas.ks3-cn-beijing.ksyun.com/layout/people/luhaoxian/standard/complex/2.pdf',
            name: '论文目录.pdf',
            size: '76539'
          },
          {
            url: 'http://zhai-datas.ks3-cn-beijing.ksyuncs.com/layout/people/wangluoyan/demo_sample/markdown/md_data%20%284%29.pdf',
            name: '论文内容.pdf'
          },
          {
            url: 'http://zhai-datas.ks3-cn-beijing.ksyuncs.com/layout/people/wangluoyan/demo_sample/markdown/md_data%20%285%29.pdf',
            name: '试卷.pdf'
          },
          {
            url: 'http://zhai-datas.ks3-cn-beijing.ksyun.com/layout/people/luhaoxian/standard/normaltable.pdf',
            name: '常规表格.pdf',
            size: '96925'
          },
          {
            url: 'http://zhai-datas.ks3-cn-beijing.ksyuncs.com/layout/people/wangluoyan/demo_sample/markdown/md_data%20%286%29.pdf',
            name: '表格.pdf'
          },
          {
            url: 'http://zhai-datas.ks3-cn-beijing.ksyuncs.com/layout/people/wangluoyan/demo_sample/markdown/md_data%20%287%29.pdf',
            name: '复杂表格.pdf'
          },
          {
            url: 'http://zhai-datas.ks3-cn-beijing.ksyuncs.com/layout/people/wangluoyan/demo_sample/markdown/md_data%20%283%29.pdf',
            name: '混合件.pdf'
          }
        ],
        [
          {
            url: 'http://zhai-datas.ks3-cn-beijing.ksyuncs.com/layout/people/wangluoyan/demo_sample/markdown/image01.png',
            name: '示例一'
          }
        ],
        [
          {
            url: 'http://zhai-datas.ks3-cn-beijing.ksyuncs.com/layout/people/wangluoyan/demo_sample/markdown/kdc01.json',
            name: '示例一'
          }
        ]
      ]
    }
  },
  computed: {
    dynamicChoiceInputOptions() {
      const options = [
        { label: '上传文件(默认)', value: 'uploadFile' },
        { label: '输入链接', value: 'inputUrl' }
      ]
      if (this.startPage.inputtype === 'kdc') {
        options.push({ label: '输入KDC内容', value: 'inputTxt' })
      }
      return options
    },
    formattedDuration() {
      return this.statistic.duration.toFixed(3) + 's'
    },
    fileName() {
      const { name } = this.fileObj
      return name.trim()
    },
    fileSize() {
      const { size } = this.fileObj
      return formatfileSize(size)
    }
  },
  // 通过watch监听输入值的变化，并将maxConNumPage/pdfDebugFlags由 string类型转为 int类型
  watch: {
    'form.maxConNumPage'(val) {
      this.maxConNumPageInt = parseInt(val)
    },
    'form.pdfDebugFlags'(val) {
      this.pdfDebugFlagsInt = parseInt(val)
    },
    'startPage.inputtype': function(newValue, oldValue) {
      if (newValue !== 'kdc') {
        this.choiceInput = 'uploadFile' // 将选择输入方式重置为默认值
      }
    }
  },
  created() {
    // this.getDicts('pdf_export_type').then(response => {
    //   this.exporttypeOptions = response.data
    // })
    this.getDicts('pdf_convert_version').then(response => {
      this.versions = response.data
      if (this.versions.length > 0) {
        this.url.value = this.versions[2].value
        this.startPage.version = this.versions[2].label
        this.remark = this.versions[2].remark
      }
    })
  },
  methods: {
    /** 提交按钮 */
    submitForm: function() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.include_elements = this.form.include_elements
          this.get_formula = this.form.get_formula
          this.isMdTableHtml = this.form.isMdTableHtml
          this.isTocCls = this.form.isTocCls
          this.open = false
        }
      })
    },
    // 取消按钮
    cancel() {
      this.form.include_elements = this.include_elements
      this.form.get_formula = this.get_formula
      this.form.isMdTableHtml = this.isMdTableHtml
      this.form.isTocCls = this.isTocCls
      this.open = false
      // this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        include_elements: ['all'],
        get_formula: 0,
        isMdTableHtml: false,
        isTocCls: true
      }
      this.resetForm('form')
    },
    /** 高级设置按钮操作 */
    handleSetting() {
      // this.reset()

      this.open = true
      this.title = '高级设置'
      this.form.password = ''
    },
    progress(percentage) {
      this.percentage = percentage
    },
    handleSelect(item) {
      this.remark = item.remark
    },
    selectVersion(value) {
      this.url.value = value
    },
    validateUploadPDFUrl(fileurl) {
      if (fileurl.trim() === '') {
        return false
      }
      if (!urlRegex.test(fileurl)) {
        return false
      }
      return true
    },
    // 获取url的后缀名
    getFileExtension(url) {
      const regex = /[^.]+$/
      const extension = url.match(regex)[0]
      return extension
    },
    /** 转换按钮操作 */
    async KdcTxtAndKdcUrl() {
      if (this.choiceInput === 'inputUrl') {
        this.inputUrl()
      } else {
        // 输入txt的情况只有kdc
        try {
          const kdc_data = JSON.parse(this.kdcTxt)
          this.json_data = {
            'kdc': kdc_data
          }
          // console.log('json data:', this.json_data)
          await this.kdc2Markdown(this.json_data)
          const s_time = new Date().getTime()
          // 结束加载
          this.loading = false
          const e_time = new Date().getTime()
          this.statistic = {
            status: 1,
            duration: this.result.duration || (e_time - s_time) / 1000
          }
          return Promise.resolve()
        } catch (error) {
          this.message('error', '输入的kdc不符合要求：', error)
        }
      }
    },
    // 输入url的情况
    async inputUrl() {
      if (!this.validateUploadPDFUrl(this.fileUrl)) {
        this.message('error', '请输入正确文件地址! ', '')
        this.originUrl = ''
        return false
      }
      const ext = this.getFileExtension(this.fileUrl)
      const isValid = this.validFileType('.' + ext)
      if (!isValid) {
        // 结束加载
        this.originUrl = ''
        this.message('error', '参数设置的‘输入文件类型’没有与输入的链接相匹配！', '')
        return
      }

      this.loading = true

      if (this.fileUrl.lastIndexOf('ksyuncs.com') > 0 || this.fileUrl.lastIndexOf('ksyun.com') > 0) {
        if (this.startPage.inputtype === 'kdc') {
          // 跨域，目前只支持特定链接
          var url = this.fileUrl.replace(/^http:\/\/zhai-datas\.ks3-cn-beijing(-internal)?\.ksyuncs\.com/, '/kso-datas')
          url = url.replace(/^http:\/\/zhai-datas\.ks3-cn-beijing(-internal)?\.ksyun\.com/, '/kso-datas')
          // console.log('url:', url)
          axios.get(url)
            .then(async response => {
              this.json_data = {
                'kdc': response.data
              }
              // console.log('json data:', this.json_data)
              await this.kdc2Markdown(this.json_data)
            })
            .catch(error => {
              this.message('error', 'Error kdc file: ', error)
            })
        } else {
          this.originUrl = this.fileUrl.replace(/http:\/\//g, 'https://')
          await this.kdcCase(undefined, this.fileUrl)
        }
      } else {
        this.message('warn', '目前只支持特定链接，暂不支持该链接。', '')
      }
      const s_time = new Date().getTime()

      // 结束加载
      this.loading = false
      const e_time = new Date().getTime()
      this.statistic = {
        status: 1,
        duration: this.result.duration || (e_time - s_time) / 1000
      }
      return Promise.resolve()
    },
    handleOpTimes(times, names) {
      this.result.ops = names
      // 1.时间戳转换
      const opTimes = this.result.opTimes
      convertOpTimes(opTimes, times)
    },
    fileChange(file, fileList) {
      this.fileList = fileList.slice(-this.maxFileNum)
    },
    onSuccess(response, file, fileList) {
      // hook
      this.fileChange(file, fileList)
    },
    resetResult() {
      this.percentage = 0
      this.result = {
        wordUrl: '',
        duration: 0,
        pdfUrl: '',
        wordUrlKs: '',
        opTimes: Array(11).fill(0), // 时间戳数组
        errMsgs: [],
        inputtype: 'pdf',
        classifier: '',
        keyWords: [],
        summary: ''
      }
      this.originUrl = ''
      this.json_data = undefined
      this.showMarkdownRender = true
      this.kdcTxt = ''
      this.statistic = {
        status: -1,
        duration: 0
      }
    },
    beforeUpload(file) {
      // 清理旧结果
      this.fileObj = { name: '', size: 0 }
      this.resetResult()

      // 文件类型判断
      const { name } = file
      var ext = '.' + name.trim().split('.').pop()
      ext = ext.toLowerCase()
      const isValid = this.validFileType(ext)
      if (isValid) {
        this.fileObj = file
        return true
      } else {
        this.message('error', '参数设置的‘输入文件类型’没有与上传的文件类型相匹配！', '')
      }
    },
    validFileType(ext) {
      var isValid
      if (!isValidFile(ext, this.fileType)) {
        // this.message('error', '请选择PDF文件/ 图片/ KDC文件! ', '')
        this.message('error', '请选择PDF文件/ 图片! ', '')
        return Promise.reject()
      } else {
        switch (ext) {
          case '.pdf':
            isValid = this.startPage.inputtype === 'pdf'
            break
          case '.json':
            isValid = this.startPage.inputtype === 'kdc'
            break
          case '.png':
          case '.jpg':
          case '.jpeg':
            isValid = this.startPage.inputtype === 'image'
            break
        }
        console.log('isValid:', isValid)
        return isValid
      }
    },
    message(type, msg, dataMsg) {
      this.$message({
        showClose: true,
        message: msg + dataMsg,
        center: true,
        duration: 4000,
        type: type
      })
    },
    async kdcCase(file, imageurl) {
      // pdf2md只支持图片或pdf文件输入
      const res_create_url_data = await pdf2WordCreateUrl(
        this.url.value,
        imageurl,
        undefined,
        undefined,
        'pdf2md',
        this.uri,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        this.include_elements,
        this.get_formula,
        this.isMdTableHtml,
        this.isTocCls,
        this.startPage.inputtype === 'image' ? 'false' : undefined
      )

      if (res_create_url_data.status === 200) {
        // 等待1s
        await sleep(1000)
        const docID = res_create_url_data.data.data['docID']
        await this.waitDocStatus(docID).catch((error) => {
          console.log(error)
        })
      }
    },
    async kdcCaseOld(file, imageurl) {
      // 判断是否KDC文件
      if (this.startPage.inputtype === 'kdc') {
        console.log('imageurl:', imageurl)
        // 获取文件内容
        // 下述获取文件内容代码并不依赖于上传kdc文件，但为了方便展示及定位错误所以先获取url
        const reader = new FileReader()
        reader.onload = async(e) => {
          try {
            this.json_data = {
              'kdc': JSON.parse(e.target.result)
            }
            // console.log('json data:', this.json_data)
            await this.kdc2Markdown(this.json_data)
          } catch (error) {
            this.message('error', 'Error parsing JSON file: ', error)
          }
        }
        reader.readAsText(file)
      } else {
        // PDF和图片需要先走pdf_export接口转为kdc
        const res_create_url_data = await pdf2WordCreateUrl(
          this.url.value,
          imageurl,
          undefined,
          undefined,
          'pdf_export',
          this.uri,
          undefined,
          undefined,
          undefined,
          undefined,
          undefined,
          undefined,
          undefined,
          undefined,
          this.include_elements,
          this.get_formula,
          this.extKdc,
          this.startPage.inputtype === 'image' ? 'false' : undefined
        )

        if (res_create_url_data.status === 200) {
          // 等待1s
          await sleep(1000)
          const docID = res_create_url_data.data.data['docID']
          await this.waitDocStatus(docID).catch((error) => {
            console.log(error)
          })
        }
      }
    },
    // 示例 和 复杂示例调用
    async pdf2word(file_type, arrayid, id) {
      // console.log('array', arrayid, ' index', id)
      // 开始加载
      this.loading = true

      const file = this.urlFiles[parseInt(arrayid)][parseInt(id)]
      this.fileObj = { name: file.name, size: file.size }
      this.resetResult()
      // console.log('[pdf2word] file.url:', file.url)
      //   this.pdfSourceUrl.push(file.url)
      // 赋值文件类型
      this.startPage.inputtype = file_type
      this.originUrl = file.url.replace(/http:\/\//g, 'https://')
      if (this.startPage.inputtype === 'kdc') {
        const url = file.url.replace(/^http:\/\/zhai-datas\.ks3-cn-beijing(-internal)?\.ksyuncs\.com/, '/kso-datas')
        // console.log('url:', url)
        axios.get(url)
          .then(async response => {
            this.json_data = {
              'kdc': response.data
            }
            // console.log('json data:', this.json_data)
            await this.kdc2Markdown(this.json_data)
          })
          .catch(error => {
            this.message('error', 'Error kdc file: ', error)
          })
      } else {
        await this.kdcCase(this.fileObj, file.url)
      }
      const s_time = new Date().getTime()

      // 结束加载
      this.loading = false
      const e_time = new Date().getTime()
      this.statistic = {
        status: 1,
        duration: this.result.duration || (e_time - s_time) / 1000
      }
      return Promise.resolve()
    },
    pdf2wordCreate(url) {
      const form = new FormData()
      const urls = []
      urls.push(url)
      // console.log(urls)
      form.append('PageNumBegin', this.startPage.value)
      form.append('PageNumEnd', this.endPage.value)
      form.append('imgUrls', urls)
      const obj = {
        PageNumBegin: this.startPage.value,
        PageNumEnd: this.endPage.value,
        justConvertToImg: false,
        url: url
      }
      const json = JSON.stringify(obj)
      // console.log(json)
      const axios_config_get_docID = {
        method: 'post',
        headers: { 'Cache-Control': 'no-cache', ...getHeaders() },
        url: this.url.value + '/layout/scheduler/pdf2docx',
        data: json,
        responseType: 'json',
        processData: false, // 必须
        contentType: false
      }
      axios(axios_config_get_docID).then((res) => {
        // console.log(res)
        const docID = res.data.data.docID
        axios_wait_doc({
          method: 'get',
          headers: { 'Cache-Control': 'no-cache', ...getHeaders() },
          url:
            this.url.value +
            '/layout/scheduler/pdf_export/query?docID=' +
            docID,
          handleProgress: this.progress
        }).then((res) => {
          // console.log(res)
          if (res === undefined) {
            console.log('undefined res')
            return Promise.resolve(-1)
          }
          // 处理完成
          // console.log('文件处理完成: ' + JSON.stringify(res))

          const { data } = res
          if (this.startPage.exporttype === 'pdf2pptx' || this.startPage.exporttype === 'pdf2xlsx') {
            this.result.exporttype = require('../../../assets/cv/' + this.startPage.exporttype + '.png')
          } else {
            this.result.exporttype = require('../../../assets/cv/word.png')
          }
          this.result.wordUrl =
            data.wordUrl !== undefined ? data.wordUrl.replace('http:', '') : ''
          this.result.duration = data.duration // 处理用时
          this.result.pdfUrl = data.pdfUrl !== undefined ? data.pdfUrl : ''
          this.result.wordUrlKs =
            data.wordUrlKs !== undefined
              ? data.wordUrlKs.replace('http:', '')
              : ''
          this.handleOpTimes(data.opTimes, data.ops)

          if (data.errMsgs !== undefined && data.errMsgs.length > 0) {
            this.result.errMsgs = data.errMsgs
            return Promise.reject('详情查看错误信息')
          } else {
            return Promise.resolve(1)
          }
        })
      })
    },
    async waitDocStatus(docID) {
      // const docID = res_create_url_data.data.data['docID']

      await axios_wait_doc({
        method: 'get',
        headers: { 'Cache-Control': 'no-cache', ...getHeaders() },
        url:
          this.url.value + '/layout/scheduler/pdf2md/query?docID=' + docID + '&getParams=1',
        handleProgress: this.progress
      }).then(async(res) => {
        if (res === undefined) {
          console.log('undefined res')
          return Promise.resolve(-1)
        }
        if (res.data.params === undefined || res.data.params.outline_extract_param === undefined || res.data.params.outline_extract_param.json_url === undefined) {
          this.message('warn', '获取入参失败', 'res.data.params=undefined')
        } else {
          // console.log('res.data.params.outline_extract_param.json_url:', res.data.params.outline_extract_param.json_url)
          const json_data = res.data.params.outline_extract_param.json_url
          this.jsonData = json_data.replace(/"/g, '')
          console.log('this.jsonData:', this.jsonData)
          this.showJsonData = true
        }

        // console.log('res.data.officeUrlKs:', res.data.officeUrlKs)
        // 获取md 结果链接中的内容
        try {
          // const json_url = res.data.json_url.replace(/http:\/\/zhai-datas.ks3-cn-beijing-internal\.ksyun\.com/g, 'http://zhai-datas.ks3-cn-beijing.ksyun.com')
          var result_url = res.data.officeUrlKs.replace(/^http:\/\/zhai-datas\.ks3-cn-beijing(-internal)?\.ksyuncs\.com/, '/kso-datas')
          result_url = result_url.replace(/^http:\/\/zhai-datas\.ks3-cn-beijing(-internal)?\.ksyun\.com/, '/kso-datas')
          // console.log('result_url:', result_url)
          const resp = await axios.get(result_url)
          // console.log('resp.data:', resp.data)
          var markdown_data = resp.data.replace(/http:\/\/zhai-datas.ks3-cn-beijing-internal\.ksyun\.com/g, 'http://zhai-datas.ks3-cn-beijing.ksyun.com')
          markdown_data = markdown_data.replace(/http:\/\/zhai-datas.ks3-cn-beijing-internal\.ksyuncs\.com/g, 'http://zhai-datas.ks3-cn-beijing.ksyuncs.com')
          markdown_data = markdown_data.replace(/(?<!\||>)\n(?!\|)/g, '\n\n')
          this.markDownData = markdown_data
          // 显示md结果
          this.showMarkDown = true
        } catch (error) {
          this.message('error', '获取pdf2md结果url内容失败: ', error)
          this.status = 0
          return Promise.resolve(0)
        }

        const { data } = res
        // await this.kdc2Markdown(data)
        this.result.wordUrl =
          data.wordUrl !== undefined ? data.wordUrl.replace('http:', '') : ''
        this.result.duration = data.duration // 处理用时
        this.result.pdfUrl = data.pdfUrl !== undefined ? data.pdfUrl : ''
        this.result.wordUrlKs =
          data.wordUrlKs !== undefined
            ? data.wordUrlKs.replace('http:', '')
            : ''
        this.handleOpTimes(data.opTimes, data.ops)

        this.getMeta(data)

        if (data.errMsgs !== undefined && data.errMsgs.length > 0) {
          this.result.errMsgs = data.errMsgs
          this.message('error', 'pdf2kdc服务报错: ', data.errMsgs)
          return Promise.reject('详情查看错误信息')
        } else {
          return Promise.resolve(1)
        }
      })
    },
    async kdc2Markdown(data) {
      // console.log('data.kdc:', data.kdc)
      if (data.kdc === undefined || data.errMsgs !== undefined) {
        this.message('error', '该kdc输入（pdf2kdc的结果/用户输入的kdc文件），不支持进行markdown转换: ', '')
        return
      }
      this.jsonData = data
      this.showJsonData = true
      if (data.kdc.doc !== undefined) {
        const req = {
          'doc': data.kdc.doc,
          'is_table_html': this.is_table_html
        }
        const res_data = await kdc2md(
          this.url.value,
          req
        )

        if (res_data.status === 200) {
          this.markDownData = res_data.data.data.replace(/http:\/\/zhai-datas.ks3-cn-beijing-internal\.ksyun\.com/g, 'http://zhai-datas.ks3-cn-beijing.ksyun.com')
          this.markDownData = this.markDownData.replace(/(?<!\||>)\n(?!\|)/g, '\n\n')
          // this.markDownData = this.markDownData + '$$\\Phi(t) = \\int_0^\\infty z^{t-1}e^{-z}dz\\,.$$'
          this.showMarkDown = true
        } else {
          this.message('error', '转换markdown错误: ', res_data.msg)
        }
      } else {
        this.message('warn', 'kdc.doc不能为空！', '')
      }
    },
    showJson() {
      this.copyData(this.jsonData)
    },
    copyMarkDownData() {
      this.copyData(this.markDownData)
    },
    copyData(data) {
      var data_value = JSON.stringify(data)
      // console.log('data_value:', data_value)

      var textArea = document.createElement('textarea')
      textArea.value = data_value
      document.body.appendChild(textArea)
      textArea.select()
      textArea.focus()
      document.execCommand('copy')
      document.body.removeChild(textArea)
      this.$message.success('复制成功')
    },
    getFileNameMD5(name) {
      const userId = this.$store.state.user.userId
      const deptId = this.$store.state.user.deptId
      const useremail = this.$store.state.user.email
      var a = name.lastIndexOf('.')
      var filter = name.substring(a + 1, name.length)
      var fname = name.substring(0, a)

      var mddata = fname + userId + useremail + deptId

      return CryptoJS.MD5(mddata).toString() + '.' + filter
    },
    /** 点击上传按钮操作 */
    async pdf2wordUpload(param) {
      // 开始加载
      this.loading = true
      // 清空
      this.jsonData = {}

      const { file } = param

      if (!this.beforeUpload(file)) {
        // 结束加载
        this.loading = false
        return Promise.reject()
      }
      // console.log('file:', file)
      const { name } = file

      const filenamebymd5 = this.getFileNameMD5(name)

      const res_sign_data = await httpRequesSignKs(filenamebymd5)

      if (res_sign_data.status === 200) {
        const res_uploadks3_data = await uploadKs3Store(res_sign_data, file)
        if (res_uploadks3_data.status === 200) {
          const imageurl =
            'https://' +
            res_sign_data.data.data['url'] +
            '/' +
            res_sign_data.data.data['key']

          this.originUrl = imageurl.replace(/http:\/\//g, 'https://')
          // console.log('this.originUrl:', this.originUrl)
          // 判断是否KDC文件
          this.kdcCase(file, imageurl)
        }
      }

      const s_time = new Date().getTime()
      // const status = await this.httpRequest(axios_config_get_docID)
      param.onSuccess()

      // 结束加载
      this.loading = false
      const e_time = new Date().getTime()
      this.statistic = {
        status: 1,
        duration: this.result.duration || (e_time - s_time) / 1000
      }
      return Promise.resolve()
    },
    httpRequesSign(axios_config, file) {
      axios(axios_config).then((res) => {
        const formData = new FormData()
        const key = res.data.data['key']
        formData.append('acl', res.data.data['acl'])
        formData.append('key', res.data.data['key'])
        formData.append('Signature', res.data.data['signature'])
        formData.append('KSSAccessKeyId', res.data.data['kSSAccessKeyId'])
        // eslint-disable-next-line no-undef
        formData.append('Policy', res.data.data['policy'])
        formData.append('file', file)
        console.log(res.data.data['url'])
        const url = 'https://' + res.data.data['url']

        const axios_config_post_ks3 = {
          method: 'post',
          headers: { 'Cache-Control': 'no-cache', ...getHeaders() },
          url: url,
          data: formData,
          responseType: 'json',
          processData: false, // 必须
          contentType: false
        }

        return axios(axios_config_post_ks3)
          .then((res) => {
            if (res.status === 200) {
              this.pdf2wordCreate(url + '/' + key)
            }
          })
          .catch((error) => {
            console.log(error)
          })
      })
    },
    // 获取文件信息
    getMeta(data) {
      if (data.fileMeta !== undefined) {
        this.classifier = data.fileMeta.Classifier
        this.keyWords = data.fileMeta.KeyWords
        this.summary = data.fileMeta.Summary
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/styles/mixin.scss";

/* 对话框底部按钮区域 */
.dialog-footer {
  display: flex;
  justify-content: space-between;
}
.reset-button {
  order: 1;
}
.right-buttons {
  order: 2;
}
.container {
  padding-bottom: 2%;
  padding-left: 8%;
  padding-right: 8%;
}
.el-row {
  margin-bottom: 20px;
  &:last-child {
    margin-bottom: 0;
  }
}
.el-col {
  border-radius: 8px;
  border: 1px dashed #d9d9d9;
  background-color: #ffffff;
  color: #666666;
  padding: 1%;
}
.bg-purple-dark {
  background: #99a9bf;
}
.bg-purple {
  background: #d3dce6;
}
.bg-purple-light {
  background: #e5e9f2;
}
.grid-content {
  border-radius: 8px;
  vertical-align: middle;
}
.row-bg {
  padding: 10px 0;
  background-color: #f9fafc;
}

.upload-container {
  width: 100%;
  padding: 1%;
  position: relative;
  @include clearfix;
  .params-container {
    width: 70%;
    display: inline-block;
    padding-left: 1%;
    padding-right: 1%;
  }
  .params {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }
  .param-label {
    width: 50%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-bottom: 2%;
  }
  .param-input {
    width: 50%;
    text-align: right;
    margin-bottom: 2%;
  }
}
// 左右分栏显示
.result-container {
  display: flex;
}
// 三个按钮居中显示
.show-button {
  display: flex;
  justify-content: center;
}
.left-column {
  flex: 2;
  overflow: auto;
//   margin-right: 1px;
}
.right-column {
  flex: 2;
  overflow: auto;
//   margin-right: 1px;
//   border: 1px solid black;
}
// 图片展示
.image-container {
//   position: relative;
  // width: 100%; /* 设置父元素的宽度为100% */
  border: 1px solid #0b58e7;
  // width: 650px;
  height: 800px; /* 设置父元素的固定高度 */
  width: 100%;
  // height: 100%;
  overflow: auto; /* 使用滚动条展示超过高度的内容 */
}
.image {
    display: block;
    width: 100%;
    height: auto;
}
</style>
