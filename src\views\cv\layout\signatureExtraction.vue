<template>
  <!-- 创建一个卡片式的容器，使用主题内容区域将背景色统一 -->
  <el-card>
    <!-- 标题插槽：显示使用说明 -->
    <div slot="header">
      <CVHint>使用说明：（签名提取）选择一张图片，可以选择 传统二值化/深度学习 两个版本，其中 深度学习版本还可以选择三种模式</CVHint>
    </div>

    <!-- 主体内容 -->
    <div>
      <div class="result-row">
        <el-row>
          <el-col v-if="containerHeight && containerWidth" :md="12" :style="{height: containerHeight, width: containerWidth}">
            <span class="image-label">Original Image:</span>
            <el-button v-if="showResult" type="text" size="large" @click="copyLink(backupUrl)">复制原图链接</el-button>
            <vue-cropper
              ref="cropper"
              :img="options.img"
              :info="true"
              :can-scale="false"
              :full="options.full"
              :output-size="options.outputSize"
              :view-mode-options="{ aspectRatio: containerWidth / containerHeight }"
              :auto-crop="options.autoCrop"
              :auto-crop-width="containerWidth"
              :auto-crop-height="containerHeight"
              :fixed-box="options.fixedBox"
              :can-move="false"
              :set-selection="getCoordinates"
            />
          </el-col>
          <!-- @click.native="getClickCoordinates($event)" -->

          <!-- <el-col v-if="version==2&&mode==2&&isUpload==true" :md="12">
            <h4>{{ '已添加的box: ' }}</h4>
            <span class="result" style="color:green">{{ inputBox }}</span>
          </el-col> -->

          <!-- <el-col v-if="showResult" :md="12" :style="{height: containerHeight, width: containerWidth}">
            <span class="image-label">Result:</span>
            <el-button type="text" size="large" @click="copyLink(url)">复制链接</el-button>
            <div v-if="version==1">
              <el-image :src="url" class="image-with-margin" />
            </div>
            <div v-else>
              <el-image :src="origin_url" class="image-with-margin" />
            </div>
          </el-col> -->

        </el-row>
      </div>
      <br>

      <!-- action="placeholder" -->
      <br>
      <div class="result-row">
        <el-upload action="#" :http-request="submitUpload" :show-file-list="false" :before-upload="beforeUpload">
          <el-button type="primary" size="small" style="width: 100px;">
            选择图片
            <i class="el-icon-upload el-icon--right" />
          </el-button>
        </el-upload>
        <el-row>
          <el-col :span="12">
            <el-button type="warning" size="small" @click="handleSetting">参数设置</el-button>
          </el-col>
          <el-col :span="12">
            <el-button type="success" size="small" @click="handleRequest">开始提取</el-button>
          </el-col>
        </el-row>
        <el-row v-if="version==2&&mode==2" style="margin-left: 10px;">
          <el-button type="success" size="small" @click="handleAddBox">add box</el-button>
          <el-button type="success" size="small" @click="handleReset">box重置</el-button>
        </el-row>
        <!-- <el-row style="margin-left: 10px;">
          <el-button v-if="origin_url!=''&&mask_url!=''" type="primary" size="small" :disabled="deleteDisabled" @click="maskRefine()">开始精修</el-button>
        </el-row> -->
      </div>
      <br>
      <h3>签名提取结果：</h3>
      <h4><span v-if="version==2&&mode==2">{{ 'mode=指定区域提取: 支持add多个box发起请求。使用add box按钮添加需要提取的区域, 添加完box后使用‘开始提取’发起请求。可以使用box重置按钮清空已添加的box。' }}</span></h4>
      <br>
      <span v-if="showResult" class="result" style="color:blue"> {{ "若是矩形框渲染易位/未显示，请点击下方的【复位】按钮重置。" }}</span>
      <el-button v-if="showResult" type="warning" size="small" @click="resetRectangles">复位</el-button>
      <hr>
      <el-row v-if="showResult" class="result-row" justify="center">
        <el-col v-if="version==2">
          <div class="result-image">
            <div v-for="(image, index) in result_image" :key="index">
              {{ '签名提取结果' + (index + 1) + '为:' }}
              <div v-if="version==2&&mode==1">
                <img v-if="image" :src="'data:image/jpeg;base64,' + image" alt="" :style="{ width: croppedImgWidth + 'px', height: croppedImgHeight + 'px' }">
              </div>
              <div v-else>
                <img v-if="image" :src="'data:image/jpeg;base64,' + image" alt="">
              </div>
              <!-- style="width: 500px;height: 368px;" -->
            </div>
          </div>
        </el-col>
        <el-col>
          <div v-if="version==2&&mode==1">
            <img :src="croppedImageData" alt="" :style="{ width: croppedImgWidth + 'px', height: croppedImgHeight + 'px' }">
          </div>
          <div v-else-if="version==1">
            <div class="result-image">
              <img :src="result_url" :style="{ width: containerWidth, height: containerHeight }">
            </div>
          </div>
          <div v-else>
            <div class="canvas-container" style="width: 750px;">
              <canvas ref="canvas" :width="imageWidth" :height="imageHeight" />
              <img :src="origin_url" @load="setImageSize">
            </div>
          </div>
          <div>
            <el-button v-if="mode!=1" size="small" type="primary" @click="zoomIn">放大</el-button>
            <el-button v-if="mode!=1" size="small" type="primary" @click="zoomOut">缩小</el-button>
          </div>
        </el-col>
      </el-row>
    </div>
    <br>

    <!-- 转换或点击上传前高级设置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="650px">
      <el-form ref="form" :model="form" label-width="80px" inline-message>
        <el-row>
          <el-col :span="12">
            <el-tooltip content="选择版本" placement="left-end">
              <template slot="default">
                <el-form-item label="version:">
                  <el-select v-model="form.version" placeholder="请选择" @change="$forceUpdate()">
                    <el-option
                      v-for="dict in versionOptions"
                      :key="dict.value"
                      :label="dict.label"
                      :value="Number(dict.value)"
                    />
                  </el-select>
                </el-form-item>
              </template>
            </el-tooltip>
          </el-col>
          <el-col v-if="form.version==2" :span="12">
            <el-tooltip content="选择模式" placement="right-end">
              <template slot="default">
                <el-form-item label="mode:">
                  <el-select v-model="form.mode" placeholder="请选择" @change="$forceUpdate()">
                    <el-option
                      v-for="dict in modeOptions"
                      :key="dict.value"
                      :label="dict.label"
                      :value="Number(dict.value)"
                    />
                  </el-select>
                </el-form-item>
              </template>
            </el-tooltip>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button class="reset-button" type="warning" @click="reset">重 置</el-button>
        <div class="right-buttons">
          <el-button class="confirm-button" type="primary" @click="submitForm">确 定</el-button>
          <el-button class="cancel-button" @click="cancel">取 消</el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 底部插槽：未使用，后续若是扩展可以考虑翻页等功能 -->
    <div slot="footer">
      footer
    </div>
  </el-card>
</template>

<script>
// import store from '@/store'
import CVHint from './components/CVHint'
// import CVCint from './components/CVCint'
import { VueCropper } from 'vue-cropper'
// import VueCanvas from 'vue-canvas'
import axios from 'axios'
import { getHeaders } from '@/utils/get-header'

export default {
  components: { CVHint, VueCropper },
  props: {
    // eslint-disable-next-line vue/require-default-prop
    user: { type: Object }
  },
  data() {
    return {
      // 是否显示弹出层
      open: false,
      // 弹出层标题
      title: '',
      options: {
        // img: store.getters.avatar, // 裁剪图片的地址
        img: 'https://zhai-datas.ks3-cn-beijing.ksyuncs.com/layout/people/wangluoyan/segmentation/%E9%80%89%E6%8B%A9%E4%B8%80%E5%BC%A0%E5%9B%BE%E7%89%87.png',
        outputSize: 1, // 裁剪生成图片的质量
        outputType: 'png', // 裁剪生成图片的格式
        full: true, // 是否输出原图比例的截图
        autoCrop: true, // 是否默认生成截图框
        autoCropWidth: 10, // 默认生成截图框宽度
        autoCropHeight: 10, // 默认生成截图框高度
        // fixedBox: true // 固定截图框大小 不允许改变
        fixedBox: false // 固定截图框大小 不允许改变
      },
      // 图片的缩放比例
      scale: 1,
      scaleFlag: false,
      // 图片展示宽度
      imageWidth: 750,
      imageHeight: 0,
      url: 'https://zhai-datas.ks3-cn-beijing.ksyuncs.com/layout/people/wangluoyan/segmentation/%E9%80%89%E6%8B%A9%E4%B8%80%E5%BC%A0%E5%9B%BE%E7%89%87.png',
      containerHeight: '',
      containerWidth: '',
      uploadurl: {
        value: '//pcv-test.wps.cn',
        default: '//pcv-test.wps.cn'
      },
      signatureurl: {
        value: '//pcv-test-scan.wps.cn'
      },
      // 显示结果
      showResult: false,
      // 原图url
      origin_url: '',
      // 版本
      versionOptions: [
        { label: '传统二值化', value: '1' },
        { label: '深度学习', value: '2' }
      ],
      // 模式
      modeOptions: [
        { label: '手写检测+签名提取', value: '0' },
        { label: '仅签名提取', value: '1' },
        { label: '指定区域提取', value: '2' }
      ],
      // 表单参数
      form: {
        version: 2,
        mode: 0
      },
      version: 2,
      mode: 0,
      // 得到的图像数据
      result_image: [],
      result_boxes: [],
      // 绘制签名框
      rectangles: [],
      originRectangles: [],
      backupUrl: '',
      inputBox: [],
      // 存储裁剪的图像数据
      croppedImageData: '',
      // 上传标志位
      isUpload: false,
      // 初始图片缩放比例
      originScale: 1,
      result_url: '',
      // 绘制框序号字体大小
      fontSize: 11,
      croppedImgHeight: 0,
      croppedImgWidth: 500
    }
  },
  mounted() {
    this.getImageSize()
  },
  methods: {
    calculateHeight() {
      const image = new Image()
      image.src = this.croppedImageData
      image.onload = () => {
        this.croppedImgHeight = (image.height / image.width) * this.croppedImgWidth
      }
      // console.log('this.croppedImgHeight:', this.croppedImgHeight)
      // console.log('this.croppedImgWidth:', this.croppedImgWidth)
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.version = this.form.version
          this.mode = this.form.mode
          this.open = false
        }
      })
    },
    // 取消按钮
    cancel() {
      this.form.version = this.version
      this.form.mode = this.mode
      this.open = false
    },
    // 表单重置
    reset() {
      this.form = {
        version: 2,
        mode: 0
      }
      this.resetForm('form')
    },
    /** 高级设置按钮操作 */
    handleSetting() {
      this.open = true
      this.title = '参数设置'
      this.resetModeInput()
    },
    /** 矩形框复位按钮 */
    resetRectangles() {
      this.zoomOut()
    },
    copyLink(url) {
      var inputNode = document.getElementById('pdfcv_input')
      if (inputNode != null) {
        inputNode.remove()
      }
      inputNode = document.createElement('input')
      // inputNode.value = this.url
      inputNode.value = url
      inputNode.id = 'pdfcv_input'
      document.body.appendChild(inputNode)
      inputNode.select()
      document.execCommand('Copy')
      inputNode.className = 'oInput'
      inputNode.style.display = 'none'
      this.$message.success('复制成功')
    },
    /** box重置 */
    handleReset() {
      this.inputBox = []
      this.rectangles = []
      this.originRectangles = []
      this.message('success', 'box成功重置! 可以重新添加新的box来发起请求', '')
    },
    /** add box */
    handleAddBox() {
      this.getCoordinates()
    },
    getImageSize() {
      var image = new Image()
      image.src = this.options.img

      image.onload = () => {
        var imageWidth = image.width
        var imageHeight = image.height
        // console.log('imageWidth:', imageWidth)
        // console.log('imageHeight:', imageHeight)

        if (imageWidth > 660) {
          this.scaleFlag = true
          var originalAspectRatio = imageWidth / imageHeight
          // 四舍五入保留一位小数
          this.scale = parseFloat((650 / imageWidth).toFixed(1))
          imageWidth = imageWidth * this.scale
          imageHeight = imageWidth / originalAspectRatio
        }

        this.containerHeight = imageHeight + 'px'
        this.containerWidth = imageWidth + 'px'
        // 保存初始图片缩放比例
        this.originScale = this.scale
      }
    },
    // 覆盖默认的上传行为
    requestUpload() {
    },
    // 向左旋转
    rotateLeft() {
      this.$refs.cropper.rotateLeft()
    },
    // 向右旋转
    rotateRight() {
      this.$refs.cropper.rotateRight()
    },
    // 图片缩放
    changeScale(num) {
      num = num || 1
      this.$refs.cropper.changeScale(num)
    },
    // 上传预处理
    beforeUpload(file) {
      // 复位
      this.resetResult()
      if (file.type.indexOf('image/') === -1) {
        this.msgError('文件格式错误，请上传图片类型,如：JPG，PNG后缀的文件。')
      } else {
        const reader = new FileReader()
        reader.readAsDataURL(file)
        reader.onload = () => {
          this.options.img = reader.result
          this.getImageSize()
        }
      }
    },
    // 重置
    resetResult() {
      // 缩放比例复位
      this.scaleFlag = false
      this.showResult = false
      this.version = 2
      this.mode = 0
      this.inputBox = []
      this.result_boxes = []
      this.result_image = []
      this.rectangles = []
      this.originRectangles = []
      this.backupUrl = ''
      this.croppedImageData = ''
    },
    // mode重置
    resetModeInput() {
      this.showResult = false
    },
    // 上传到服务器
    async submitUpload(param) {
      this.fileName = param.file.name
      const form = new FormData()
      form.append('file', param.file)
      form.append('origin', 'pdf-cv-demo')
      const axios_config_upload = {
        method: 'post',
        headers: { 'Cache-Control': 'no-cache', ...getHeaders() },
        url: this.uploadurl.value + '/layout/scheduler/samplecollect/upload',
        data: form,
        responseType: 'json',
        processData: false,
        contentType: false
      }
      console.log('等待上传...')
      // 上传
      await axios(axios_config_upload).then(res => {
        this.url = res.data.data.url
      })
      this.isUpload = true
      return Promise.resolve()
    },
    async handleRequest() {
      // 请求
      const s_time = new Date().getTime()
      const status = await this.beforeSignature(this.url)
      const e_time = new Date().getTime()
      // 状态和处理用时
      this.statistic = {
        status,
        duration: (e_time - s_time) / 1000
      }
    },
    getParam(url) {
      var data_dict = {}
      if (this.version === 2) {
        switch (this.mode) {
          case 0:
            data_dict = {
              'image_url': url,
              'version': this.version,
              'mode': this.mode
            }
            break
          case 1:
            this.getCroppedImage()
            return -1
          case 2:
            // this.getCoordinates()

            // if (this.inputBox.length === 0) {
            //   this.message('warning', '输入box不能为空, 请先add box', '')
            //   return -1
            // }
            data_dict = {
              'image_url': url,
              'version': this.version,
              'mode': this.mode,
              'boxes': this.inputBox
            }
        }
      } else {
        data_dict = {
          'image_url': url,
          'version': this.version
        }
      }
      return data_dict
    },
    // 签名提取
    async beforeSignature(url) {
      // 对传入的url进行处理
      this.origin_url = url.replace(/-internal/g, '')
      const data_dict = this.getParam(url)
      if (data_dict === -1) {
        return Promise.resolve(1)
      }
      this.signature(data_dict)
    },
    async signature(data_dict) {
      try {
        const response = await axios({
          method: 'post',
          headers: { 'Cache-Control': 'no-cache', 'Content-Type': 'application/json', ...getHeaders() },
          url: this.signatureurl.value + '/opt/signature',
          data: data_dict,
          responseType: 'json',
          processData: false,
          contentType: false,
          timeout: 80000 // 设置超时时间为80秒
        })
        if (response.data.code !== 200 || response.data.data === undefined) {
          return Promise.reject('处理失败!')
        }
        console.log('处理完成: ' + JSON.stringify(response.data.data))
        // 清空
        this.originRectangles = []
        this.rectangles = []
        // 根据版本和模式, 选择不同的返回处理
        if (this.version === 1) {
          // this.url = response.data.data.result_url
          this.v1ResultImgWidth =
          this.result_url = response.data.data.result_url.replace(/-internal/g, '')
        } else {
          this.result_image = response.data.data.results
          if (this.mode === 0 || this.mode === 2) {
            this.result_boxes = response.data.data.result_boxes
            response.data.data.result_boxes.forEach(box => {
              // const rect = {
              //   x: box[0],
              //   y: box[1],
              //   width: box[2] - box[0],
              //   height: box[5] - box[1]
              // }
              const rect = {
                x1: box[0],
                y1: box[1],
                x2: box[2],
                y2: box[3],
                x3: box[4],
                y3: box[5],
                x4: box[6],
                y4: box[7]
              }
              // 保存原始矩形框的坐标值
              this.originRectangles.push(rect)
              // 根据缩放比例调整矩形框的坐标值
              // const scaledRectangle = {
              //   x: rect.x * this.scale,
              //   y: rect.y * this.scale,
              //   width: rect.width * this.scale,
              //   height: rect.height * this.scale
              // }
              const scaledRectangle = {
                x1: rect.x1 * this.scale,
                y1: rect.y1 * this.scale,
                x2: rect.x2 * this.scale,
                y2: rect.y2 * this.scale,
                x3: rect.x3 * this.scale,
                y3: rect.y3 * this.scale,
                x4: rect.x4 * this.scale,
                y4: rect.y4 * this.scale
              }
              this.rectangles.push(scaledRectangle)
            })
          }
        }
        // 显示结果
        this.showResult = true
        return Promise.resolve(1)
      } catch (error) {
        this.message('error', '处理失败: ', error)
        return Promise.resolve(0)
      }
    },
    message(type, msg, dataMsg) {
      this.$message({
        showClose: true,
        message: msg + dataMsg,
        center: true,
        duration: 4000,
        type: type
      })
    },
    setImageSize(event) {
      const image = event.target
      // 存储原始图像高度宽度值
      this.originimageWidth = image.width
      this.originimageHeight = image.height
      // 计算初始scale
      this.scale = (this.imageWidth / this.originimageWidth).toFixed(4)
      this.imageHeight = (this.scale * this.originimageHeight).toFixed(2)

      this.drawRectangles(this.origin_url, this.rectangles, this.fontSize)
      // 关闭原始图片
      this.backupUrl = this.origin_url
      this.origin_url = ''
    },
    drawRectangles(url, rectanglesLatex, fontSize) {
      const canvas = this.$refs.canvas
      const ctx = canvas.getContext('2d')

      const image = new Image()
      image.src = url

      image.onload = () => {
        ctx.drawImage(image, 0, 0, this.imageWidth, this.imageHeight)

        ctx.lineWidth = 1
        // 绘制公式框
        ctx.strokeStyle = 'red'
        rectanglesLatex.forEach((rect, index) => {
          // 根据左上角点+宽高绘制框
          // ctx.strokeRect(rect.x, rect.y, rect.width, rect.height)

          // 根据四个顶点绘制框
          ctx.beginPath()
          // 连接四个点
          ctx.moveTo(rect.x1, rect.y1)
          ctx.lineTo(rect.x2, rect.y2)
          ctx.lineTo(rect.x3, rect.y3)
          ctx.lineTo(rect.x4, rect.y4)
          // 闭合路径
          ctx.closePath()
          // 描边
          ctx.stroke()

          // 设置文本字体字号
          // ctx.font = '8px Arial'
          ctx.font = fontSize.toString() + 'px Arial'

          // 绘制文本
          var text = (index + 1).toString()
          // 获取文本字体的宽度/高度
          var textWidth = ctx.measureText(text).width
          var textHeight = parseInt(ctx.font)

          // 设置白底
          ctx.fillStyle = 'white'
          ctx.fillRect(rect.x1 - 2, rect.y1 - textHeight + 2, textWidth + 4, textHeight)

          // 设置文本颜色
          ctx.fillStyle = 'rgba(0, 0, 255)'
          ctx.fillText(text, rect.x1, rect.y1)
        })
        console.log('绘制完成')
      }
    },
    /** 放大按钮 */
    zoomIn() {
      // 添加限制，避免过大
      if (this.imageWidth < 1000) {
        this.imageWidth = this.imageWidth + 100
        this.scale = (this.imageWidth / this.originimageWidth).toFixed(4)
      }
      // 放大字号
      if (this.fontSize < 15) {
        this.fontSize += 1
      }

      // 更新宽高值,并重新绘制
      this.updateImgSize()
    },
    /** 缩小按钮 */
    zoomOut() {
      // 添加限制，避免过小
      if (this.imageWidth > 10) {
        this.imageWidth = this.imageWidth - 50
        this.scale = (this.imageWidth / this.originimageWidth).toFixed(4)
      }
      // 缩小字号
      if (this.fontSize > 8) {
        this.fontSize -= 1
      }
      // 更新宽高值,并重新绘制
      this.updateImgSize()
    },
    updateImgSize() {
      this.imageHeight = (this.scale * this.originimageHeight).toFixed(2)

      // 得到更新后的坐标值
      // const updateRectangles = (rectangles, originalRectangles) => {
      //   rectangles.forEach((rectangle, index) => {
      //     rectangle.x = originalRectangles[index].x * this.scale
      //     rectangle.y = originalRectangles[index].y * this.scale
      //     rectangle.width = originalRectangles[index].width * this.scale
      //     rectangle.height = originalRectangles[index].height * this.scale
      //   })
      // }
      const updateRectangles = (rectangles, originalRectangles) => {
        rectangles.forEach((rectangle, index) => {
          rectangle.x1 = originalRectangles[index].x1 * this.scale
          rectangle.y1 = originalRectangles[index].y1 * this.scale
          rectangle.x2 = originalRectangles[index].x2 * this.scale
          rectangle.y2 = originalRectangles[index].y2 * this.scale
          rectangle.x3 = originalRectangles[index].x3 * this.scale
          rectangle.y3 = originalRectangles[index].y3 * this.scale
          rectangle.x4 = originalRectangles[index].x4 * this.scale
          rectangle.y4 = originalRectangles[index].y4 * this.scale
        })
      }
      updateRectangles(this.rectangles, this.originRectangles)

      // 重新绘制
      this.drawRectangles(this.backupUrl, this.rectangles, this.fontSize)

      // 关闭原始图片
      this.origin_url = ''
    },
    // 获取框坐标值
    async getCoordinates() {
      // 获取图片基于容器的坐标点,包含左上角和右下角坐标信息
      const imgAxis = this.$refs.cropper.getImgAxis()
      // 获取截图框基于容器的坐标点
      const cropAxis = this.$refs.cropper.getCropAxis()

      // 判断是否缩放
      var scale = 1
      if (this.scaleFlag) {
        scale = this.originScale
      }
      // 左上角坐标点, 图片基于容器的坐标点若是为负数, 则设置为0
      var cropBoxLeftX = Math.max(0, (cropAxis.x1 - Math.max(0, imgAxis.x1))) / scale
      var cropBoxLeftY = Math.max(0, (cropAxis.y1 - Math.max(0, imgAxis.y1))) / scale
      // 右下角坐标点
      var cropBoxRightX = Math.max(0, (cropAxis.x2 - Math.max(0, imgAxis.x1))) / scale
      var cropBoxRightY = Math.max(0, (cropAxis.y2 - Math.max(0, imgAxis.y1))) / scale

      // 保留一位小数
      cropBoxLeftX = Math.round(cropBoxLeftX)
      cropBoxLeftY = Math.round(cropBoxLeftY)
      cropBoxRightX = Math.round(cropBoxRightX)
      cropBoxRightY = Math.round(cropBoxRightY)

      var leftTop = {
        x: cropBoxLeftX,
        y: cropBoxLeftY
      }
      var rightTop = {
        x: cropBoxRightX,
        y: cropBoxLeftY
      }
      var leftBottom = {
        x: cropBoxLeftX,
        y: cropBoxRightY
      }
      var rightBottom = {
        x: cropBoxRightX,
        y: cropBoxRightY
      }
      var box = []
      box.push(leftTop.x, leftTop.y, rightTop.x, rightTop.y, rightBottom.x, rightBottom.y, leftBottom.x, leftBottom.y)
      this.inputBox.push(box)
      this.message('success', '添加box成功', '')
    },
    // 裁剪用户选中区域
    async getCroppedImage() {
      const w = this.inputBox[2] - this.inputBox[0]
      const h = this.inputBox[7] - this.inputBox[1]
      this.selection = { x: this.inputBox[0], y: this.inputBox[1], width: w, height: h }
      // this.$refs.cropper.setSelection(this.selection)
      await this.$refs.cropper.getCropData(async(data) => {
        // 绘制原图使用
        this.croppedImageData = data
        await this.calculateHeight()
        const imageData = data.split(',')[1]
        var data_dict = {
          'image_base64': imageData,
          'version': this.version,
          'mode': this.mode
        }
        this.signature(data_dict)
      })
    }
  }
}
</script>
<style lang="scss" scoped>
@import "~@/styles/mixin.scss";

.vue-cropper-preview {
position: relative;
width: 180px;
height: 180px;
border-radius: 50%;
box-shadow: 0 0 4px #ccc;
overflow: hidden;
}

.img-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

/* 对话框底部按钮区域 */
.dialog-footer {
  display: flex;
  justify-content: space-between;
}

.right-buttons {
  order: 2;
}

.result-row {
  display: flex;
  justify-content: center;
  align-items: center;
}

.image-label {
  margin-top: 10px;
  font-size: 16px;
  background-color: #333;
  color: #fff;
  padding: 5px 10px;
}

.button-row {
  display: flex;
  justify-content: space-between;
}

.image-with-margin {
  margin-bottom: 20px;
}

.canvas-container {
  position: relative;
  // width: 100%; /* 设置父元素的宽度为100% */
  width: 1000px;
  height: 800px; /* 设置父元素的固定高度 */
  // overflow: auto; /* 使用滚动条展示超过高度的内容 */
  overflow-y: scroll; /* 使用垂直滚动条 */
  overflow-x: scroll; /* 使用水平滚动条 */
  scroll-behavior: smooth; /* 平滑滚动效果 */
}

.result-image {
  position: relative;
  // width: 100%; /* 设置父元素的宽度为100% */
  width: 900px;
  height: 800px; /* 设置父元素的固定高度 */
  // overflow: auto; /* 使用滚动条展示超过高度的内容 */
  overflow-y: scroll; /* 使用垂直滚动条 */
  overflow-x: scroll; /* 使用水平滚动条 */
  scroll-behavior: smooth; /* 平滑滚动效果 */
}

</style>
