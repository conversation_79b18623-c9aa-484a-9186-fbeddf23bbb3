import request from '@/utils/request'

// 查询Upload2Ks3列表
export function listUpload2Ks3(query) {
  return request({
    url: '/api/v1/upload2ks3',
    method: 'get',
    params: query
  })
}

// 查询Upload2Ks3详细
export function getUpload2Ks3(id) {
  return request({
    url: '/api/v1/upload2ks3/' + id,
    method: 'get'
  })
}

// 新增Upload2Ks3
export function addUpload2Ks3(data) {
  return request({
    url: '/api/v1/upload2ks3',
    method: 'post',
    data: data
  })
}

// 修改Upload2Ks3
export function updateUpload2Ks3(data) {
  return request({
    url: '/api/v1/upload2ks3/' + data.id,
    method: 'put',
    data: data
  })
}

export function updateUpload2Ks3Status(data) {
  return request({
    url: '/api/v1/upload2ks3/status/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除Upload2Ks3
export function delUpload2Ks3(data) {
  return request({
    url: '/api/v1/upload2ks3',
    method: 'delete',
    data: data
  })
}
export function uploadFileBaseViaUrl(data) {
  return request({
    url: '/api/v1/upload2ks3/upload_file_base_via_url',
    method: 'post',
    data: data
  })
}