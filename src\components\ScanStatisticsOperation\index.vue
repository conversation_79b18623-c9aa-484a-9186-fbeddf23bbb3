<template>
  <div>
    <el-button
      type="text"
      size="mini"
      @click="openExtraInfo()"
    > 详细信息
    </el-button>
    <el-dialog :visible.sync="extraOpen" height="calc(100%)" width="1500px">
      <el-table :data="extraInfoData">
        <el-table-column
          v-if="false"
          type="selection"
          width="700"
          align="center"
        /><el-table-column
          label="日期"
          align="center"
          prop="day"
          :show-overflow-tooltip="true"
        /><el-table-column
          label="任务总数"
          align="center"
          sortable
          prop="request_total"
          :show-overflow-tooltip="true"
        /><el-table-column
          label="成功任务数"
          align="center"
          sortable
          prop="request_success"
          :show-overflow-tooltip="true"
        /><el-table-column
          label="失败任务数"
          align="center"
          prop="failedCount"
          sortable
          :show-overflow-tooltip="true"
        /><el-table-column
          label="成功率"
          align="center"
          prop="successRate"
          sortable
          :show-overflow-tooltip="true"
        /><el-table-column
          label="失败率"
          align="center"
          prop="failedRate"
          sortable
          :show-overflow-tooltip="true"
        /><el-table-column
          label="平均耗时(s)"
          align="center"
          prop="requestTimeAvgS"
          sortable
          :show-overflow-tooltip="true"
        /><el-table-column
          label="最小耗时(s)"
          align="center"
          prop="requestTimeMinS"
          sortable
          :show-overflow-tooltip="true"
        /><el-table-column
          label="最大耗时(s)"
          align="center"
          prop="requestTimeMaxS"
          sortable
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="服务名称"
          align="center"
          prop="service"
          :show-overflow-tooltip="true"
        /> <el-table-column
          label="路由名称"
          align="center"
          prop="router"
          max-width="300px"
          :show-overflow-tooltip="true"
        /><el-table-column
          label="操作"
          align="center"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            <a style="color: blue" @click="gotoScanRequest(scope.row)">任务详情</a>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import { getStatisticsList } from '@/api/scan/statistics'

export default {
  name: 'ScanStatisticsOperation',
  props: {
    day: {
      type: Number,
      default: 0
    },
    service: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      extraOpen: false,
      extraInfoData: []
    }
  },
  methods: {
    openExtraInfo() {
      this.extraOpen = true
      this.getExtraInfoData(this.day, this.service)
    }, getExtraInfoData(day, service) {
      if (service === 'all') {
        service = ''
      }
      const qParams = {
        dataType: '0',
        pageIndex: 1,
        pageSize: 999,
        startTime: day,
        endTime: day,
        service: service,
        router: ''
      }
      getStatisticsList(qParams).then((response) => {
        console.log(response.data.list)
        if (response.data.list !== undefined && response.data.list !== null) {
          for (var i = response.data.list.length - 1; i >= 0; i--) {
            var successRate = this.calSuccessRate(response.data.list[i])
            var failedRate = this.calFailedRate(response.data.list[i])
            var failedCount = this.calFailedCount(response.data.list[i])
            response.data.list[i].successRate = successRate
            response.data.list[i].failedRate = failedRate
            response.data.list[i].failedCount = failedCount
            var requestTimeAvgS = response.data.list[i].request_time_avg / 1000
            var requestTimeMaxS = response.data.list[i].request_time_max / 1000
            var requestTimeMinS = response.data.list[i].request_time_min / 1000
            if (response.data.list[i].request_total === 0) {
              requestTimeMinS = 0
            }
            response.data.list[i].requestTimeAvgS = requestTimeAvgS
            response.data.list[i].requestTimeMaxS = requestTimeMaxS
            response.data.list[i].requestTimeMinS = requestTimeMinS
          }
        }
        this.extraInfoData = response.data.list
      })
    }, calFailedCount(row) {
      return row.request_total - row.request_success
    },
    calSuccessRate(row) {
      if (row.request_success === 0 || row.request_total === 0) {
        return 0
      }
      return this.toFixedWithoutZero((row.request_success / row.request_total * 100), 2)
    },
    calFailedRate(row) {
      if (row.request_total === 0) {
        return 0
      }
      return this.toFixedWithoutZero(((row.request_total - row.request_success) / row.request_total) * 100, 2)
    }, toFixedWithoutZero(num, fixed) {
      const result = num.toFixed(fixed)
      if (result.endsWith('0')) {
        return parseFloat(result)
      }
      return result
    }, gotoScanRequest(row) {
      this.extraOpen = false
      const st = row.day.toString()
      this.$router.push({ path: '/scan/scan-request', query: { startTime: st, router: row.router, service: row.service }})
    }

  }
}
</script>
