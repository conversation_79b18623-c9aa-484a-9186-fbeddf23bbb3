<template>
  <!-- 创建一个卡片式的容器，使用主题内容区域将背景色统一 -->
  <el-card>
    <!-- 标题插槽：显示使用说明 -->
    <div slot="header">
      <CVHint>使用说明：（表格提取）选择一张或多张图片或一个pdf文件，系统会对文件进行处理，输出格式html会在页面展示结果，若输出格式xlsx则会输出对应文档</CVHint>
    </div>

    <!-- 主体内容 -->
    <div class="card-content">
      <div>
        <div class="container">
          <el-row type="flex" justify="center" :gutter="20">
            <!-- 将不同版本的提示语挪为单独一列 -->
            <el-col :span="5" class="grid-content">
              <div class="params-container" style="width: 100% !important">
                <div class="params">
                  <span style="color:blue"> {{ "版本信息：" }}</span>
                </div><br>
                <div class="params">
                  <span v-show="remark === ''" style="color:darkblue"> {{ "已是最新效果。" }}</span>
                </div>
                <div class="params">
                  <!-- 不同版本对应的提示语 -->
                  <span style="color:darkblue"> {{ remark }}</span>
                </div>
              </div>
            </el-col>

            <el-col :span="8" class="grid-content">
              <span style="color:blue"> {{ "参数设置：" }}</span>
              <div class="upload-container">
                <!-- <div class="params-container" style="width: 100% !important">
                  <div class="params">
                    <span class="param-label" style="width: 25% !important">url :</span>
                    <el-input
                      v-model="url.value"
                      class="param-input"
                      style="width: 75% !important"
                      type="text"
                      @change="urlChange"
                    />
                  </div>
                </div> -->
                <div class="params-container" style="width: 100% !important">

                  <div class="params">
                    <span class="param-label" style="width: 25% !important">版本 :</span>
                    <el-select
                      v-model="startPage.version"
                      class="param-input"
                      style="width: 75% !important"
                      placeholder="版本"
                      size="small"
                      @change="selectVersion"
                    >
                      <el-option
                        v-for="dict in versions"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                        @click.native="handleSelect(dict)"
                      />
                    </el-select>
                  </div>
                  <div class="params">
                    <span
                      class="param-label"
                      style="width: 25% !important"
                    >dpi :</span>
                    <el-input
                      v-model="dpi.value"
                      class="param-input"
                      style="width: 75% !important"
                      type="number"
                      :min="dpi.min"
                      :max="dpi.max"
                      @change="dpiChange"
                    />
                  </div>
                  <!-- <div class="params">
                    <span
                      class="param-label"
                      style="width: 25% !important"
                    >url of png :</span>
                    <el-input
                      v-model="fileUrl"
                      class="param-input"
                      style="width: 75% !important"
                      type="text"
                    />
                  </div> -->
                  <div class="params">
                    <span
                      class="param-label"
                      style="width: 25% !important"
                    >输出 :</span>
                    <el-select
                      v-model="startPage.exporttype"
                      class="param-input"
                      style="width: 75% !important"
                      placeholder="导出类型"
                      size="small"
                      @change="selectExporttype"
                    >
                      <el-option
                        v-for="dict in exporttypeOptions"
                        :key="dict"

                        :value="dict"
                      />
                    </el-select>
                  </div>
                  <div v-show="showNewVersion" class="params">
                    <span
                      class="param-label"
                      style="width: 25% !important"
                    >sheet拆分 :</span>

                    <el-select
                      v-model="startPage.splitsheet"
                      class="param-input"
                      style="width: 75% !important"
                      placeholder="拆分类型"
                      size="small"
                    >
                      <el-option
                        v-for="dict in selectOptions2"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </div>
                  <div v-show="showNewVersion" class="params">
                    <span
                      class="param-label"
                      style="width: 25% !important"
                    >表格外文本 :</span>
                    <el-select
                      v-model="startPage.pagetext"
                      class="param-input"
                      style="width: 75% !important"
                      placeholder=""
                      size="small"
                    >
                      <el-option
                        v-for="dict in selectOptions"
                        :key="dict"
                        :value="dict"
                      />
                    </el-select>
                  </div>
                  <div v-show="showNewVersion" class="params">
                    <span
                      class="param-label"
                      style="width: 30% !important"
                    >表格外文本样式 :</span>
                    <el-select
                      v-model="startPage.pagetextoption"
                      class="param-input"
                      style="width: 70% !important"
                      placeholder=""
                      size="small"
                    >
                      <el-option
                        v-for="dict in pagetextOptions"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </div>
                  <br><div class="params">
                    <el-button
                      type="warning"
                      size="mini"
                      @click="handleSetting"
                    >高级设置</el-button>
                    <el-tooltip content="切换输入方式" placement="right-end">
                      <el-select v-model.number="choiceInput" placeholder="默认上传文件">
                        <el-option
                          v-for="dict in choiceInputOptions"
                          :key="dict.value"
                          :label="dict.label"
                          :value="dict.value"
                        />
                      </el-select>
                    </el-tooltip>
                    <!-- <el-button
                      type="primary"
                      size="mini"
                      :disabled="startPage.exporttype==='picedit'"
                      @click="pdf2wordconvertclick"
                    >转换</el-button> -->
                  </div>

                  <hr>
                  <div v-if="choiceInput=='inputUrl'">
                    <br><span style="color:blue"> {{ "输入图片链接：" }}</span><br>
                    <div class="params">
                      <span
                        class="param-label"
                        style="width: 25% !important"
                      >url of png:</span>
                      <el-input
                        v-model="fileUrl"
                        class="param-input"
                        style="width: 75% !important"
                        type="text"
                      />
                    </div>
                    <el-button
                      type="primary"
                      size="mini"
                      :disabled="startPage.exporttype==='picedit'"
                      @click="pdf2wordconvertclick"
                    >开始转换</el-button>
                  </div>

                  <!-- 转换或点击上传前高级设置对话框 -->
                  <el-dialog :title="title" :visible.sync="open" width="600px">
                    <el-form ref="form" :model="form" :rules="rules" label-width="120px" inline-message>

                      <el-row>
                        <el-col :span="12">
                          <!-- <el-form-item label="origin_height:" prop="originHeight">
                            <el-input v-model.number="form.originHeight" placeholder="请输入originHeight：" />
                          </el-form-item> -->
                          <el-tooltip content="原始高度" placement="left-end">
                            <template slot="default">
                              <el-form-item label="origin_height:" prop="originHeight">
                                <el-input v-model.number="form.originHeight" placeholder="请输入originHeight：" />
                              </el-form-item>
                            </template>
                          </el-tooltip>
                        </el-col>

                        <el-col :span="12">
                          <!-- <el-form-item label="origin_width:" prop="originWidth">
                            <el-input v-model.number="form.originWidth" placeholder="请输入originWidth：" />
                          </el-form-item> -->
                          <el-tooltip content="原始宽度" placement="right-end">
                            <template slot="default">
                              <el-form-item label="origin_width:" prop="originWidth">
                                <el-input v-model.number="form.originWidth" placeholder="请输入originWidth：" />
                              </el-form-item>
                            </template>
                          </el-tooltip>
                        </el-col>
                      </el-row>
                    </el-form>

                    <div slot="footer" class="dialog-footer">
                      <el-button class="reset-button" type="warning" @click="reset">重 置</el-button>
                      <div class="right-buttons">
                        <el-button class="confirm-button" type="primary" @click="submitForm">确 定</el-button>
                        <el-button class="cancel-button" @click="cancel">取 消</el-button>
                      </div>
                    </div>
                  </el-dialog>
                </div>

                <!-- 新增转化结果 -->
                <!-- <div class="params-container">
                  <br><div v-show="fileName !== ''" class="params">
                    <span style="color:blue" class="param-label">{{ '转化结果' }} :</span>
                  </div>
                  <div v-show="fileName !== ''" class="params">
                    <span class="param-label">{{ fileName }}:</span>
                    <span class="param-input">
                      {{ fileSize }}
                    </span>
                  </div>
                  <div v-show="statistic.status !== -1" class="params">
                    <span class="param-label">处理用时:</span>
                    <span class="param-input">
                      {{ formattedDuration }}
                    </span>
                  </div>
                  <div v-show="statistic.status !== -1" class="params">
                    <span v-show="statistic.status === 1" class="param-label" style="color:blue;">转化成功!</span>
                    <span v-show="statistic.status === 0" class="param-label" style="color: #ff0000;">转化失败!</span>
                  </div>
                </div> -->

                <div v-if="choiceInput=='uploadFile'">
                  <br><span style="color:blue"> {{ "上传图片/pdf文件：" }}</span>
                  <el-upload
                    ref="upload"
                    v-loading="loading"
                    element-loading-text="文件处理中"
                    class="uploader"
                    list-type="text"
                    :accept="fileType.join(',')"
                    :multiple="true"
                    :show-file-list="true"
                    :file-list="fileList"
                    :http-request="tableExtractUpload2"
                    :on-remove="handleRemove"
                    :on-change="handleChange"
                    :auto-upload="true"
                    action="placeholder"
                    drag
                  >

                    <i class="el-icon-upload" />
                    <div class="el-upload__text">
                      将文件拖到此处，或
                      <el-button type="text">点击上传</el-button>
                    </div>

                  </el-upload>
                  <br><div class="params">
                    <el-button size="small" type="primary" @click="submitUpload">开始转换</el-button>
                    <el-button size="small" type="success" @click="clearUpload">清空上传列表</el-button>
                  </div>
                </div>
              </div>

              <div class="params-container">
                <br><div v-show="fileName !== ''" class="params">
                  <br><div><span style="color:darkblue"> {{ '转化成功后的结果在页面下方展示。' }}</span></div><br>
                  <span style="color:blue" class="param-label">{{ '转化结果：' }}</span>
                </div>
                <div v-show="fileName !== ''" class="params">
                  <span class="param-label">{{ fileName }}:</span>
                  <span class="param-input">
                    {{ fileSize }}
                  </span>
                </div>
                <div v-show="statistic.status !== -1" class="params">
                  <span class="param-label">处理用时:</span>
                  <span class="param-input">
                    {{ formattedDuration }}
                  </span>
                </div>
                <div v-show="statistic.status !== -1" class="params">
                  <span v-show="statistic.status === 1" class="param-label" style="color:blue;">转化成功!</span>
                  <span v-show="statistic.status === 0" class="param-label" style="color: #ff0000;">转化失败!</span>
                </div>
              </div>

              <ul>
                <li v-for="(errMsg, index) in result.errMsgs" :key="index">
                  {{ errMsg }}
                </li>
              </ul>
            </el-col>
            <el-col
              :span="5"
              class="grid-content"
            >
              <CVDemo :thumbnail="true" :loading="loading" :data="urlFiles[0]" @click="tableextractDemo(0,$event)">示例:</CVDemo>
            </el-col>
          </el-row>
        </div>
        <el-row
          v-show="
            percentage === 100
          "
          type="flex"
          justify="space-around"
        >
          <el-col v-show="result.xlsxUrl !== ''" :span="4" class="grid-content">
            <div>
              <a :href="result.xlsxUrl" target="_blank" download="">
                <!-- <img src="@/assets/cv/word.png" style="width: 100%"> -->
                <img :src="result.xlsxicon" style="width: 100%">
              </a>
              <div>PDF自研工具输出成功，点击图标下载</div>
            </div>
          </el-col>
        </el-row>
        <el-row v-show="htmlsInZip.length!==0"><el-col>
          <el-collapse :value="['to']">
            <el-collapse-item title="原图" name="from">
              <div v-for="(imgUrl, index) in originalImages" :key="index"><img :src="imgUrl" alt=""></div>
            </el-collapse-item>
            <el-collapse-item title="转换结果" name="to">
              <div v-for="(html, index) in htmlsInZip" :key="index" class="extracted-table-container" v-html="html" />
            </el-collapse-item>
          </el-collapse>
        </el-col></el-row>
      </div>
    </div>

    <!-- 底部插槽：未使用，后续若是扩展可以考虑翻页等功能 -->
    <div slot="footer">
      Card Footer
    </div>
  </el-card>
</template>

<script>
import axios from 'axios'
import jszip from 'jszip'
import CVHint from './components/CVHint'
import CVDemo from './components/CVDemo'

import { urlRegex, formatfileSize, isValidFile } from '@/api/cv/utils'
import { axios_wait_doc } from '@/api/cv/request-status'
import {
  fetchUrl2Ks
} from '@/api/cv/request-ks3'
import { getHeaders } from '@/utils/get-header'
import { sleep } from '@/api/cv/utils'

export default {
  name: 'TableExtract',
  components: { CVHint, CVDemo },
  props: {
    maxFileNum: {
      validator(value) {
        if (value <= 0) {
          return 1
        }
        return value
      },
      default: 1
    },
    fileType: {
      type: Array,
      default: function() {
        return ['.png', '.jpg', '.jpeg', '.pdf', '.zip']
      }
    }
  },
  data() {
    return {
      // 输入方式字典
      choiceInputOptions: [
        { label: '上传图片/pdf文件(默认)', value: 'uploadFile' },
        { label: '输入图片链接', value: 'inputUrl' }
      ],
      choiceInput: 'uploadFile',
      // dpi 设置
      dpi: {
        value: 144,
        default: 144,
        min: 1,
        max: 400
      },
      // 弹出层标题（用户导入）
      title: '',
      // 是否显示弹出层（用户导入）
      open: false,
      // 表单参数
      form: {
        originHeight: 10,
        originWidth: 10
      },
      // 参数
      pdfDebugFlags: 0,
      originHeight: 10,
      originWidth: 10,
      // 表单校验
      rules: {
        // 定义规则对象
        // trigger: 'blur' 表示在失去焦点时触发验证
        originHeight: [{ type: 'number', message: 'originHeight必须为int类型', trigger: ['blur', 'change'] }],
        originWidth: [{ type: 'number', message: 'originWidth必须为int类型', trigger: ['blur', 'change'] }]
      },
      loading: false,
      url: {
        value: '//pcv-test.wps.cn',
        default: '//pcv-test.wps.cn'
      },
      fileObj: {
        name: '',
        size: 0
      },
      versionremark: '',
      remark: '',
      showNewVersion: false,
      versions: [],
      exporttypeOptions: ['html', 'xlsx'],
      selectOptions: ['是', '否'],
      selectOptions2: [{ 'label': '一个表格一个sheet', 'value': 0 }, { 'label': '一张图片一个sheet', 'value': 1 }, { 'label': '所有图片一个sheet', 'value': 2 }],
      pagetextOptions: [{ 'label': '所有文本一个单元格', 'value': 0 }, { 'label': '每段文本一个单元格', 'value': 1 }],
      startPage: {

        exporttype: 'html',
        version: '',
        xlsxicon: '',
        splitsheet: '一个表格一个sheet',
        pagetext: '否',
        pagetextoption: '所有文本一个单元格'
      },
      fileUrl: '',
      fileList: [],
      fileData: '',
      percentage: 0,
      result: {
        duration: 0,
        zipUrl: '',
        xlsxUrl: '',
        errMsgs: []
      },
      statistic: {
        status: -1,
        duration: 0
      },
      originalImages: [],
      htmlsInZip: [],
      urlFiles: [
        [
          {
            url: 'http://zhai-datas.ks3-cn-beijing.ksyun.com/layout/people/luhaoxian/table/示例一.jpeg',
            name: '示例一.jpeg',
            size: '210114'
          },
          {
            url: 'http://zhai-datas.ks3-cn-beijing.ksyun.com/layout/people/luhaoxian/table/示例二.jpeg',
            name: '示例二.jpeg',
            size: '122470'
          },
          {
            url: 'http://zhai-datas.ks3-cn-beijing.ksyun.com/layout/people/luhaoxian/table/示例三.jpeg',
            name: '示例三.jpeg',
            size: '2003254'
          },
          {
            url: 'http://zhai-datas.ks3-cn-beijing.ksyun.com/layout/people/luhaoxian/table/示例四.jpeg',
            name: '示例四.jpeg',
            size: '1380232'
          },
          {
            url: 'http://zhai-datas.ks3-cn-beijing.ksyun.com/layout/people/luhaoxian/table/示例五.jpeg',
            name: '示例五.jpeg',
            size: '36367'
          }
        ]
      ]
    }
  },
  computed: {
    formattedDuration() {
      return this.statistic.duration.toFixed(3) + 's'
    },
    fileName() {
      const { name } = this.fileObj
      return name.trim()
    },
    fileSize() {
      const { size } = this.fileObj
      return formatfileSize(size)
    }
  },
  // 通过watch监听输入值的变化，并将maxConNumPage/pdfDebugFlags由 string类型转为 int类型
  watch: {
    'form.pdfDebugFlags'(val) {
      this.pdfDebugFlagsInt = parseInt(val)
    }
  },
  created() {
    this.getDicts('pdf_convert_version').then(response => {
      this.versions = response.data
      if (this.versions.length > 0) {
        this.url.value = this.versions[0].value
        this.startPage.version = this.versions[0].label
        this.remark = this.versions[0].remark
      }
    })
  },
  methods: {
    // dpi 设置
    numberInputChange(obj, value) {
      if (value === '') {
        obj.value = obj.default
      } else if (value > obj.max) {
        obj.value = obj.max
      } else if (value < obj.min) {
        obj.value = obj.min
      }
      obj.value = parseInt(value)
    },
    dpiChange(value) {
      this.numberInputChange(this.dpi, value)
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          // this.pdfDebugFlags = this.form.pdfDebugFlags
          this.originHeight = this.form.originHeight
          this.originWidth = this.form.originWidth
          this.open = false
        }
      })
    },
    // 取消按钮
    cancel() {
      this.form.originHeight = this.originHeight
      this.form.originWidth = this.originWidth
      this.open = false
      // this.reset()
    },
    /** 重置按钮 */
    // 表单重置
    reset() {
      this.form = {
        originHeight: 10,
        originWidth: 10
      }
      this.resetForm('form')
    },
    /** 高级设置按钮操作 */
    handleSetting() {
      // this.reset()

      this.open = true
      this.title = '高级设置'
      this.form.password = ''
    },
    urlChange(value) {
      // url 检测
      if (urlRegex.test(value)) {
        this.url.value = value
      } else {
        this.url.value = this.url.default
      }
    },
    handleSelect(item) {
      this.remark = item.remark
    },
    selectVersion(value) {
      this.versionremark = ''
      this.url.value = value
    },
    selectExporttype(value) {
      if (value === 'html') {
        this.showNewVersion = false
        this.startPage.pagetextoption = '所有文本一个单元格'
        this.startPage.splitsheet = '一个表格一个sheet'
      } else {
        this.showNewVersion = true
        this.versionremark = ''
      }
    },
    fileChange(file, fileList) {
      this.fileList = fileList.slice(-this.maxFileNum)
    },
    onSuccess(response, file, fileList) {
      // hook
      this.fileChange(file, fileList)
    },
    resetResult() {
      this.result = {
        duration: 0,
        zipUrl: '',
        errMsgs: [] }
      this.statistic = {
        status: -1,
        duration: 0
      }
      this.originalImages = []
      this.htmlsInZip = []
    },
    beforeUpload(file) {
      // 清理旧结果
      this.fileObj = { name: '', size: 0 }
      this.resetResult()

      const { name } = file
      var ext = '.' + name.trim().split('.').pop()
      ext = ext.toLowerCase()
      if (!isValidFile(ext, this.fileType)) {
        this.$message({
          showClose: true,
          message: '请选择png/jpg/jpeg/pdf文件/zip文件!',
          center: true,
          duration: 4000,
          type: 'error'
        })
        return false
      } else {
        this.fileObj = file
        return true
      }
    },
    /** 转换按钮操作 */
    async pdf2wordconvertclick() {
      console.log('pdf2wordconvertclick')
      console.log(this.fileUrl)
      if (!this.validateUploadPDFUrl(this.fileUrl)) {
        this.$message({
          showClose: true,
          message: '请输入正确文件地址url ',
          center: true,
          duration: 4000,
          type: 'error'
        })
        return false
      }
      console.log('url匹配通过')

      this.loading = true

      if (this.fileUrl.lastIndexOf('ksyun.com') > 0) {
        var urls = []
        urls.push(this.fileUrl)
        const s_time = new Date().getTime()
        // 表格提取的url，相应的请求体在这
        const status = await this.tableextract(urls)
        // 结束加载
        this.loading = false
        const e_time = new Date().getTime()
        this.statistic = {
          status,
          duration: this.result.duration || (e_time - s_time) / 1000
        }
        return Promise.resolve()
      } else {
        this.fetchUrl2Ks3(this.fileUrl)
      }
      this.loading = false
    },
    async fetchUrl2Ks3(sourceurl) {
      fetchUrl2Ks(this, sourceurl).then(async(res) => {
        if (res.data.message === 'ok') {
          const ksurl = res.data.url
          console.log(ksurl)
          var urls = []
          urls.push(ksurl)
          const s_time = new Date().getTime()
          const status = await this.tableextract(urls)
          // 结束加载
          this.loading = false
          const e_time = new Date().getTime()
          this.statistic = {
            status,
            duration: this.result.duration || (e_time - s_time) / 1000
          }
          return Promise.resolve()
        }
      }).catch((error) => {
        this.$message({
          showClose: true,
          message: '文件处理失败! ' + error,
          center: true,
          duration: 4000,
          type: 'error'
        })
        return Promise.resolve(0)
      })
    },
    validateUploadPDFUrl(fileurl) {
      if (fileurl.trim() === '') {
        return false
      }
      // if (!urlRegex.test(fileurl)) {
      if (urlRegex.test(fileurl)) {
        return false
      }
      return true
    },
    fetchXlsx(url) {
      this.result.xlsxUrl = url !== undefined
        ? url.replace('http:', '')
        : ''
      this.result.xlsxicon = require('../../../assets/cv/pdf2xlsx.png')
      this.percentage = 100
      return Promise.resolve(0)
    },
    fetchAndUnzip(url) {
      // url = 'http://zhai-platereduction.ks3-cn-beijing.ksyun.com/layout_test/tableextract/tmp/2021-11-05/b63cf4d5-40ba-4754-8607-fc0384bd76e0/b63cf4d5-40ba-4754-8607-fc0384bd76e0.zip?Expires=1636163178&AWSAccessKeyId=AKLTvraLpUToTR6_PmBcKXIRCw&Signature=oFEaHHd2XxUBpUZWoSIfhbo1Nlk%3D'
      // url = url.replace('http://zhai-platereduction.ks3-cn-beijing.ksyun.com', '/kso')
      // url = url.replace('http://zhai-platereduction.ks3-cn-beijing-internal.ksyun.com', '/kso')
      // 线上版
      url = url.replace(/^http:\/\/zhai-platereduction\.ks3-cn-beijing(-internal)?\.ksyun\.com/, '/kso')
      // 开发版
      url = url.replace(/^http:\/\/zhai-datas\.ks3-cn-beijing(-internal)?\.ksyun\.com/, '/kso-datas')
      let blob
      return axios({
        method: 'get',
        url,
        responseType: 'blob'
      }).then(res => {
        blob = new Blob([res.data], { type: 'application/zip' })
        return jszip.loadAsync(blob)
      }).then((zip) => {
        const promises = []
        Object.keys(zip.files).forEach((fileName) => {
          promises.push(zip.file(fileName).async('string'))
        })
        return Promise.all(promises)
      })
      // .then(data => {
      //   console.log('所有结果：', data)
      //   return Promise.resolve(data)
      // })
        .catch(error => {
          console.log('下载解压文件失败, ', error)
          return Promise.reject([])
        })
    },
    clearUpload() {
      this.$refs.upload.clearFiles()
      this.fileList = []
    },
    async submitUpload() {
      const s_time = new Date().getTime()
      this.loading = true
      var urls = []
      for (var element of this.fileList) {
        console.log(element.name)
        const form = new FormData()
        form.append('file', element)
        form.append('origin', 'pdf-cv-demo')
        const axios_config_upload = {
          method: 'post',
          headers: { 'Cache-Control': 'no-cache', ...getHeaders() },
          url: this.url.value + '/layout/scheduler/samplecollect/upload',
          data: form,
          responseType: 'json',
          processData: false,
          contentType: false
        }

        await axios(axios_config_upload).then(res => {
          const url = res.data.data.url
          urls.push(url)
        })
      }

      const status = await this.tableextract(urls)
      this.loading = false
      const e_time = new Date().getTime()
      this.statistic = {
        status,
        duration: this.result.duration || (e_time - s_time) / 1000
      }

      return Promise.resolve()
    },
    async tableextractDemo(arrayid, id) {
      console.log('array', arrayid, ' index', id)
      // 开始加载
      this.loading = true

      const file = this.urlFiles[parseInt(arrayid)][parseInt(id)]

      this.fileObj = { name: file.name, size: file.size }
      this.resetResult()

      const s_time = new Date().getTime()
      const status = await this.tableextract([file.url])
      // 结束加载
      this.loading = false
      const e_time = new Date().getTime()
      this.statistic = {
        status,
        duration: this.result.duration || (e_time - s_time) / 1000
      }
      return Promise.resolve()
    },
    handleRemove(file, fileList) {
      this.fileList = fileList
    },
    handleChange(file, fileList) {
    // var existFile= fileList.slice(0,fileList.length-1).find(f=>f.name==file.name)
    //  if (existFile){
    //      fileList.pop()
    //  }
    //  this.fileList=fileList

    },
    tableExtractUpload2(param) {
      this.fileList.push(param.file)
    },
    async tableExtractUpload(param) {
      // 开始加载
      this.loading = true
      console.log(param)
      const { file } = param
      if (!this.beforeUpload(file)) {
        // 结束加载
        this.loading = false
        return Promise.reject()
      }

      const s_time = new Date().getTime()
      const form = new FormData()
      form.append('file', file)
      form.append('origin', 'pdf-cv-demo')
      const axios_config_upload = {
        method: 'post',
        headers: { 'Cache-Control': 'no-cache', ...getHeaders() },
        url: this.url.value + '/layout/scheduler/samplecollect/upload',
        data: form,
        responseType: 'json',
        processData: false,
        contentType: false
      }

      const urls = await axios(axios_config_upload).then(res => {
        const url = res.data.data.url
        if (url === undefined) {
          return Promise.reject('无效下载地址')
        }
        console.log('图片下载地址: ', url)
        return Promise.resolve([url])
      }).catch(error => {
        this.$message({
          showClose: true,
          message: '文件处理失败! ' + error,
          center: true,
          duration: 4000,
          type: 'error'
        })
        return Promise.resolve()
      })
      if (urls === undefined) {
        return
      }

      const status = await this.tableextract(urls)
      // 结束加载
      this.loading = false
      const e_time = new Date().getTime()
      this.statistic = {
        status,
        duration: this.result.duration || (e_time - s_time) / 1000
      }
      return Promise.resolve()
    },
    // 相应的请求体设置
    tableextract(urls) {
      const imgs = []
      var zipurl = ''
      var pdfurl = ''

      urls.forEach((url, index) => {
        this.$set(this.originalImages, index, url.replace('zhai-platereduction.ks3-cn-beijing-internal.ksyun.com', 'zhai-platereduction.ks3-cn-beijing.ksyun.com'))
        if (url.indexOf('.pdf') > 0) {
          pdfurl = url
        } else if (url.indexOf('.zip') > 0) {
          zipurl = url
        } else {
          // imgs.push({
          //   index,
          //   url,
          //   'dpi': 96,
          //   'is_trim': false,
          //   'origin_height': 10,
          //   'origin_width': 10
          // })
          imgs.push({
            index,
            url,
            'dpi': this.dpi.value,
            'origin_height': this.originHeight,
            'origin_width': this.originWidth
          })
        }
      })
      // const data = {
      //   imgs
      // }
      var data_dict = {}
      if (this.showNewVersion) {
        data_dict = {
          'imgs': imgs,
          'zip_url': zipurl,
          'pdf_url': pdfurl,
          'export_type': this.startPage.exporttype,
          'sheet_option': this.startPage.splitsheet === '一个表格一个sheet' ? 0 : this.startPage.splitsheet,
          'page_text': this.startPage.pagetext === '是',
          'page_text_option': this.startPage.pagetextoption === '所有文本一个单元格' ? 0 : this.startPage.pagetextoption
          // "sheet_option": this.startPage.splitsheet,
          // "page_text": this.startPage.page_text,
        }
      } else {
        data_dict = {
          'imgs': imgs,
          'zip_url': zipurl,
          'pdf_url': pdfurl
        }
      }

      return axios({
        method: 'post',
        headers: { 'Cache-Control': 'no-cache', ...getHeaders() },
        url: this.url.value + '/layout/scheduler/tableextract/create',
        // url: '/cvhost/layout/scheduler/tableextract/create',
        data: JSON.stringify(data_dict),
        responseType: 'json',
        processData: false,
        contentType: false
      }).then(async(res) => {
        res = res.data

        if (res.code !== 200) {
          return Promise.reject('转化请求失败!')
        }
        // 等待1s
        await sleep(1000)

        const docID = res.data.docID
        return axios_wait_doc({
          method: 'get',
          headers: { 'Cache-Control': 'no-cache', ...getHeaders() },
          url: this.url.value + '/layout/scheduler/tableextract/query?docID=' + docID
          // url: '/cvhost/layout/scheduler/tableextract/query?docID=' + docID
        })
      }).then((res) => {
        if (res === undefined) {
          console.log('undefined res')
          return Promise.resolve(-1)
        }
        // 处理完成
        console.log('文件处理完成: ' + JSON.stringify(res))

        const { data } = res
        if (data.pages_err !== undefined && data.pages_err['0'] !== undefined) {
          this.result = {
            duration: 0,
            zipUrl: '',
            errMsgs: data.pages_err['0'] }
          return Promise.reject('详情查看错误信息')
        } else {
          this.result = {
            duration: data.duration / 1000,
            zipUrl,
            errMsgs: [] }
        }
        const zipUrl = data.dst_url
        if (zipUrl === undefined) {
          return Promise.reject('无压缩包下载地址')
        }
        if (zipUrl.lastIndexOf('.xlsx') > 0) {
          return this.fetchXlsx(zipUrl)
        } else {
          return this.fetchAndUnzip(zipUrl)
        }
      }).then(htmlsInZip => {
        console.log('htmls(zip):', htmlsInZip)
        this.htmlsInZip = htmlsInZip
        return Promise.resolve(1)
      }).catch((error) => {
        this.$message({
          showClose: true,
          message: '文件处理失败! ' + error,
          center: true,
          duration: 4000,
          type: 'error'
        })
        return Promise.resolve(0)
      })
    }
  }
}
</script>

<style lang="scss">
// scoped style donot apply to v-html
// choose global style or deep selector
.extracted-table-container{
  overflow-x: auto;
  width: 100%;
  padding-top: 2%;
  padding-bottom: 2%;
  border-bottom: 1px solid #d9d9d9;
  table{
    // colspan-and-table-layoutfixed-break-the-table-style
    table-layout: auto;
    width: fit-content;
    margin: auto;
  }
}
</style>
<style lang="scss" scoped>
@import "~@/styles/mixin.scss";

/* 对话框底部按钮区域 */
.dialog-footer {
  display: flex;
  justify-content: space-between;
}
.reset-button {
  order: 1;
}
.right-buttons {
  order: 2;
}
.container {
  padding-bottom: 2%;
  padding-left: 8%;
  padding-right: 8%;
}
.el-row {
  margin-bottom: 20px;
  &:last-child {
    margin-bottom: 0;
  }
}
.el-col {
  border-radius: 8px;
  border: 1px dashed #d9d9d9;
  background-color: #ffffff;
  color: #666666;
  padding: 1%;
}
.grid-content {
  border-radius: 8px;
  vertical-align: middle;
}

.upload-container {
  width: 100%;
  padding: 1%;
  position: relative;
  @include clearfix;
  .params-container {
    width: 50%;
    display: inline-block;
    padding-left: 1%;
    padding-right: 1%;
  }
  .params {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }
  .param-label {
    width: 50%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-bottom: 2%;
  }
  .param-input {
    width: 50%;
    text-align: right;
    margin-bottom: 2%;
  }
}

.download-doc-btn{
    text-align: center;
    width: 100%;
    height: 4vh;
    font-size: 2vh;
    line-height: 4vh;
    color: #fff;
    background-color: #338ef0;
    border: none;
    border-radius: 4px;
}

.download-doc-btn a{
    text-decoration: none;
    color: #fff;
}
</style>
