import axios from 'axios'
import { getToken } from '@/utils/auth'
import store from '@/store'
import { Message } from 'element-ui'
export const axios_cv_dw = axios.create({
  baseURL: process.env.VUE_APP_BASE_API
})

// http request 拦截器
axios_cv_dw.interceptors.request.use(
  config => {
    // do something before request is sent
    if (store.getters.token) {
      // let each request carry token
      // ['X-Token'] is a custom headers key
      // please modify it according to the actual situation
      config.headers['Authorization'] = 'Bearer ' + getToken()
      config.headers['Content-Type'] = 'application/json'
    }
    return config
  },
  err => {
    return Promise.reject(err)
  }
)

export function DownLoadErrImgs(url) {
  const axios_config_download = {
    method: 'get',
    headers: { 'Cache-Control': 'no-cache' },
    // url:'/api/v1/pdf-edit-request/download/3/6418a0c5-3368-419c-86aa-5d6399e034bb',
    url: '/api/v1/' + url,
    // data: formData,
    responseType: 'blob',
    processData: false, // 必须
    contentType: false
  }
  axios_cv_dw(axios_config_download).then((res) => {
    const blobtype = res.headers['content-type']
    if (blobtype.lastIndexOf('json') <= 0) {
      const downname = NewGuid() + '.zip'
      const url = window.URL.createObjectURL(new Blob([res.data]))
      let link = document.createElement('a')
      link.style.display = 'none'
      link.href = url
      link.setAttribute('download', downname)
      document.body.appendChild(link)
      // console.log(res)
      link.click()
      link = null
    } else {
      Message({
        message: '没有数据',
        type: 'error',
        duration: 5 * 1000
      })
    }
  }).catch(err => {
    console.log(err)
  })
}

export function downLoadFile(query, download, filetype, docid, starttime, suffix) {
  const axios_config_download = {
    method: 'get',
    headers: { 'Cache-Control': 'no-cache' },
    // url:'/api/v1/pdf-edit-request/download/3/6418a0c5-3368-419c-86aa-5d6399e034bb',
    url: '/api/v1/request/' + download + '/' + filetype + '/' + docid + starttime,
    // data: formData,
    params: query,
    responseType: 'blob',
    processData: false, // 必须
    contentType: false
  }
  var pre = ''
  axios_cv_dw(axios_config_download).then((res) => {
    const blobtype = res.headers['content-type']

    if (blobtype.lastIndexOf('text/plain') >= 0) {
      var reader = new FileReader()
      reader.readAsText(res.data, 'utf-8')
      reader.onload = function(e) {
        Message({
          message: reader.result,
          type: 'error',
          duration: 5 * 1000
        })
      }
      return
    }

    if (blobtype.lastIndexOf('document') > 0) {
      pre = 'docx'
    } else if (blobtype.lastIndexOf('zip') > 0) {
      pre = 'zip'
    } else if (blobtype.lastIndexOf('pdf') > 0) {
      pre = 'pdf'
    } else if (blobtype.lastIndexOf('png') > 0) {
      pre = 'png'
    } else if (blobtype.lastIndexOf('jpeg') > 0) {
      pre = 'jpeg'
    } else if (blobtype.lastIndexOf('json') > 0) {
      pre = 'xml'
    } else {
      pre = suffix
    }
    if (suffix === 'xlsx' || suffix === 'json' || suffix === 'pptx') {
      pre = suffix
    }

    const downname = NewGuid() + '.' + pre

    if (blobtype.lastIndexOf('json') <= 0 || pre === 'xml' || pre === 'json') {
      const url = window.URL.createObjectURL(new Blob([res.data]))
      let link = document.createElement('a')
      link.style.display = 'none'
      link.href = url
      link.setAttribute('download', downname)
      document.body.appendChild(link)
      // console.log(res)
      link.click()
      link = null
    } else {
      Message({
        message: '下载失败',
        type: 'error',
        duration: 5 * 1000
      })
    }
  })
}

// 获取链接
export function getCopyLink(query, filetype, docid, starttime) {
  return new Promise((resolve, reject) => {
    const axios_config_copylink = {
      url: '/api/v1/request/copy/' + filetype + '/' + docid + starttime,
      method: 'get',
      headers: { 'Cache-Control': 'no-cache' },
      params: query
    }
    axios_cv_dw(axios_config_copylink).then((res) => {
      resolve(res.data)
    }).catch(error => {
      reject(error)
    })
  })
}

function S4() {
  return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1)
}
function NewGuid() {
  return (S4() + S4() + '-' + S4() + '-' + S4() + '-' + S4() + '-' + S4() + S4() + S4())
}

