<template>
  <div>
    <CVHint>PDF或图片转WORD：选择一个PDF或图片文件，系统会对文件进行处理，点击“下载结果”获取处理的效果</CVHint>
    <div class="container">
      <el-row type="flex" justify="center" :gutter="20">
        <el-col :span="8" class="grid-content">
          <div class="upload-container">
            <div class="params-container" style="width: 100% !important">
              <div class="params">
                <span class="param-label" style="width: 25% !important">url :
                  <el-tooltip
                    effect="dark"
                    content="默认使用网站后端提供的abbyy服务，如需使用其他abbyy服务请输入url，如：//pcv-test.wps.cn"
                    placement="top"
                  >
                    <svg-icon icon-class="question" style="margin-right:10px; float: right;" />
                  </el-tooltip>
                </span>
                <el-input
                  v-model="url.value"
                  placeholder="默认使用网站后端abbyy服务"
                  class="param-input"
                  style="width: 75% !important"
                  type="text"
                  @change="urlChange"
                />
              </div>
              <div class="params">
                <span
                  class="param-label"
                  style="width: 25% !important"
                >导出类型 :</span>
                <el-select
                  v-model="exportType"
                  class="param-input"
                  style="width: 75% !important"
                  placeholder=""
                  size="small"
                >
                  <el-option
                    v-for="dict in exporttypeOptions"
                    :key="dict"
                    :value="dict"
                    :label="dict"
                  />
                </el-select>
              </div>
            </div>
            <div class="params-container">
              <!-- <div class="params"> -->
              <div v-show="fileName !== ''" class="params">
                <span class="param-label">{{ fileName }} :</span>
                <span class="param-input">
                  {{ fileSize }}
                </span>
              </div>
              <!-- <div class="params"> -->
              <div v-show="statistic.status !== -1" class="params">
                <span class="param-label">处理用时 :</span>
                <span class="param-input">
                  {{ formattedDuration }}
                </span>
              </div>
              <div v-show="statistic.status !== -1" class="params">
                <span v-show="statistic.status === 1" class="param-label" style="color: #00ff33;">转化成功!</span>
                <span v-show="statistic.status === 0" class="param-label" style="color: #ff0000;">转化失败!</span>
              </div>
            </div>

            <!-- accept=".png,.jpg,.pdf" -->
            <el-upload
              v-loading="loading"
              element-loading-text="文件处理中"
              class="uploader"
              list-type="text"
              :multiple="false"
              :accept="fileType.join(',')"
              :show-file-list="false"
              :file-list="fileList"
              :http-request="pdf2wordUpload"
              action="placeholder"
              drag
            >
              <i class="el-icon-upload" />
              <div class="el-upload__text">
                将文件拖到此处，或<em>点击上传</em>
              </div>
            </el-upload>
          </div>

          <!-- <a target="_blank" download="" :href="result.wordUrl"> -->
          <a
            v-show="result.wordUrl!==''"
            target="_blank"
            download=""
            :href="result.wordUrl"
          >
            <div class="download-doc-btn">下载结果</div>
          </a>
          <ul />
        </el-col>
      </el-row>
    </div>
  </div>

</template>

<script>
import CVHint from './components/CVHint'

import { urlRegex, formatfileSize, isValidFile } from '@/api/cv/utils'
import { axios_get_docID, axios_wait_doc } from '@/api/cv/request-abbyy'

export default {
  name: 'Abbyy',
  components: { CVHint },
  props: {
    maxFileNum: {
      validator(value) {
        if (value <= 0) {
          return 1
        }
        return value
      },
      default: 1
    },
    fileType: {
      type: Array,
      default: function() {
        return ['.pdf', '.png', '.jpg', '.jpeg']
      }
    }
  },
  data() {
    return {
      loading: false,
      url: {
        value: '', // //pcv-test.wps.cn
        default: '' // //pcv-test.wps.cn
      },
      fileObj: {
        name: '',
        size: 0
      },
      fileList: [],
      result: {
        wordUrl: ''
      },
      statistic: {
        duration: 0,
        status: -1
      },
      exporttypeOptions: ['docx', 'xlsx', 'pptx'],
      exportType: 'docx'
    }
  },
  computed: {
    formattedDuration() {
      return this.statistic.duration.toFixed(3) + 's'
    },
    fileName() {
      const { name } = this.fileObj
      return name.trim()
    },
    fileSize() {
      const { size } = this.fileObj
      return formatfileSize(size)
    }
  },
  methods: {
    urlChange(value) {
      // url 检测
      if (urlRegex.test(value)) {
        this.url.value = value
      } else {
        this.url.value = this.url.default
      }
    },
    fileChange(file, fileList) {
      this.fileList = fileList.slice(-this.maxFileNum)
    },
    onSuccess(response, file, fileList) {
      // hook
      this.fileChange(file, fileList)
    },
    beforeUpload(file) {
      // 清理旧结果
      this.fileObj = { name: '', size: 0 }
      this.result = {
        wordUrl: '' }
      this.statistic = {
        status: -1,
        duration: 0
      }

      // 文件类型判断
      const { name } = file
      var ext = '.' + name.trim().split('.').pop()
      ext = ext.toLowerCase()
      if (!isValidFile(ext, this.fileType)) {
        this.$message({
          showClose: true,
          message: '请选择pdf/png/jpg/jpeg文件!',
          center: true,
          duration: 4000,
          type: 'error'
        })
        return false
      } else {
        this.fileObj = file
        return true
      }
    },
    async pdf2wordUpload(param) {
      // 开始加载
      this.loading = true

      const { file } = param
      if (!this.beforeUpload(file)) {
        // 结束加载
        this.loading = false
        return Promise.resolve()
      }

      const form = new FormData()
      form.append('file', file)
      form.append('pdf2docengine', 'ABBYY')
      let url = this.url.value + '/layout/abbyy/pdfconvert/' + this.exportType
      if (this.url.value === '') {
        url = process.env.VUE_APP_BASE_API + 'layout/abbyy/pdfconvert/' + this.exportType
      }
      const axios_config_get_docID = {
        method: 'post',
        headers: { 'Cache-Control': 'no-cache' },
        url,
        data: form,
        responseType: 'text',
        processData: false, // 必须
        contentType: false
      }

      const s_time = new Date().getTime()

      const status = await this.httpRequest(axios_config_get_docID)
      if (status === 1) {
        param.onSuccess()
      }

      // 结束加载
      this.loading = false
      const e_time = new Date().getTime()
      this.statistic = {
        status,
        duration: (e_time - s_time) / 1000
      }
      return Promise.resolve()
    },
    httpRequest(axios_config) {
      return axios_get_docID(axios_config)
        .then((res) => {
          let url = this.url.value + '/layout/abbyy/pdfconvert/' + res // docID
          if (this.url.value === '') {
            url = process.env.VUE_APP_BASE_API + 'layout/abbyy/pdfconvert/' + res
          }
          return axios_wait_doc({
            method: 'get',
            headers: { 'Cache-Control': 'no-cache' },
            url,
            processData: false
          })
        })
        .then((res) => {
          if (res === undefined) {
            console.log('undefined res')
            return Promise.resolve(-1)
          }
          // 处理完成
          console.log('文件处理完成')
          this.result.wordUrl = res.replace('http://', 'https://')
          return Promise.resolve(1)
        })
        .catch((error) => {
          this.$message({
            showClose: true,
            message: '文件处理失败! ' + error,
            center: true,
            duration: 4000,
            type: 'error'
          })
          return Promise.resolve(0)
        })
    },
    downloadFile() {
      console.log(this.result.wordUrl)
      // downloadFileType(this.result.wordUrl, this.exportType)
    }
  }
}

</script>

<style lang="scss" scoped>
@import "~@/styles/mixin.scss";
.container {
  padding-bottom: 2%;
  padding-left: 8%;
  padding-right: 8%;
}
.el-row {
  margin-bottom: 20px;
  &:last-child {
    margin-bottom: 0;
  }
}
.el-col {
  border-radius: 8px;
  border: 1px dashed #d9d9d9;
  background-color: #ffffff;
  color: #666666;
  padding: 1%;
}
.grid-content {
  border-radius: 8px;
  vertical-align: middle;
}

.upload-container {
  width: 100%;
  padding: 1%;
  position: relative;
  @include clearfix;
  .params-container {
    width: 50%;
    display: inline-block;
    padding-left: 1%;
    padding-right: 1%;
  }
  .params {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }
  .param-label {
    width: 50%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-bottom: 2%;
  }
  .param-input {
    width: 50%;
    text-align: right;
    margin-bottom: 2%;
  }
}

.download-doc-btn{
    text-align: center;
    width: 100%;
    height: 4vh;
    font-size: 2vh;
    line-height: 4vh;
    color: #fff;
    background-color: #338ef0;
    border: none;
    border-radius: 4px;
}

.download-doc-btn a{
    text-decoration: none;
    color: #fff;
}
</style>
