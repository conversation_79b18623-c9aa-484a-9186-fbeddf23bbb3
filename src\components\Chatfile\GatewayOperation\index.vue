<template>
  <div>
    <el-button
      type="text"
      size="mini"
      @click="ShowInputData(inputData)"
    >查看输入 |</el-button>
    <el-button
      type="text"
      size="mini"
      @click="ShowOnputData(outputData)"
    >查看输出 |</el-button>
    <el-button
      type="text"
      size="mini"
      @click="JumpData"
    >跳转handle数据 |</el-button>
    <el-dialog :visible.sync="input_open" width="800px">
      <div>
        <button class="button-spacing" @click="CopyData(origin_input_data)">复制</button>
        <button class="button-spacing" @click="SaveData(origin_input_data)">保存到本地</button>
        <pre class="json-output">{{ origin_input_data }}</pre>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="output_open" width="800px">
      <div>
        <button class="button-spacing" @click="CopyData(origin_output_data)">复制</button>
        <button class="button-spacing" @click="SaveData(origin_input_data)">保存到本地</button>
        <pre class="json-output">{{ origin_output_data }}</pre>
      </div>
    </el-dialog>
  </div>
</template>

<style>
.button-spacing {
  margin-right: 10px;
}
</style>

<style>
.json-output {
  text-align: left;
  white-space: pre-wrap;
}
</style>

<script>
import { string } from 'clipboard'
import { decode_and_uncompress } from '@/api/chatfile/utils'
export default {
  name: 'GatewayOperation',
  props: {
    inputData: {
      type: string,
      default: ''
    },
    outputData: {
      type: string,
      default: ''
    },
    clientRequestId: {
      type: string,
      default: ''
    },
    wpsUid: {
      type: Number,
      default: 0
    },
    sessionId: {
      type: string,
      default: ''
    },
    chatId: {
      type: string,
      default: ''
    },
    beginT: {
      type: string,
      default: ''
    }
  },
  data() {
    return {
      input_open: false,
      output_open: false,
      origin_input_data: '',
      origin_output_data: ''
    }
  },
  methods: {
    ShowOnputData(outputData) {
      this.origin_output_data = decode_and_uncompress(outputData)
      this.output_open = true
    },
    ShowInputData(inputData) {
      this.origin_input_data = decode_and_uncompress(inputData)
      this.input_open = true
    },
    CopyData(data) {
      const textarea = document.createElement('textarea')
      textarea.value = data
      document.body.appendChild(textarea)
      textarea.select()
      document.execCommand('copy')
      document.body.removeChild(textarea)
      this.$message.success('成功复制到剪贴板')
    },
    SaveData(data) {
      const blob = new Blob([data], { type: 'text/plain' })
      const link = document.createElement('a')
      link.download = 'gateway_data.txt'
      link.href = URL.createObjectURL(blob)
      link.click()
    },
    JumpData() {
      this.$router.push({ path: '/chatfile/handle-request', query: { client_request_id: this.clientRequestId, user_id: this.wpsUid, session_id: this.sessionId, chat_id: this.chatId, begin_t: this.beginT }})
    }
  }
}
</script>
