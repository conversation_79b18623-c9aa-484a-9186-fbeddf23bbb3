
<template>
  <BasicLayout>
    <template #wrapper>
      <el-card class="box-card">
        <el-form ref="queryForm" :model="queryParams" :inline="true" label-width="65px">
          <el-form-item label="日期">
            <el-date-picker
              v-model="queryParams.begin_t"
              size="small"
              type="date"
              align="right"
              value-format="yyyyMMdd"
            /></el-form-item>
          <el-form-item label="client_request_id" label-width="110px">
            <el-input
              v-model="queryParams.client_request_id"
              placeholder="请输入client_request_id"
              clearable
              size="small"
            />
          </el-form-item>
          <el-form-item label="errno">
            <el-input
              v-model="queryParams.errno"
              placeholder="请输入errno"
              clearable
              size="small"
            />
          </el-form-item>
          <el-form-item label="wps_uid">
            <el-input
              v-model="queryParams.wps_uid"
              placeholder="请输入wps_uid"
              clearable
              size="small"
            />
          </el-form-item>
          <el-form-item label="session_id" label-width="70px">
            <el-input
              v-model="queryParams.session_id"
              placeholder="请输入session_id"
              clearable
              size="small"
            />
          </el-form-item>
          <el-form-item label="chat_id">
            <el-input
              v-model="queryParams.chat_id"
              placeholder="请输入chat_id"
              clearable
              size="small"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-table v-loading="loading" :data="gatewayRequestList" span="1.36">
          <el-table-column v-if="false" type="selection" width="5" align="center" />
          <el-table-column
            label="日期"
            align="center"
            prop="begin_t"
            width="250"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="client_request_id"
            align="center"
            prop="client_request_id"
            width="250"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="errno"
            align="center"
            prop="errno"
            width="100"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="wps_uid"
            align="center"
            prop="wps_uid"
            width="100"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="session_id"
            align="center"
            prop="session_id"
            width="250"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="chat_id"
            align="center"
            prop="chat_id"
            width="250"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="mode"
            align="center"
            prop="mode"
            width="100"
          /><el-table-column
            label="耗时(s)"
            align="center"
            prop="request_time"
            width="250"
            sortable
            :show-overflow-tooltip="true"
          />
          <el-table-column label="操作" width="300" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <GatewayOperation
                :client-request-id="scope.row.client_request_id"
                :wps-uid="scope.row.wps_uid"
                :session-id="scope.row.session_id"
                :chat-id="scope.row.chat_id"
                :input-data="scope.row.input_data"
                :output-data="scope.row.output_data"
                :begin-t="formatDate(scope.row.begin_t)"
              />
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageIndex"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>

    </template>
  </BasicLayout>
</template>

<script>
import { getGatewayRequestList } from '@/api/chatfile/gateway-request'
import { format_current_data_YYYYMMDD } from '@/api/chatfile/utils'
import GatewayOperation from '@/components/Chatfile/GatewayOperation/index.vue'

export default {
  name: 'GatewayRequest',
  components: {
    GatewayOperation
  },
  data() {
    return {
      // 总条数
      total: 0,
      // 弹出层标题
      title: '',
      open: false,
      loading: false,
      // 类型数据字典
      typeOptions: [],
      routerTypeOptions: [],
      gatewayRequestList: [],
      // 查询参数
      queryParams: {
        pageIndex: 1,
        pageSize: 10,
        client_request_id: undefined,
        wps_uid: undefined,
        begin_t: format_current_data_YYYYMMDD(),
        errno: undefined,
        session_id: undefined,
        chat_id: undefined,
        mode: undefined
      },
      // 表单参数
      form: {
      },
      // 表单校验
      rules: { begin_t: [{ required: true, message: '日期不能为空', trigger: 'blur' }]
      }
    }
  },
  created() {
    if (this.$route.query !== undefined) {
      this.queryParams.client_request_id = this.$route.query.client_request_id
      this.queryParams.session_id = this.$route.query.session_id
      this.queryParams.chat_id = this.$route.query.chat_id
      if (this.$route.query.wps_uid > 0) {
        this.queryParams.wps_uid = this.$route.query.wps_uid
      }
      if (this.$route.query.begin_t !== undefined) {
        this.queryParams.begin_t = this.$route.query.begin_t
      }
    }
    this.getList()
  },
  methods: {
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageIndex = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.client_request_id = undefined
      this.queryParams.wps_uid = undefined
      this.queryParams.begin_t = format_current_data_YYYYMMDD()
      this.queryParams.errno = undefined
      this.queryParams.session_id = undefined
      this.queryParams.chat_id = undefined
      this.queryParams.mode = undefined
      this.resetForm('queryForm')
      this.handleQuery()
    },
    formatDate(data) {
      if (data == null) {
        return ''
      }
      const datastr = data.split('T')[0]
      return datastr.replaceAll('-', '')
    },
    getList() {
      this.loading = true
      getGatewayRequestList(this.addDateRange(this.queryParams)).then(response => {
        this.gatewayRequestList = response.data.list
        this.total = response.data.count
        this.loading = false
      }, _ => {
        this.gatewayRequestList = []
        this.total = 0
        this.loading = false
      }
      )
    }
  }
}
</script>
