<template>
  <BasicLayout>
    <template #wrapper>
      <el-card class="box-card">
        <el-form :inline="true" label-position="left">
          <el-tooltip
            effect="dark"
            placement="top"
            content="ks3 prefix"
          ><label class="el-form-item__label" style="padding: 0px; line-height: 36px;"><svg-icon icon-class="question" /></label></el-tooltip>
          <el-form-item label="Prefix">
            <el-select v-model="params.prefix" placeholder="请刷新和检查有无数据">
              <el-option
                v-for="prefix in params.prefixes"
                :key="prefix"
                :label="prefix"
                :value="prefix"
              />
            </el-select>
          </el-form-item>

          <el-tooltip
            effect="dark"
            placement="top"
            content="为空表示不使用<RequestId>, 来过滤性能分析记录, 支持模糊搜索"
          ><label class="el-form-item__label" style="padding: 0px; line-height: 36px;"><svg-icon icon-class="question" /></label></el-tooltip>
          <el-form-item label="RequestId">
            <el-input
              v-model="params.rootLabels[0]"
              type="text"
              size="small"
              style="width: 240px"
              placeholder="请输入RequestId"
              clearable
            />
          </el-form-item>

          <el-tooltip
            effect="dark"
            placement="top"
            content="限制性能分析记录的查询数量"
          ><label class="el-form-item__label" style="padding: 0px; line-height: 36px;"><svg-icon icon-class="question" /></label></el-tooltip>
          <el-form-item label="限制数">
            <el-input
              v-model="params.count.value"
              type="number"
              :min="params.count.min"
              :max="params.count.max"
              size="small"
              style="width: 160px"
              clearable
              @change="paramChangeCount"
            />
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="small"
              :disabled="params.prefix.length===0"
              @click="listTraces"
            >获取记录</el-button>
          </el-form-item>
        </el-form>

        <el-tree
          :data="traces"
          :props="props"
          node-key="nodeId"
          indent="24"
          @node-expand="onNodeExpand"
        >
          <span slot-scope="{ node, data }" class="custom-tree-node">
            <span>{{ node.label }}</span>
            <span v-if="node.level === 1"> <!-- root -->
              <el-button-group>
                <el-tooltip
                  effect="dark"
                  placement="top"
                  content="刷新"
                >
                  <el-button
                    type="primary"
                    plain
                    size="small"
                    circle
                    icon="el-icon-refresh"
                    :loading="data.loading"
                    @click.stop="() => getTrace(data)"
                  />
                </el-tooltip>
                <el-tooltip
                  effect="dark"
                  placement="top"
                  content="过滤"
                >
                  <el-button
                    type="info"
                    plain
                    size="small"
                    circle
                    icon="el-icon-delete"
                    @click.stop="() => filterTrace(node, data)"
                  />
                </el-tooltip>
              </el-button-group>
            </span>
            <span v-else-if="data.nodeLabel !== ''"> <!-- not root && not empty label -->
              <el-button
                type="text"
                disabled
                :v-if="data.podname.length > 0"
              >
                {{ data.podname }}
              </el-button>
              <el-button
                type="text"
                disabled
              >
                {{ (data.duration/1000).toFixed(2) }}s
              </el-button>
              <el-button
                type="text"
                size="small"
                @click.stop="() => getResult(node, data, 1)"
              >
                火焰图
              </el-button>
              <el-button
                type="text"
                size="small"
                @click.stop="() => getResult(node, data, 2)"
              >
                行耗时
              </el-button>
              <el-button
                type="text"
                size="small"
                @click.stop="() => getResult(node, data, 3)"
              >
                行内存
              </el-button>
            </span>
          </span>
        </el-tree>
      </el-card>
    </template>
  </BasicLayout>
</template>
<script>
import { listPrefixes, listTraces, getTrace, getResult } from '@/api/layout/profiling'

export default {
  name: 'ProfilingTraces',
  data() {
    return {
      params: {
        prefix: '',
        prefixes: [],
        rootLabels: [''],
        count: {
          value: 20,
          default: 20,
          min: 1,
          max: 100
        }
      },
      traces: null,
      props: {
        label: 'nodeLabel',
        children: 'children'
      }
    }
  },
  created() {
    listPrefixes()
      .then(({ data: result }) => {
        if (result.length <= 0) {
          return Promise.reject('无效前缀参数列表')
        }
        this.params.prefixes = result // []
        this.params.prefix = result[0]
      })
      .catch((error) => {
        this.$message({
          showClose: true,
          message: '获取参数失败! ' + error,
          duration: 4000,
          type: 'error'
        })
      })
  },
  methods: {
    numberInputChange(obj, value) {
      if (value === '') {
        obj.value = obj.default
      } else if (value > obj.max) {
        obj.value = obj.max
      } else if (value < obj.min) {
        obj.value = obj.min
      }
    },
    paramChangeCount(value) {
      this.numberInputChange(this.params.count, value)
    },
    async onNodeExpand(data, node) {
      if (node.level !== 1) {
        return
      }
      if (data.children === null || data.children.length !== 1 || data.children[0].nodeLabel !== '') {
        // 非第一次展开
        return
      }

      await this.getTrace(data)
    },
    async getTrace(data) {
      if (data.loading) {
        return
      }
      data.loading = true
      await getTrace(data.nodeId, {
        prefix: this.params.prefix
      })
        .then(({ data: result }) => {
          data.children = result.children
        })
        .catch((error) => {
          this.$message({
            showClose: true,
            message: '获取结果失败! ' + error,
            duration: 4000,
            type: 'error'
          })
        })
      data.loading = false
    },
    filterTrace(node, { nodeId }) {
      const children = node.parent.data
      const index = children.findIndex(data => data.nodeId === nodeId)
      children.splice(index, 1)
    },
    listTraces() {
      listTraces({
        prefix: this.params.prefix,
        rootLabels: this.params.rootLabels.filter(label => label && label.trim()),
        count: this.params.count.value
      })
        .then(({ data: result }) => {
          result.forEach((trace) => {
            trace.loading = false // 显示加载状态
            trace.children = [{ // 用于展示下拉符号
              nodeLabel: ''
            }]
          })
          this.traces = result
        })
        .catch((error) => {
          this.$message({
            showClose: true,
            message: '获取结果失败! ' + error,
            duration: 4000,
            type: 'error'
          })
        })
    },
    getResult(node, { nodeId }, type) {
      let root = node.parent // = node
      while (root.parent.parent !== null) {
        root = root.parent
      }
      getResult({
        prefix: this.params.prefix,
        rootId: root.data.nodeId,
        nodeId,
        type
      })
        .then(({ blob, filename }) => {
          // blob = new Blob([blob], { type: 'application/force-download' })
          blob = new Blob([blob], { type: blob.type + ';charset=utf-8' })
          const url = window.URL.createObjectURL(blob)
          window.open(url + '#' + filename, '_blank')
          window.URL.revokeObjectURL(url)
        })
        .catch((error) => {
          this.$message({
            showClose: true,
            message: '获取结果失败! ' + error,
            duration: 4000,
            type: 'error'
          })
        })
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/styles/mixin.scss";

// .container {
//   padding-bottom: 2%;
//   padding-left: 8%;
//   padding-right: 8%;
// }
// .el-row {
//   margin-bottom: 20px;
//   &:last-child {
//     margin-bottom: 0;
//   }
// }
// .el-col {
//   border-radius: 8px;
//   border: 1px dashed #d9d9d9;
//   background-color: #ffffff;
//   color: #666666;
//   padding: 1%;
// }
  // .params {
  //   display: flex;
  //   align-items: center;
  //   justify-content: space-between;
  //   width: 40%;
  // }
  // .param-label {
  //   width: 80px;
  //   overflow: hidden;
  //   text-overflow: ellipsis;
  //   white-space: nowrap;
  //   margin-bottom: 2%;
  //   color: #606266;
  //   font-size: 14px;
  // }
  // .param-input {
  //   text-align: right;
  //   margin-bottom: 2%;
  // }
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 16px;
  padding-right: 8px;
  line-height: 30px;
}
// .custom-tree-node .el-button--medium.is-round {
//   padding: 2px 10px;
// }
::v-deep .el-tree>div {
  margin-top: 10px;
  margin-bottom: 10px;
  border-bottom: 2px solid #dfe4ed;
}
::v-deep .el-tree-node__content {
  height: 30px;
}

</style>
