<template>
  <el-dialog :title="title" width="1200px" :visible="open" @close="closeDialog">
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">
      <el-form-item label="任务名" prop="name">
        <el-input v-model="form.name" placeholder="任务" />
      </el-form-item>
      <el-form-item label="服务" prop="service">
        <el-select v-model="form.service" placeholder="服务" clearable size="small" :disabled="useTemplate" @change="handleServiceChange">
          <el-option v-for="item in serviceOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-row v-if="mode === 'task'">
        <el-col :span="6">
          <el-form-item label="模板" prop="template_id" inline>
            <el-select v-model="form.template_id" placeholder="请先选择服务" clearable filterable size="small" @change="showTemplateData(form.template_id)">
              <template v-for="item in templateOptions">
                <el-tooltip v-if="item.remark" :key="item.id" :content="item.remark" placement="top">
                  <el-option :label="item.name" :value="item.id" />
                </el-tooltip>
                <el-option v-else :key="item.id" :label="item.name" :value="item.id" />
              </template>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="18">
          <el-form-item label="使用模板" inline>
            <el-switch v-model="useTemplate" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="host" prop="host">
        <el-select v-model="form.host" placeholder="host" clearable filterable allow-create size="small" :disabled="useTemplate">
          <template v-for="(item, index) in hostOptions">
            <el-tooltip v-if="item.remark" :key="index" :content="item.remark" placement="top">
              <el-option :label="item.label" :value="item.value" />
            </el-tooltip>
            <el-option v-else :key="index" :label="item.label" :value="item.value" />
          </template>
        </el-select>
        <!--<el-input v-model="form.host" placeholder="host" />-->
      </el-form-item>
      <el-form-item label="接口" prop="uri">
        <el-select v-model="form.uri" placeholder="接口" clearable filterable allow-create size="small" :disabled="useTemplate">
          <template v-for="(item, index) in uriOptions">
            <el-tooltip :key="index" :content="`${item.value.uri},${item.remark}`" placement="top">
              <el-option :label="item.label" :value="item.value.uri" />
            </el-tooltip>
          </template>
        </el-select>
        <!--<el-input v-model="form.uri" placeholder="接口" :disabled="useTemplate" />-->
      </el-form-item>
      <el-form-item v-if="mode === 'task'" label="执行时间" prop="exec_time">
        <el-date-picker
          v-model="form.exec_time"
          size="small"
          type="datetime"
          align="right"
          value-format="yyyy-MM-dd HH:mm:ss"
        />
      </el-form-item>
      <div v-if="!IsGenerateFont">
        <el-form-item label="参数配置">
          <el-switch v-model="useEasy" active-text="简单" inactive-text="高级" />
        </el-form-item>
        <div>
          <div v-show="useEasy">
            <div v-show="sampleFormStruct.length > 0 || paramOptions.length > 0">
              <SampleForm ref="sampleForm" :dynamic="easyDynamicForm" :sample-form-options="sampleFormOptions" :struct="sampleFormStruct" :editable="!useTemplate" />
              <el-form-item label="参数" prop="body.statsic">
                <EasyForm ref="easyForm" :data="easyStaticForm" :struct="paramOptions" :editable="!useTemplate" />
              </el-form-item>
            </div>
            <div v-show="sampleFormStruct.length=== 0 && paramOptions.length=== 0" style="margin-left: 120px; margin-bottom: 36px;">
              <span v-if="!form.uri">简单模式请先选择接口</span>
              <span v-else>该接口不支持简单模式</span>
            </div>
          </div>
          <div v-show="!useEasy">
            <el-form-item
              label="静态字段"
              prop="body.static"
            >
              <JsonEdit
                ref="jsonEditStatic"
                v-model="hardStaticForm"
                :editable="!useTemplate"
              />
            </el-form-item>
            <el-form-item label="动态字段" prop="body.dynamic" label-position="top">
              <el-tabs v-model="activeView" type="border-card" size="small">
                <el-tab-pane label="表单" name="form">
                  <NestedFieldTb
                    :data="hardDynamicForm"
                    :editable="!useTemplate"
                    :form-options="formOptions"
                    @addSibling="handleAddSibling"
                    @remove="handleRemove"
                  />
                </el-tab-pane>
                <el-tab-pane label="JSON" name="json">
                  <JsonEdit
                    ref="jsonEditDynamic"
                    v-model="hardDynamicForm"
                    :editable="!useTemplate"
                  />
                </el-tab-pane>
              </el-tabs>
            </el-form-item>
            <el-form-item v-if="form.config.is_async" label="查询字段" prop="body.query">
              <JsonEdit
                ref="jsonEditQuery"
                v-model="hardQueryForm"
                :editable="!useTemplate"
              />
            </el-form-item>
          </div>
        </div>
      </div>
      <div v-else>
        <GenerateFontForm
          ref="vetorForm"
          v-model="editData"
          :uri="form.uri"
          :editable="!useTemplate"
        />
      </div>

      <el-form-item label="配置" prop="config" class="wrap-form-item">
        <EasyForm v-if="configOptions.length > 0" :data="configData" :struct="configOptions" :editable="!useTemplate" />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="form.remark" placeholder="备注" type="textarea" />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="cancel">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import JsonEdit from '@/components/Smoke/JsonEdit'
import NestedFieldTb from '@/components/Smoke/NestedFieldTb'
import EasyForm from '@/components/Smoke/EasyForm'
import SampleForm from '@/components/Smoke/SampleForm'
import GenerateFontForm from '@/components/Smoke/GenerateFontForm'
import axios from 'axios'
import { Promise } from 'core-js'
export default {
  name: 'SmokeCreateDialog',
  components: {
    NestedFieldTb,
    JsonEdit,
    EasyForm,
    SampleForm,
    GenerateFontForm
  },
  props: {
    mode: {
      type: String,
      default: 'task'
    },
    title: {
      type: String,
      default: '创建任务'
    },
    update: {
      type: Boolean,
      default: false
    },
    taskId: {
      type: Number,
      default: undefined
    },
    templateId: {
      type: Number,
      default: undefined
    },
    open: {
      type: Boolean,
      default: false
    },
    serviceOptions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      // 配置选项
      configOptions: [],
      // 模板列表
      templateOptions: [],
      // 接口列表
      uriOptions: [],
      // host列表
      hostOptions: [],
      // 简单表单配置
      paramOptions: [],
      // 表单参数
      form: {
        body: {
          static: {},
          dynamic: [
            {
              key: '',
              type: '',
              value: [],
              sampletype: [],
              len: undefined,
              children: []
            }
          ],
          query: {}
        },
        config: {
          is_async: false
        }
      },
      // 简单模式表单
      easyStaticForm: {},
      easyDynamicForm: [],
      // 高级模式表单
      hardStaticForm: {},
      hardDynamicForm: [
        {
          key: '',
          type: '',
          value: [],
          sampletype: [],
          len: undefined,
          children: []
        }
      ],
      hardQueryForm: {},
      formOptions: [],
      sampleFormOptions: [],
      sampleFormStruct: [],
      activeView: 'form',
      rules: {
        name: [
          { required: true, message: '任务名不能为空', trigger: 'blur' }
        ],
        exec_time: [
          { required: true, message: '执行时间不能为空', trigger: 'blur' }
        ],
        // 自定义校验在created中添加，data中还没有this,无法绑定
        service: [],
        uri: [],
        host: []
      },
      useTemplate: false,
      useEasy: true,
      configData: {},
      commonConfigOptions: [],
      geFontConfigOptions: [],
      partialGenFontConfigOptions: [],
      editData: []
    }
  },
  computed: {
    IsEditable() {
      if (this.form.template_id && this.mode === 'task') {
        return false
      }
      return true
    },
    IsGenerateFont() {
      return this.form.service === 'edit' && this.form.uri && this.form.uri.includes('/generate_font')
    }
  },
  watch: {
    activeView(val) {
      if (val === 'json') {
        this.$nextTick(() => {
          this.$refs.jsonEditDynamic.refresh()
        })
      }
    },
    open(val) {
      if (val) {
        if (this.update) {
          this.form.id = this.mode === 'task' ? this.taskId : this.templateId
          this.fetchData()
        } else {
          if (this.mode === 'task' && this.templateId) {
            this.showTemplateData(this.templateId)
          } else if (this.templateId || this.taskId) {
            this.fetchData()
          } else {
            this.reset()
          }
        }
      }
    },
    useEasy(val) {
      // 由简单模式切换到高级模式，自动将简单模式的数据同步到高级模式
      if (!val) {
        this.hardStaticForm = { ...this.easyStaticForm }
        if (this.$refs.sampleForm) {
          const res = this.$refs.sampleForm.convertToDynamic()
          if (res.length > 0) {
            this.hardDynamicForm = res
            this.easyDynamicForm = res
          }
        }
      } else {
        const res = this.removeNoExist(this.hardStaticForm, this.paramOptions)
        this.easyStaticForm = { ...res }
        this.easyDynamicForm = this.hardDynamicForm
      }
    },
    'form.uri': {
      handler(val, oldVal) {
        const item = this.uriOptions.find(item => item.value.uri === val)
        if (val && val.includes('/generate_font')) {
          if (val.includes('/partial')) {
            this.configOptions = [...this.commonConfigOptions, ...this.partialGenFontConfigOptions]
          } else {
            this.configOptions = [...this.commonConfigOptions, ...this.geFontConfigOptions]
          }
        } else {
          this.configOptions = this.commonConfigOptions
        }
        if (item) {
          if (oldVal) {
            this.easyStaticForm = {}
          }

          this.getDicts(`smoke_${item.value.model}_param`).then((response) => {
            if (oldVal) {
              this.easyStaticForm = {}
            }
            const data = this.parseParamData(response.data)
            this.paramOptions = this.transformStruct(data)
            this.sampleFormStruct = data.filter(item => item.value &&
                        (item.value.type === 'sample' ||
                        item.value.type === 'other'))
          })
          this.$set(this.form.config, 'is_async', item.value.is_async)
        // this.form.config.is_async = item.value.is_async
        } else {
          this.paramOptions = []
          this.sampleFormStruct = []
        }
      },
      immediate: true
    }
  },
  created() {
    this.getDicts('pdf_smokev2_config').then((response) => {
      this.commonConfigOptions = this.parseParamData(response.data)
      this.commonConfigOptions = this.transformStruct(this.commonConfigOptions)
      this.configOptions = this.commonConfigOptions
    })
    this.getDicts('smoke_genfont_config').then((response) => {
      this.geFontConfigOptions = this.parseParamData(response.data)
      this.geFontConfigOptions = this.transformStruct(this.geFontConfigOptions)
    })
    this.getDicts('smoke_partial_genfont_config').then((response) => {
      this.partialGenFontConfigOptions = this.parseParamData(response.data)
      this.partialGenFontConfigOptions = this.transformStruct(this.partialGenFontConfigOptions)
    })
    this.getDicts('pdf_smoke_form').then((response) => {
      const options = this.parseParamData(response.data)
      this.sampleFormOptions = options
        .filter(item => item.value.type === 'sample')
        .map(item => {
          return {
            label: item.label,
            value: item.value.value,
            remark: item.remark
          }
        })
      this.formOptions = options
        .map(item => {
          return {
            label: item.label,
            value: item.value.value,
            remark: item.remark
          }
        })
    })
    this.rules.service = [
      { validator: this.validateFormItem, trigger: 'blur' }
    ]
    this.rules.uri = [
      { validator: this.validateFormItem, trigger: 'blur' }
    ]
    this.rules.host = [
      { validator: this.validateFormItem, trigger: 'blur' }
    ]
  },
  methods: {
    /** 提交表单 */
    submitForm() {
      this.$refs['form'].validate((valid) => {
        let isValid = false
        if (valid) {
          if (this.form.service === 'edit' && this.form.uri && this.form.uri.includes('/generate_font')) {
            isValid = this.$refs.vetorForm.handleSubmit()
          } else {
            if (this.useEasy) {
              if (this.$refs.sampleForm.validate()) {
                isValid = true
              }
            } else {
              if (this.validateCustom()) {
                isValid = true
              }
            }
          }
        }
        if (isValid) {
          try {
            if (this.update) {
              this.updateData()
            } else {
              this.createData()
            }
          } catch (e) {
            this.msgError(e.message)
          }
        }
      })
    },
    // 创建数据
    createData() {
      const data = this.createReqData()
      let apiUrl = ''
      if (this.mode === 'task') {
        if (data.template_id && this.useTemplate) {
          data.template_id = parseInt(data.template_id)
          if (isNaN(data.template_id)) {
            data.template_id = undefined
          }
        } else {
          data.template_id = undefined
        }
        apiUrl = 'http://10.213.40.66:8001/data-processing/smoke/tasks'
      } else if (this.mode === 'template') {
        apiUrl = 'http://10.213.40.66:8001/data-processing/smoke/templates'
      }
      axios.post(apiUrl, data).then((response) => {
        if (response.data.code === 200) {
          this.msgSuccess('创建成功')
          this.cancel()
        } else {
          this.msgError('创建失败', response.data.msg)
        }
      }).catch((error) => {
        console.error(error)
        this.msgError('创建失败' + error.response.data.msg)
      })
    },
    // 更新数据
    updateData() {
      const data = this.createReqData()
      let apiUrl = ''
      if (this.mode === 'task') {
        apiUrl = `/data-processing/smoke/tasks/${data.id}/update`
      } else if (this.mode === 'template') {
        apiUrl = `http://10.213.40.66:8001/data-processing/smoke/templates/${data.id}/update`
      }
      axios.post(apiUrl, data).then((response) => {
        if (response.data.code === 200) {
          this.msgSuccess('更新成功')
          this.cancel()
        } else {
          this.msgError('更新失败' + response.data.msg)
        }
      }).catch((error) => {
        console.error(error)
        this.msgError('更新失败' + error.response.data.msg)
      })
    },
    // 取消按钮
    cancel() {
      this.closeDialog()
      this.$emit('close')
    },
    closeDialog() {
      this.reset()
      this.$emit('update:open', false)
    },
    // 重置
    reset() {
      console.log('重置')
      this.form = {
        body: {
          static: {},
          dynamic: [
            {
              key: '',
              type: '',
              value: [],
              sampletype: [],
              len: undefined,
              children: []
            }
          ],
          query: {}
        },
        config: {
          is_async: false
        }
      }
      this.templateOptions = []
      this.activeView = 'form'
      this.easyStaticForm = {}
      this.easyDynamicForm = []
      this.hardStaticForm = {}
      this.hardDynamicForm = [
        {
          key: '',
          type: '',
          value: [],
          sampletype: [],
          len: undefined,
          children: []
        }
      ]
      this.hardQueryForm = {}
      this.sampleFormStruct = []
      this.hostOptions = []
      this.uriOptions = []
      this.paramOptions = []

      this.useTemplate = false
      this.useEasy = true
      this.activeView = 'form'
      this.configData = {}
      this.editData = []
      if (this.$refs.vetorForm) {
        this.$refs.vetorForm.reset()
      }
      this.resetForm('form')
    },
    getUidFormCookie() {
      const cookie = document.cookie
      const cookieArr = cookie.split(';')
      for (let i = 0; i < cookieArr.length; i++) {
        const cookiePair = cookieArr[i].split('=')
        if (cookiePair[0].trim() === 'uid') {
          return decodeURIComponent(cookiePair[1])
        }
      }
      return null
    },
    createNewRow() {
      return {
        key: '',
        value: [],
        type: '', // 默认类型
        form: '',
        len: undefined,
        sampletype: [],
        children: [] // 存放子行数据
      }
    },
    // 生成请求数据
    createReqData() {
      const data = { ... this.form }
      data.uid = this.getUidFormCookie()
      data.config = this.handleConfigData(this.configData)
      data.id = this.mode === 'task' ? this.taskId : this.templateId
      if (this.form.service === 'edit' && this.form.uri && this.form.uri.includes('/generate_font')) {
        data.body = {
          dynamic: this.editData
        }
      } else if (this.useEasy) {
        data.body = {
          static: this.easyStaticForm,
          dynamic: this.$refs.sampleForm.convertToDynamic()
        }
      } else {
        data.body = {
          static: this.hardStaticForm,
          dynamic: this.hardDynamicForm,
          query: this.hardQueryForm
        }
      }
      if (this.mode === 'task') {
        if (this.useTemplate) {
          data.template_id = this.form.template_id
        } else {
          data.template_id = undefined
        }
      }
      return data
    },
    // 处理同级新增（在指定 index 后插入新行）m
    handleAddSibling(index) {
      this.form.body.dynamic.splice(index + 1, 0, this.createNewRow())
    },
    showTemplateData(id) {
      axios.get('http://10.213.40.66:8001/data-processing/smoke/templates', { params: { id: id }}).then((response) => {
        if (response.data.code === 200) {
          // 保留原有的执行时间
          const exec_time = this.form.exec_time
          const name = this.form.name
          this.initFormStruct(response.data.data.service, response.data.data.uri).then(() => {
            this.form = response.data.data
            const config = this.showConfigData(this.form.config)
            this.$set(this.form, 'config', config)
            this.hardDynamicForm = this.form.body.dynamic
            this.hardStaticForm = this.form.body.static
            this.configData = this.form.config
            this.useEasy = false
            if (this.form.service === 'edit' && this.form.uri && this.form.uri.includes('/generate_font')) {
              this.editData = this.form.body.dynamic
            }
            if (!this.hardDynamicForm || this.hardDynamicForm.length === 0) {
              this.hardDynamicForm = [
                {
                  key: '',
                  type: '',
                  value: [],
                  sampletype: [],
                  len: undefined,
                  children: []
                }
              ]
            }
            if (!this.hardStaticForm) {
              this.hardStaticForm = {}
            }
            this.$set(this.form, 'template_id', id)
            this.$set(this.form, 'exec_time', exec_time)
            this.$set(this.form, 'name', name)
            this.useTemplate = true
            this.msgSuccess('查询成功')
          }).catch((error) => {
            console.error(error)
            this.msgError('获取模板数据失败' + (error.response.data.msg || '未知错误'))
          })
        } else {
          this.msgError('获取模板数据失败' + response.data.msg)
        }
      }).catch((error) => {
        console.error(error)
        this.msgError('获取模板数据失败' + error.response.data.msg)
      })
    },
    // 处理同级删除
    handleRemove(index) {
      if (this.form.body.dynamic.length > 1) {
        this.form.body.dynamic.splice(index, 1)
      } else {
        this.$set(this.form.body.dynamic, 0, this.createNewRow())
      }
    },
    convertLongtime(time) {
      if (time === 0) {
        return ''
      }
      var date = new Date(time * 1000)
      var DD = String(date.getDate()).padStart(2, '0') // 获取日
      var MM = String(date.getMonth() + 1).padStart(2, '0') // 获取月份，1 月为 0
      var yyyy = date.getFullYear() // 获取年
      var hh = String(date.getHours()).padStart(2, '0') // 获取当前小时数(0-23)
      var mm = String(date.getMinutes()).padStart(2, '0') // 获取当前分钟数(0-59)
      var ss = String(date.getSeconds()).padStart(2, '0') // 获取当前秒数(0-59)
      var today = yyyy + '-' + MM + '-' + DD + ' ' + hh + ':' + mm + ':' + ss
      return today
    },
    // 获取数据
    fetchData() {
      let apiUrl = ''
      let params = {}
      if (this.mode === 'task') {
        apiUrl = `http://10.213.40.66:8001/data-processing/smoke/tasks`
        params = { id: this.taskId }
      } else if (this.mode === 'template') {
        apiUrl = `http://10.213.40.66:8001/data-processing/smoke/templates`
        params = { id: this.templateId }
      }
      axios.get(apiUrl, { params }).then((response) => {
        if (response.data.code === 200) {
          // 修改默认使用高级模式，后端传过来的数据可能存在结构中不存在的字段
          this.useEasy = false
          if (response.data.data.exec_time) {
            response.data.data.exec_time = this.convertLongtime(response.data.data.exec_time)
          }
          if (!this.update) {
            response.data.data.name = undefined
            response.data.data.exec_time = undefined
          }
          if (response.data.data.template_id) {
            this.useTemplate = true
          } else {
            this.useTemplate = false
          }
          this.initFormStruct(response.data.data.service, response.data.data.uri).then(() => {
            this.form = response.data.data
            const config = this.showConfigData(this.form.config)
            this.$set(this.form, 'config', config)
            this.hardDynamicForm = this.form.body.dynamic
            this.hardStaticForm = this.form.body.static
            this.configData = this.form.config
            if (this.form.service === 'edit' && this.form.uri && this.form.uri.includes('/generate_font')) {
              this.editData = this.form.body.dynamic
            }
            this.msgSuccess('查询成功')
          }).catch((error) => {
            console.error(error)
            this.msgError('获取' + this.mode + '数据失败' + (error.response.data.msg || '未知错误'))
            this.cancel()
          })
        } else {
          this.msgError('获取' + this.mode + '数据失败' + (response.data.msg || '未知错误'))
          this.cancel()
        }
      }).catch((error) => {
        console.error(error)
        this.msgError('获取' + this.mode + '数据失败' + (error.response.data.msg || '未知错误'))
        this.cancel()
      })
    },
    validateCustom() {
      if (this.form.template_id) {
        return true
      }
      if (this.$refs.jsonEditStatic && !this.$refs.jsonEditStatic.validateJson()) {
        this.msgError('静态字段 JSON 格式错误')
        return false
      }
      if (this.$refs.jsonEditDynamic && !this.$refs.jsonEditDynamic.validateJson()) {
        this.msgError('动态字段 JSON 格式错误')
        return false
      }
      if (!this.validateDynamic(this.hardDynamicForm)) {
        return false
      }
      if (this.$refs.jsonEditQuery && !this.$refs.jsonEditQuery.validateJson()) {
        this.msgError('查询字段 JSON 格式错误')
        return false
      }
      return true
    },
    validateDynamic(children) {
      for (const child of children) {
        if (!child.key) {
          this.msgError('动态字段的key不能为空')
          return false
        }
        if (child.len < 0) {
          this.msgError('动态字段的len不能小于0')
          return false
        }
        if (child.children && child.children.length > 0) {
          if (!this.validateDynamic(child.children)) {
            return false
          }
        } else {
          if (!child.value) {
            this.msgError('动态字段的value不能为空')
            return false
          }
          if (!child.form) {
            this.msgError('动态字段的form不能为空')
            return false
          }
        }
      }
      return true
    },
    validateFormItem(rule, value, callback) {
      if (this.form.template_id && this.mode === 'task') {
        callback()
      } else if (value) {
        callback()
      } else {
        callback(new Error('不能为空'))
      }
    },
    convertLenToInt(children) {
      for (const child of children) {
        if (child.len) {
          child.len = parseInt(child.len)
          if (isNaN(child.len)) {
            child.len = 1
          }
        } else {
          child.len = 1
        }
        if (child.children && child.children.length > 0) {
          this.convertLenToInt(child.children)
        }
      }
    },
    convertConfig(config) {
      if (typeof config.connum !== 'number') {
        if (isNaN(config.connum)) {
          config.connum = 1
        } else {
          config.connum = parseInt(config.connum)
        }
      }
      if (typeof config.resave_mode !== 'number') {
        config.resave_mode = undefined
      }
      if (typeof config.save_response_mode !== 'number') {
        config.save_response_mode = undefined
      }
    },
    getTemplateOptions(service) {
      const reqData = {
        service: service
      }
      axios.post('http://10.213.40.66:8001/data-processing/smoke/templates/search', reqData).then((response) => {
        if (response.data.code === 200) {
          this.templateOptions = response.data.data.templates
        } else {
          this.msgError('获取模板列表失败' + response.data.msg)
        }
      }).catch((error) => {
        console.error(error)
        this.msgError('获取模板列表失败' + error.response.data.msg)
      })
    },
    parseParamData(list) {
      return list.map(item => {
        if (typeof item.value === 'string') {
          try {
            item.value = JSON.parse(item.value) // 尝试解析 JSON 字符串
          } catch (error) {
            console.error('JSON 解析失败，跳过该项:', item.value, error)
            return null // 如果解析失败，返回 null
          }
        }
        return item // 返回解析成功的项
      }).filter(item => item !== null) // 过滤掉解析失败的项
    },
    transformStruct(data) {
      // 建一个 map方便查找
      const lookup = {}

      const staticStruct = data.filter(item => item.value &&
                        item.value.type !== 'sample' &&
                        item.value.type !== 'other')

      staticStruct.forEach(item => {
        if (item.value && item.value.prop) {
          lookup[item.value.prop] = item
        }
      })

      // 记录所有应该被移除的子节点 prop
      const propsToRemove = new Set()

      // 第一次遍历，处理有 children 的节点
      staticStruct.forEach(item => {
        if (item.value && Array.isArray(item.value.children)) {
          const childrenKeys = item.value.children

          item.value.value = childrenKeys.map(key => {
            const matchedItem = lookup[key]
            if (matchedItem) {
              propsToRemove.add(key) // 标记这个prop需要被移除
              return {
                label: matchedItem.label,
                value: matchedItem.value
              }
            } else {
              return null
            }
          }).filter(Boolean)

          delete item.value.children // 处理完删除 children 字段
        }
      })

      // 第二次遍历，移除被 children 引用过的子节点
      const result = staticStruct.filter(item => {
        const prop = item?.value?.prop
        return !propsToRemove.has(prop) || (Array.isArray(item.value.value))
        // 如果是父节点（展开后的），保留
        // 如果是子节点（出现在 propsToRemove 里），移除
      })

      return result
    },
    removeNoExist(staticData, paramOptions) {
      // 构建一个 Set，存储所有有效的 prop
      const validProps = new Set()

      // 递归提取 paramOptions 中的所有 prop
      const extractProps = (options) => {
        options.forEach(option => {
          if (option.value && option.value.prop) {
            validProps.add(option.value.prop)
          }
          if (Array.isArray(option.value?.value)) {
            extractProps(option.value.value) // 递归处理嵌套的 value
          }
        })
      }

      extractProps(paramOptions)

      // 递归清理 staticData 中的无效 key
      const cleanStatic = (data) => {
        Object.keys(data).forEach(key => {
          if (!validProps.has(key)) {
            // 如果 key 不在 validProps 中，删除它
            delete data[key]
          } else if (typeof data[key] === 'object' && data[key] !== null) {
            // 如果 key 对应的值是对象，递归处理
            cleanStatic(data[key])
          }
        })
      }

      cleanStatic(staticData)
      return staticData
    },
    existsInUriOptions(value) {
      return this.uriOptions.some(item => item.value.uri === value)
    },
    handleServiceChange(val) {
      if (val) {
        this.getTemplateOptions(val)
        this.getDicts(`smoke_${val}_uri`).then((response) => {
          this.uriOptions = this.parseParamData(response.data)
        })
        this.getDicts(`smoke_${val}_host`).then((response) => {
          this.hostOptions = response.data
        }).catch((error) => {
          console.error(error)
          this.msgError('获取host列表失败' + error.response.data.msg)
        })
      } else {
        this.templateOptions = []
      }
    },
    handleUriChange(val) {
      const item = this.uriOptions.find(item => item.value.uri === val)
      if (item) {
        this.getDicts(`smoke_${item.label}_param`).then((response) => {
          const data = this.parseParamData(response.data)
          this.paramOptions = this.transformStruct(data)
          this.sampleFormStruct = data.filter(item => item.value &&
                        (item.value.type === 'sample' ||
                        item.value.type === 'other'))
        })
        this.$set(this.form.config, 'is_async', item.value.is_async)
        // this.form.config.is_async = item.value.is_async
      } else {
        this.paramOptions = []
        this.sampleFormStruct = []
      }
    },
    initFormStruct(service, uri) {
      return Promise.all([
        this.getHostDicts(service),
        this.getUriDicts(service).then(() => {
          const item = this.uriOptions.find(item => item.value.uri === uri)
          if (item) {
            this.getParamDicts(item.label)
          } else {
            this.paramOptions = []
            this.sampleFormStruct = []
          }
        })]
      )
    },
    getUriDicts(service) {
      return this.getDicts(`smoke_${service}_uri`).then((response) => {
        this.uriOptions = this.parseParamData(response.data)
      })
    },
    getHostDicts(service) {
      return this.getDicts(`smoke_${service}_host`).then((response) => {
        this.hostOptions = response.data
      })
    },
    getParamDicts(uri) {
      return this.getDicts(`smoke_${uri}_param`).then((response) => {
        const data = this.parseParamData(response.data)
        if (this.paramOptions.length > 0 || this.sampleFormStruct.length > 0) {
          this.form.body.static = {}
        }
        this.paramOptions = this.transformStruct(data)
        this.sampleFormStruct = data.filter(item => item.value &&
                        (item.value.type === 'sample' ||
                        item.value.type === 'other'))
      })
    },
    handleConfigData(data) {
      const config = { ...this.removeNoExist(data, this.configOptions) }
      for (const op of this.configOptions) {
        if (op.value.type === 'el-input' && op.value.split) {
          const list = config[op.value.prop].split(',')
          switch (op.value.valuetype) {
            case 'number':
              config[op.value.prop] = list.map(item => {
                const num = Number(item)
                if (isNaN(num)) {
                  throw new Error(`Invalid number: ${item}`) // 抛出错误
                }
                return num
              })
              break
            case 'bool':
              config[op.value.prop] = list.map(item => item.toLowerCase() === 'true')
              break
            default:
              config[op.value.prop] = list
          }
        }
      }
      return config
    },
    showConfigData(data) {
      const config = data
      for (const key in config) {
        if (Array.isArray(config[key])) {
          config[key] = config[key].join(',')
        }
      }
      return config
    }
  }
}
</script>
