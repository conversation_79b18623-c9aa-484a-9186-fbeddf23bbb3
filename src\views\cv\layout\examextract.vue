<template>
  <!-- 创建一个卡片式的容器，使用主题内容区域将背景色统一 -->
  <el-card>
    <!-- 标题插槽：显示使用说明 -->
    <div slot="header">
      <CVHint>使用说明：（试卷切题）选择一张图片，系统会对图片进行处理，转换成功后会在页面展示结果</CVHint>
    </div>

    <!-- 主体内容 -->
    <div class="card-content">
      <div>
        <div class="container">
          <el-row type="flex" justify="center" :gutter="20">

            <el-col :span="8" class="grid-content">
              <div class="upload-container">
                <div class="params-container" style="width: 100% !important">

                  <!-- <div class="params">
                    <span class="param-label" style="width: 25% !important">版本 :</span>
                    <el-select
                      v-model="startPage.version"
                      class="param-input"
                      style="width: 75% !important"
                      placeholder="已是最新版本"
                      size="small"
                      @change="selectVersion"
                    >
                      <el-option
                        v-for="dict in versions"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                        @click.native="handleSelect(dict)"
                      />
                    </el-select>
                  </div> -->
                  <div class="params">
                    <span class="param-label" style="width: 25% !important">版本 :</span>
                    <el-select
                      v-model="grayUrl"
                      class="param-input"
                      style="width: 75% !important"
                      placeholder="已是最新版本"
                      size="small"
                      @change="selectGrayVersion"
                    >
                      <el-option
                        v-for="dict in grayVersionOptions"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </div>
                  <div class="params">
                    <span
                      class="param-label"
                      style="width: 25% !important"
                    >输出 :</span>
                    <el-select
                      v-model="startPage.exporttype"
                      class="param-input"
                      style="width: 75% !important"
                      placeholder="导出类型"
                      size="small"
                      @change="selectExporttype"
                    >
                      <el-option
                        v-for="dict in exporttypeOptions"
                        :key="dict"

                        :value="dict"
                      />
                    </el-select>
                  </div>
                  <br><div class="params">
                    <!-- :disabled="true" -->
                    <el-button
                      type="warning"
                      size="mini"
                      @click="handleSetting"
                    >高级设置</el-button>
                    <el-tooltip content="切换输入方式" placement="right-end">
                      <el-select v-model.number="choiceInput" placeholder="默认上传文件">
                        <el-option
                          v-for="dict in choiceInputOptions"
                          :key="dict.value"
                          :label="dict.label"
                          :value="dict.value"
                        />
                      </el-select>
                    </el-tooltip>
                    <!-- <el-button
                      type="primary"
                      size="mini"
                      :disabled="startPage.exporttype==='picedit'"
                      @click="convertclick"
                    >转换</el-button> -->
                  </div>

                  <hr>
                  <div v-if="choiceInput=='inputUrl'">
                    <br><span style="color:blue"> {{ "输入图片链接：" }}</span><br>
                    <div class="params">
                      <span
                        class="param-label"
                        style="width: 25% !important"
                      >url of png :</span>
                      <el-input
                        v-model="fileUrl"
                        class="param-input"
                        style="width: 75% !important"
                        type="text"
                      />
                    </div>
                    <el-button
                      type="primary"
                      size="mini"
                      :disabled="startPage.exporttype==='picedit'"
                      @click="convertclick"
                    >开始转换</el-button>
                  </div>

                  <!-- 转换或点击上传前高级设置对话框 -->
                  <el-dialog :title="title" :visible.sync="open" width="600px">
                    <el-form ref="form" :model="form" :rules="rules" label-width="120px" inline-message>

                      <el-row>
                        <el-col :span="24">
                          <el-tooltip content="选择调用的公式模型" placement="left-end">
                            <template slot="default">
                              <el-form-item label="choice_model:" prop="choiceModel">
                                <el-select v-model.number="form.choiceModel" :disabled="true" placeholder="请选择">
                                  <el-option
                                    v-for="dict in choiceModelOptions"
                                    :key="dict.value"
                                    :label="dict.label"
                                    :value="dict.value"
                                  />
                                </el-select>
                              </el-form-item>
                            </template>
                          </el-tooltip>
                        </el-col>
                        <el-col :span="24">
                          <el-tooltip content="是否开启层级(get_testpaper_level)" placement="left-end">
                            <template slot="default">
                              <el-form-item label="testpaper_level:">
                                <el-radio-group v-model="switchTestPaperLevel">
                                  <el-radio
                                    v-for="dict in switchTestPaperLevelOptions"
                                    :key="dict.value"
                                    :label="dict.value"
                                  >{{ dict.label }}</el-radio>
                                </el-radio-group>
                              </el-form-item>
                            </template>
                          </el-tooltip>
                        </el-col>

                      </el-row>
                    </el-form>

                    <div slot="footer" class="dialog-footer">
                      <el-button class="reset-button" type="warning" @click="reset">重 置</el-button>
                      <div class="right-buttons">
                        <el-button class="confirm-button" type="primary" @click="submitForm">确 定</el-button>
                        <el-button class="cancel-button" @click="cancel">取 消</el-button>
                      </div>
                    </div>
                  </el-dialog>
                </div>

                <div v-if="choiceInput=='uploadFile'">
                  <br><span style="color:blue"> {{ "上传图片：" }}</span>
                  <el-upload
                    v-loading="loading"
                    element-loading-text="文件处理中"
                    class="uploader"
                    list-type="text"
                    :accept="'image/*'"
                    :multiple="false"
                    :show-file-list="false"
                    :file-list="fileList"
                    :http-request="submitUpload"
                    :on-success="onSuccess"
                    action="placeholder"
                    drag
                  >
                    <i class="el-icon-upload" />
                    <div class="el-upload__text">
                      将文件拖到此处，或
                      <el-button type="text">点击上传</el-button>
                    </div>
                  </el-upload>

                  <el-progress
                    v-show="percentage !== 0 && percentage !== 100"
                    :text-inside="true"
                    :stroke-width="14"
                    :percentage="percentage"
                  />
                </div>

                <div class="params-container">
                  <br><div v-show="fileName !== ''" class="params">
                    <span style="color:blue" class="param-label">{{ '转化结果' }} :</span>
                  </div>
                  <div v-show="fileName !== ''" class="params">
                    <span class="param-label">{{ fileName }}</span>
                  </div>
                  <div v-show="statistic.status !== -1" class="params">
                    <span class="param-label">处理用时:</span>
                    <span class="param-input">
                      {{ formattedDuration }}
                    </span>
                  </div>
                  <div v-show="statistic.status !== -1" class="time-params">
                    <div v-for="(time, index) in resultTime" :key="index">
                      <span class="param-label">{{ resultTime[index].comment }}</span>
                      <span class="param-input">
                        {{ resultTime[index].value + 's' }}<br>
                      </span>
                    </div>
                  </div>
                  <div v-show="statistic.status !== -1" class="params">
                    <span v-show="statistic.status === 1" class="param-label" style="color:blue;">转化成功!</span>
                    <span v-show="statistic.status === 0" class="param-label" style="color: #ff0000;">转化失败!</span>
                  </div>
                </div>

              </div>
              <ul>
                <li v-for="(errMsg, index) in result.errMsgs" :key="index">
                  {{ errMsg }}
                </li>
              </ul>
            </el-col>
            <el-col
              :span="5"
              class="grid-content"
            >
              <CVDemo :thumbnail="true" :loading="loading" :data="urlFiles[0]" @click="tableextractDemo(0,$event)">示例:</CVDemo>
            </el-col>
          </el-row>
        </div>

        <div v-show="showResult">
          <span class="result" style="color:blue"> {{ "若是矩形框易位/未显示，请点击下方的【复位】按钮重置。" }}</span>
          <div class="result-button">
            <el-row>
              <!-- <el-col :span="7">
                <el-button type="primary" size="small" @click="choiceFormulaType(choicePaperOptions[0].value)">显示两级切题</el-button>
              </el-col>
              <el-col :span="7">
                <el-button type="primary" size="small" @click="choiceFormulaType(choicePaperOptions[1].value)">只显示一级切题</el-button>
              </el-col>
              <el-col :span="7">
                <el-button type="success" size="small" @click="choiceFormulaType(choicePaperOptions[2].value)">只显示二级切题</el-button>
              </el-col> -->
              <el-col :span="7">
                <el-button type="primary" size="small" @click="showResult=true;showFirst=true;showSecond=false">显示一级切题内容</el-button>
              </el-col>
              <el-col :span="7">
                <el-button type="primary" size="small" @click="showResult=true;showSecond=true;showFirst=false">显示二级切题内容</el-button>
              </el-col>
              <el-col :span="7">
                <el-button type="success" size="small" @click="copyPaperCutData()">复制服务返回数据</el-button>
              </el-col>
              <el-col :span="3">
                <el-button type="danger" size="small" @click="resetRectangles()">复位</el-button>
              </el-col>
            </el-row>
          </div>
        </div>
        <br>

        <!-- 试卷切题结果展示 -->
        <div class="container">
          <div v-show="showResult" class="result">
            <el-row v-show="showResult" class="formula-row">
              <h4>{{ '试卷切题结果为:' }}</h4>
              <el-col>
                <h4>{{ '学科信息为：' }}</h4>
                {{ course_info }}
                <div v-if="showFirst">
                  <h4>{{ '以下展示一级切题内容：' }}</h4><hr>
                  <div v-for="(item, index) in originData" :key="`${item}-${index}`">
                    <div :id="'formula-' + index">
                      <h4>{{ 'block' + (index + 1) + ':' }}</h4>
                      <h5>{{ 'question:' }}</h5>
                      <div v-if="item.question">
                        <div v-for="(qu, qu_index) in item.question" :key="`${qu}-${qu_index}`">
                          {{ qu }}
                        </div>
                      </div>
                      <h5>{{ 'option:' }}</h5>
                      <div v-if="item.option">
                        <div v-for="(op, op_index) in item.option" :key="`${op}-${op_index}`">
                          {{ op }}
                        </div>
                      </div>
                      <h5>{{ 'answer:' }}</h5>
                      <div v-if="item.answer">
                        <div v-for="(ans, ans_index) in item.answer" :key="`${ans}-${ans_index}`">
                          {{ ans }}
                        </div>
                      </div>
                    </div>
                    <hr> <!-- 添加分割线 -->
                  </div>
                </div>

                <div v-if="showSecond">
                  <h4>{{ '以下展示二级切题内容：' }}</h4><hr>
                  <div v-for="(item, index) in originSubData" :key="`${item}-${index}`">
                    <div :id="'formula-' + index">
                      <h4>{{ 'sub_block' + (index + 1) + ':' }}</h4>
                      <h5>{{ 'question:' }}</h5>
                      <div v-if="item.question">
                        <div v-for="(qu, qu_index) in item.question" :key="`${qu}-${qu_index}`">
                          {{ qu }}
                        </div>
                      </div>
                      <h5>{{ 'option:' }}</h5>
                      <div v-if="item.option">
                        <div v-for="(op, op_index) in item.option" :key="`${op}-${op_index}`">
                          {{ op }}
                        </div>
                      </div>
                      <h5>{{ 'answer:' }}</h5>
                      <div v-if="item.answer">
                        <div v-for="(ans, ans_index) in item.answer" :key="`${ans}-${ans_index}`">
                          {{ ans }}
                        </div>
                      </div>
                    </div>
                    <hr> <!-- 添加分割线 -->
                  </div>
                </div>

              </el-col>
            </el-row>

            <!-- <el-row v-show="showBlock" class="formula-row">
              <h4>{{ '试卷切题结果为:' }}</h4>
              <el-col>
                <div v-for="(block, index) in content.blocks" :key="index">
                  <div v-katex>
                    <h4>题目 {{ index + 1 }} 为:</h4>
                    <div v-for="(formula) in block.block_content" :key="`${formula}-${index}`">{{ formula }}</div>
                    <div v-if="block.block_content.length > 0">
                      <div>
                        <el-button
                          v-show="block.block_formula_origin.length!=0"
                          type="text"
                          size="medium"
                          @click="copyLink(block.block_formula_origin)"
                        >复制公式原文</el-button>
                      </div>
                    </div><br>
                  </div>
                  <hr>
                </div>
              </el-col>
            </el-row> -->

            <!-- <el-row v-show="showBlock" class="formula-row">
              <h4>{{ '试卷切题结果为:' }}</h4>
              <el-col>
                <div v-for="(block, index) in content.blocks" :key="index">
                  <div v-katex>
                    <h4>题目 {{ index + 1 }} 为:</h4>
                    <template v-if="block.block_formula.length == 0 && block.block_text.length == 0">
                      {{ '公式: ' }}<br>
                      {{ '文本: ' }}
                    </template>
                    <template v-else>
                      <div v-for="(formula, formula_index) in block.block_formula" :key="`${formula}-${index}`">{{ '公式 ' + (formula_index+1) + '为: '+ formula }}</div>
                      <div v-if="block.block_formula.length > 0">
                        <div>
                          <el-button
                            type="text"
                            size="medium"
                            @click="copyLink(block.block_formula_origin)"
                          >复制公式原文</el-button>
                        </div>
                      </div><br>
                      <div v-if="block.block_text.length == 0">
                        {{ '文本: ' }}
                      </div>
                      <div v-for="(text, text_index) in block.block_text" v-else :key="`${text}-${index}`">{{ '文本 ' + (text_index+1) + '为: '+ text }}</div>
                      <div v-if="block.block_text.length > 0">
                        <div>
                          <el-button
                            type="text"
                            size="medium"
                            @click="copyLink(block.block_text)"
                          >复制文本原文</el-button>
                        </div>
                      </div>
                    </template>
                  </div>
                  <hr>
                </div>
              </el-col>
            </el-row> -->

            <el-row class="result-row" style="width: 750px;">
              <h4>{{ '只绘制两级切题的结果:' }}</h4>
              <el-col>
                <div class="canvas-container" style="width: 750px;">
                  <canvas ref="canvas" :width="imageWidth" :height="imageHeight" />
                  <img :src="originUrl" @load="setImageSize">
                </div>
                <!-- <div v-if="drawImageUrl"> -->
                <div>
                  <el-button size="small" type="primary" @click="zoomIn">放大</el-button>
                  <el-button size="small" type="primary" @click="zoomOut">缩小</el-button>
                  <!-- <el-button size="small" type="info" @click="openDialog">查看图片</el-button> -->
                </div>

              </el-col>
            </el-row>

            <el-dialog :visible="showDialog" @close="closeDialog">
              <div class="canvas-container">
                <canvas ref="canvas" :width="imageWidth" :height="imageHeight" />
                <img :src="originUrl" @load="setImageSize">
              </div>
            </el-dialog>

          </div>
        </div>
      </div>
    </div>

    <!-- 底部插槽：未使用，后续若是扩展可以考虑翻页等功能 -->
    <div slot="footer">
      footer
    </div>
  </el-card>
</template>

<script>
import axios from 'axios'
import CVHint from './components/CVHint'
import CVDemo from './components/CVDemo'

import { isValidFile } from '@/api/cv/utils'
import { fetchUrl2Ks } from '@/api/cv/request-ks3'
import { getHeaders } from '@/utils/get-header'

import { axios_wait_doc } from '@/api/cv/request'
import { sleep } from '@/api/cv/utils'

export default {
  name: 'Examextract',
  components: { CVHint, CVDemo },
  props: {
    maxFileNum: {
      validator(value) {
        if (value <= 0) {
          return 1
        }
        return value
      },
      default: 1
    },
    fileType: {
      type: Array,
      default: function() {
        return ['.png', '.jpg', '.jpeg', '.pdf', '.zip']
      }
    }
  },
  data() {
    return {
      // 输入方式字典
      choiceInputOptions: [
        { label: '上传图片(默认)', value: 'uploadFile' },
        { label: '输入图片链接', value: 'inputUrl' }
      ],
      choiceInput: 'uploadFile',
      // 公式模型字典
      choiceModelOptions: [
        { label: 'formula_small(默认)', value: 'formula_small' },
        { label: 'formula', value: 'formula' }
      ],
      // 绘制公式框/文本框字典
      choiceRectanglesOptions: [
        { label: '只绘制公式框(默认)', value: 'Latex' },
        { label: '只绘制文本框', value: 'Text' },
        { label: '公式框+文本框', value: 'LatexAndText' }
      ],
      // 选择显示行间公式/所有公式
      choicePaperOptions: [
        { label: '全部显示', value: 'all' },
        { label: '只显示一级', value: 'first' },
        { label: '只显示二级', value: 'second' }
      ],
      // 选择是否返回层级关系
      switchTestPaperLevelOptions: [
        { label: '是', value: true },
        { label: '否', value: false }
      ],
      // 耗时情况
      resultTime: [],
      fileName: '',
      // 画框后的图片url
      drawImageUrl: '',
      dialogVisible: false, // 弹窗的显示与隐藏
      // 控制公式识别结果的显示
      showFirst: true,
      showSecond: false,
      showResult: false,
      // 存储公式图片
      originImg: [],
      // 存储公式
      originData: [],
      originSubData: [],
      // 存储文本
      originText: [],
      // 存储转换后的公式
      formulas: [],
      // 弹出层标题（用户导入）
      title: '',
      // 是否显示弹出层（用户导入）
      open: false,
      // 表单参数
      form: {
        choiceModel: 'formula_small',
        choiceRectangles: 'Latex',
        // choiceFormula: 'allFormula',
        originWidth: 10
      },
      // 参数
      pdfDebugFlags: 0,
      choiceModel: 'formula_small',
      choiceRectangles: 'Latex',
      originWidth: 10,
      // 表单校验
      rules: {
        // 定义规则对象
        // trigger: 'blur' 表示在失去焦点时触发验证
        originHeight: [{ type: 'number', message: 'originHeight必须为int类型', trigger: ['blur', 'change'] }],
        originWidth: [{ type: 'number', message: 'originWidth必须为int类型', trigger: ['blur', 'change'] }]
      },
      loading: false,
      url: {
        value: '//pcv-test.wps.cn',
        default: '//pcv-test.wps.cn',
        gray: '//pcv-test-gray.wps.cn'
      },
      grayUrl: '//pcv-test-gray.wps.cn/layout/scheduler/paper_cut',
      // 选择调用开发环境或是alpha环境的接口
      grayVersionOptions: [
        { label: '开发环境', value: '//pcv-test-gray.wps.cn/layout/scheduler/paper_cut' },
        { label: '测试环境', value: '//pcv-test.wps.cn/layout/scheduler/paper_cut' },
        { label: 'alpha环境', value: '//pcv-test-gray.wps.cn/alpha/layout/scheduler/paper_cut' }
      ],
      getExtend: 1,
      fileObj: {
        name: '',
        size: 0
      },
      versionremark: '',
      remark: '',
      showNewVersion: false,
      versions: [],
      //   exporttypeOptions: ['html', 'img'],
      exporttypeOptions: ['html'],
      startPage: {
        exporttype: 'html',
        version: '',
        xlsxicon: '',
        splitsheet: '一个表格一个sheet',
        pagetext: '否',
        pagetextoption: '所有文本一个单元格'
      },
      fileUrl: '',
      fileList: [],
      fileData: '',
      // 进度百分比
      percentage: 0,
      result: {
        duration: 0,
        zipUrl: '',
        xlsxUrl: '',
        errMsgs: []
      },
      statistic: {
        status: -1,
        duration: 0
      },
      status: -1,
      originalImages: [],
      htmlsInZip: [],
      urlFiles: [
        [
          {
            url: 'http://zhai-datas.ks3-cn-beijing.ksyuncs.com/layout/people/wangluoyan/formula/examBlock01.png',
            name: '示例一.jpeg'
          },
          {
            url: 'http://zhai-datas.ks3-cn-beijing.ksyuncs.com/layout/people/wangluoyan/formula/examBlock02.png',
            name: '示例二.jpeg'
          },
          {
            url: 'http://zhai-datas.ks3-cn-beijing.ksyuncs.com/layout/people/wangluoyan/formula/examBlock03.png',
            name: '示例三.jpeg'
          },
          {
            url: 'http://zhai-datas.ks3-cn-beijing.ksyuncs.com/layout/people/wangluoyan/formula/examBlock04.png',
            name: '示例四.jpeg'
          }
        ]
      ],
      // 原图url
      originUrl: '',
      backupUrl: '',
      // 原始宽高值
      originimageHeight: 0,
      originimageWidth: 0,
      // 图片的缩放比例
      scale: 1,
      originScale: 1,
      // 图片展示宽度
      imageWidth: 750,
      imageHeight: 0,
      // 存储框坐标
      rectangles: {
        // blocks框
        blockRectangles: [],
        // formulas框
        latexRectangles: [],
        // texts框
        textRectangles: [],
        // 初始坐标值
        originalBlockRectangles: [],
        originalTextRectangles: [],
        originalLatexRectangles: []
      },
      // 存储内容
      content: {
        blocks: [],
        originData: [],
        originText: []
      },
      // 公式框
      rectanglesLatex: [],
      // 保存公式框初始值
      originalRectanglesLatex: [],
      // 保存subquestion_box初始值
      originalRectanglesSub: [],
      rectanglesSub: [],
      // // 矫正图片返回结果的标志
      // correctionImg: false,
      showDialog: false, // 控制弹窗显示/隐藏的数据属性
      // 左上角字体大小
      fontSize: 11,
      // 左上角字体内容
      fontText: '',
      subfontText: '',
      // 存储学科信息
      course_info: {},
      // 存储服务返回原始数据
      papercutData: {},
      // 选择框类型
      choice: 'blocks',
      switchTestPaperLevel: false,
      // json_url中的数据
      jsonData: {}
    }
  },
  computed: {
    // 根据公式长度确定组件长度
    contentSize() {
      // 假设每个字符的宽度为 10px
      const charWidth = 5
      // 计算公式内容的最大长度
      const maxLength = this.formulas.reduce((max, item) => {
        const length = item.latex.length
        return length > max ? length : max
      }, 0)
      //   const formulasLength = this.formulas.val.length
      // 根据公式长度计算组件区域的大小
      return maxLength * charWidth
    },
    formattedDuration() {
      return this.statistic.duration.toFixed(3) + 's'
    }
  },
  // 通过watch监听输入值的变化，并将maxConNumPage/pdfDebugFlags由 string类型转为 int类型
  watch: {
    'form.pdfDebugFlags'(val) {
      this.pdfDebugFlagsInt = parseInt(val)
    }
  },
  created() {
  },
  methods: {
    resetRectangles() {
      this.zoomIn()
      this.zoomOut()
    },
    progressPass(percentage) {
      this.percentage = 100
    },
    updateImgSize() {
      this.imageHeight = (this.scale * this.originimageHeight).toFixed(2)

      // 得到更新后的坐标值
      const updateRectangles = (rectangles, originalRectangles) => {
        rectangles.forEach((rectangle, index) => {
          rectangle.x = originalRectangles[index].x * this.scale
          rectangle.y = originalRectangles[index].y * this.scale
          rectangle.width = originalRectangles[index].width * this.scale
          rectangle.height = originalRectangles[index].height * this.scale
        })
      }
      updateRectangles(this.rectanglesLatex, this.originalRectanglesLatex)
      updateRectangles(this.rectanglesSub, this.originalRectanglesSub)

      // 重新绘制
      // this.drawRectangles(this.backupUrl, this.rectanglesLatex, this.fontText, this.fontSize, 'red')
      // this.drawRectangles(this.backupUrl, this.rectanglesSub, this.subfontText, this.fontSize, 'blue')
      this.drawRectangles(this.backupUrl, this.rectanglesLatex, this.fontText, this.fontSize, 'red',
        this.rectanglesSub, this.subfontText, this.fontSize, 'blue')

      // 关闭原始图片
      this.originUrl = ''
    },
    setImageSize(event) {
      const image = event.target
      // 存储原始图像高度宽度值
      this.originimageWidth = image.width
      this.originimageHeight = image.height
      // 计算初始scale
      this.scale = (this.imageWidth / this.originimageWidth).toFixed(4)
      this.imageHeight = (this.scale * this.originimageHeight).toFixed(2)

      this.drawRectangles(this.originUrl, this.rectangles.blockRectangles, '', this.fontSize, 'green')
      // // 关闭原始图片
      this.backupUrl = this.originUrl
      this.originUrl = ''
    },
    drawRectangles(url, rectanglesLatex, fontText, fontSize, color, rectanglesSub, fontTextSub, fontSizeSub, colorSub) {
      const canvas = this.$refs.canvas
      const ctx = canvas.getContext('2d')

      const image = new Image()
      image.src = url

      image.onload = () => {
        ctx.drawImage(image, 0, 0, this.imageWidth, this.imageHeight)

        ctx.lineWidth = 1
        // 绘制框
        ctx.strokeStyle = color
        rectanglesLatex.forEach((rect, index) => {
          ctx.strokeRect(rect.x, rect.y, rect.width, rect.height)

          // 设置文本字体字号
          // ctx.font = '8px Arial'
          ctx.font = fontSize.toString() + 'px Arial'

          // 绘制文本
          // var text = (index + 1).toString()
          var text = fontText[index]
          // 获取文本字体的宽度/高度
          var textWidth = ctx.measureText(text).width
          var textHeight = parseInt(ctx.font)

          // 设置白底
          ctx.fillStyle = 'white'
          ctx.fillRect(rect.x - 2, rect.y - textHeight + 2, textWidth + 4, textHeight)

          // 设置文本颜色
          ctx.fillStyle = 'rgba(0, 0, 255)'
          ctx.fillText(text, rect.x, rect.y)
        })

        // 绘制次要框
        ctx.strokeStyle = colorSub
        rectanglesSub.forEach((rect, index) => {
          ctx.strokeRect(rect.x, rect.y, rect.width, rect.height)

          // 设置文本字体字号
          // ctx.font = '8px Arial'
          ctx.font = fontSizeSub.toString() + 'px Arial'

          // 绘制文本
          // var text = (index + 1).toString()
          var text = fontTextSub[index]
          // 获取文本字体的宽度/高度
          var textWidth = ctx.measureText(text).width
          var textHeight = parseInt(ctx.font)

          // 设置白底
          ctx.fillStyle = 'white'
          ctx.fillRect(rect.x - 2, rect.y - textHeight + 2, textWidth + 4, textHeight)

          // 设置文本颜色
          ctx.fillStyle = 'rgba(0, 0, 255)'
          ctx.fillText(text, rect.x, rect.y)
        })

        console.log('绘制完成')
      }
    },
    drawRectangles_backup(url, rectanglesLatex, fontText, fontSize, color) {
      const canvas = this.$refs.canvas
      const ctx = canvas.getContext('2d')

      const image = new Image()
      image.src = url

      image.onload = () => {
        ctx.drawImage(image, 0, 0, this.imageWidth, this.imageHeight)

        ctx.lineWidth = 1
        // 绘制框
        ctx.strokeStyle = color
        rectanglesLatex.forEach((rect, index) => {
          ctx.strokeRect(rect.x, rect.y, rect.width, rect.height)

          // 设置文本字体字号
          // ctx.font = '8px Arial'
          ctx.font = fontSize.toString() + 'px Arial'

          // 绘制文本
          // var text = (index + 1).toString()
          var text = fontText[index]
          // 获取文本字体的宽度/高度
          var textWidth = ctx.measureText(text).width
          var textHeight = parseInt(ctx.font)

          // 设置白底
          ctx.fillStyle = 'white'
          ctx.fillRect(rect.x - 2, rect.y - textHeight + 2, textWidth + 4, textHeight)

          // 设置文本颜色
          ctx.fillStyle = 'rgba(0, 0, 255)'
          ctx.fillText(text, rect.x, rect.y)
        })

        console.log('绘制完成')
      }
    },
    fileChange(file, fileList) {
      this.fileList = fileList.slice(-this.maxFileNum)
    },
    onSuccess(response, file, fileList) {
      // hook
      this.fileChange(file, fileList)
    },
    openDialog() {
      this.dialogVisible = true // 打开弹窗
    },
    closeDialog() {
      this.dialogVisible = false // 关闭弹窗
    },
    /** 放大按钮 */
    zoomIn() {
      // 添加限制，避免过大
      if (this.imageWidth < 1000) {
        this.imageWidth = this.imageWidth + 100
        this.scale = (this.imageWidth / this.originimageWidth).toFixed(4)
      }
      // 放大字号
      if (this.fontSize < 15) {
        this.fontSize += 1
      }

      // 更新宽高值,并重新绘制
      this.updateImgSize()
    },
    /** 缩小按钮 */
    zoomOut() {
      // 添加限制，避免过小
      if (this.imageWidth > 10) {
        this.imageWidth = this.imageWidth - 50
        this.scale = (this.imageWidth / this.originimageWidth).toFixed(4)
      }
      // 缩小字号
      if (this.fontSize > 8) {
        this.fontSize -= 1
      }
      // 更新宽高值,并重新绘制
      this.updateImgSize()
    },
    copyPaperCutData() {
      this.copyData(this.papercutData)
    },
    copyData(data) {
      var data_value = JSON.stringify(data)

      var textArea = document.createElement('textarea')
      textArea.value = data_value
      document.body.appendChild(textArea)
      textArea.select()
      textArea.focus()
      document.execCommand('copy')
      document.body.removeChild(textArea)
      this.$message.success('复制成功')
    },
    /** 复制链接按钮 */
    copyLink(url) {
      var inputNode = document.getElementById('pdfcv_input')
      if (inputNode != null) {
        inputNode.remove()
      }
      inputNode = document.createElement('input')
      inputNode.value = url
      inputNode.id = 'pdfcv_input'
      document.body.appendChild(inputNode)
      inputNode.select()
      document.execCommand('Copy')
      inputNode.className = 'oInput'
      inputNode.style.display = 'none'
      this.$message.success('复制成功')
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.choiceModel = this.form.choiceModel
          this.choiceRectangles = this.form.choiceRectangles
          this.originWidth = this.form.originWidth
          this.open = false
        }
      })
    },
    // 取消按钮
    cancel() {
      this.form.choiceModel = this.choiceModel
      this.form.choiceRectangles = this.choiceRectangles
      this.form.originWidth = this.originWidth
      this.open = false
    },
    /** 重置按钮 */
    // 表单重置
    reset() {
      this.form = {
        choiceModel: 'formula_small',
        choiceRectangles: 'Latex',
        originWidth: 10
      }
      this.resetForm('form')
    },
    /** 高级设置按钮操作 */
    handleSetting() {
      this.open = true
      this.title = '高级设置'
      this.form.password = ''
    },
    // 获取版本
    handleSelect(item) {
      this.remark = item.remark
    },
    // 选择版本
    selectVersion(value) {
      this.versionremark = ''
      this.url.value = value
    },
    // 选择版本
    selectGrayVersion(value) {
      this.versionremark = ''
      this.grayUrl = value
    },
    // 选择导出类型
    selectExporttype(value) {
      if (value === 'html') {
        this.showNewVersion = false
        this.startPage.pagetextoption = '所有文本一个单元格'
        this.startPage.splitsheet = '一个表格一个sheet'
      } else {
        this.showNewVersion = true
        this.versionremark = ''
      }
    },
    resetResult() {
      this.result = {
        duration: 0,
        zipUrl: '',
        errMsgs: [] }
      this.statistic = {
        status: -1,
        duration: 0
      }
      this.status = -1
      this.originalImages = []
      this.htmlsInZip = []
      this.resultTime = []
      this.drawImageUrl = ''
      // this.correctionImg = false
      this.showFirst = true
      this.showSecond = false
      this.showResult = false
      // 按钮重置
      this.choiceFormula = 'allFormula'
      // 字体大小
      this.fontSize = 11
    },
    // ks3上传前的文件类型检查
    beforeUpload(file) {
      // 清理旧结果
      this.fileObj = { name: '', size: 0 }
      this.resetResult()

      const { name } = file
      var ext = '.' + name.trim().split('.').pop()
      ext = ext.toLowerCase()
      if (!isValidFile(ext, this.fileType)) {
        this.$message({
          showClose: true,
          message: '请选择png/jpg/jpeg/pdf文件/zip文件!',
          center: true,
          duration: 4000,
          type: 'error'
        })
        return false
      } else {
        this.fileObj = file
        return true
      }
    },
    /** 转换按钮操作 */
    async convertclick() {
      this.resetResult()
      this.fileName = ''
      console.log(this.fileUrl)
      // 检查url是否有效
      this.checkImageLinkAvailability(this.fileUrl)
        .then(async() => {
          // 执行链接成功后的逻辑
          console.log('url匹配通过')

          this.loading = true
          // 检查this.fileUrl字符串中是否包含ksyun.com子字符串
          if (this.fileUrl.lastIndexOf('ksyun.com') > 0) {
            var urls = []
            // 将this.fileUrl的值添加到一个名为urls的数组中
            urls.push(this.fileUrl)
            const s_time = new Date().getTime()
            // 请求
            // const status = await this.examextract(urls)
            // create请求
            const create_docID = await this.examextractCreate(urls)
            if (create_docID !== undefined) {
              // 等待1s
              await sleep(1000)
              // query请求
              await this.waitDocStatus(create_docID)
            }
            // 结束加载
            this.loading = false
            const e_time = new Date().getTime()
            this.statistic = {
              // status: this.status,
              status,
              duration: this.result.duration || (e_time - s_time) / 1000
            }
            return Promise.resolve()
          } else {
            this.fetchUrl2Ks3(this.fileUrl)
          }
          this.loading = false
        })
        .catch(() => {
          // 执行链接加载失败后的逻辑
          this.$message({
            showClose: true,
            message: '请输入正确文件地址url ',
            center: true,
            duration: 4000,
            type: 'error'
          })
        })
    },
    beforeCreate(url) {
      // 保存绘制原图
      this.originUrl = url.replace(/-internal/g, '')
      // 清空之前的缩放比例
      this.scale = 1
      // 清空之前的坐标值
      this.originalRectanglesLatex = []
      this.rectanglesLatex = []
      this.originalRectanglesSub = []
      this.rectanglesSub = []
      // // 清空之前的字体内容
      this.subfontText = ''
      this.fontText = ''
      // 重置坐标
      this.rectangles = {
        // blocks框
        blockRectangles: [],
        // formulas框
        latexRectangles: [],
        // texts框
        textRectangles: [],
        // 初始坐标值
        originalBlockRectangles: [],
        originalTextRectangles: [],
        originalLatexRectangles: []
      }
      // 重置block
      // this.blocks = []
      // 重置内容
      this.originData = []
      this.originSubData = []
      // this.content = {
      //   blocks: [],
      //   originData: [],
      //   originText: []
      // }
      // 重置初始宽度值
      this.imageWidth = 750
    },
    // 转换按钮：若url不包含ksyun.com
    async fetchUrl2Ks3(sourceurl) {
      console.log('url不包含ksyun.com: ', sourceurl)
      fetchUrl2Ks(this, sourceurl).then(async(res) => {
        if (res.data.message === 'ok') {
          const ksurl = res.data.url
          console.log(ksurl)
          var urls = []
          urls.push(ksurl)
          const s_time = new Date().getTime()
          // const status = await this.examextract(urls)
          // 请求前的操作
          this.beforeCreate(urls)
          // create请求
          const create_docID = await this.examextractCreate(urls)
          if (create_docID !== undefined) {
            // 等待1s
            await sleep(1000)
            // query请求
            await this.waitDocStatus(create_docID)
          }
          // 结束加载
          this.loading = false
          const e_time = new Date().getTime()
          this.statistic = {
            // status: this.status,
            status,
            duration: this.result.duration || (e_time - s_time) / 1000
          }
          return Promise.resolve()
        }
      }).catch((error) => {
        this.$message({
          showClose: true,
          message: '文件处理失败! ' + error,
          center: true,
          duration: 4000,
          type: 'error'
        })
        return Promise.resolve(0)
      })
    },
    choiceFormulaType(choice) {
      this.choice = choice
      this.showResult = true
      if (choice === 'all') {
        this.showSecond = true
        this.showFirst = true
        this.choiceFormula = this.rectangles.latexRectangles
        this.showData(this.content.originData, this.rectangles.latexRectangles, 'red')
      } else if (choice === 'first') {
        this.showFirst = true
        this.showSecond = false
        this.choiceFormula = this.rectangles.textRectangles
        this.showData(this.content.originText, this.rectangles.textRectangles, 'purple')
      } else {
        this.showFirst = false
        this.showSecond = true
        this.choiceFormula = this.rectangles.blockRectangles
        this.showData(this.content.originData, this.rectangles.blockRectangles, 'green')
      }
    },
    showData(result_latex, rectanglesLatex) {
      // 设置公式原文
      this.originData = result_latex
      // 设置块级公式居中
      this.formulas = []
      this.formulas = result_latex.map(item => {
        return {
          'latex': `$$${item}$$`
        }
      })
      // 绘制框
      this.drawRectangles(this.backupUrl, rectanglesLatex, this.fontSize)
    },
    // 使用Image对象来检查链接是否可以加载为图片,利用浏览器的图片预加载机制
    checkImageLinkAvailability(url) {
      return new Promise((resolve, reject) => {
        if (url.trim() === '') {
          reject()
        }
        const img = new Image()
        img.onload = function() {
          resolve()
        }
        img.onerror = function() {
          reject()
        }
        img.src = url
      })
    },
    // 清空上传列表
    clearUpload() {
      this.$refs.upload.clearFiles()
      this.fileList = []
    },
    // 上传到服务器
    async submitUpload(param) {
      this.resetResult()
      this.fileList.push(param.file)
      // 只取列表中的最后一个值
      this.fileList = this.fileList.slice(-this.maxFileNum)
      // this.showResult = false
      this.loading = true
      var urls = []
      const s_time = new Date().getTime()
      for (var element of this.fileList) {
        this.fileName = element.name
        const form = new FormData()
        form.append('file', element)
        form.append('origin', 'pdf-cv-demo')
        const axios_config_upload = {
          method: 'post',
          headers: { 'Cache-Control': 'no-cache', ...getHeaders() },
          url: this.url.value + '/layout/scheduler/samplecollect/upload',
          data: form,
          responseType: 'json',
          processData: false,
          contentType: false
        }
        // 上传
        await axios(axios_config_upload).then(res => {
          const url = res.data.data.url
          this.beforeCreate(url)
          urls.push(url)
        })
      }
      // 请求
      // const status = await this.examextract(urls)
      // create请求
      const create_docID = await this.examextractCreate(urls)
      if (create_docID !== undefined) {
        // 等待1s
        await sleep(1000)
        // query请求
        await this.waitDocStatus(create_docID)
      }
      this.loading = false
      // return Promise.resolve()
      const e_time = new Date().getTime()
      this.statistic = {
        // status: this.status,
        status,
        duration: this.result.duration || (e_time - s_time) / 1000
      }
      return Promise.resolve()
    },
    // 相应的请求体设置
    formulaCreate(urls) {
      // 对传入的url进行处理
      // let urlString = urls[0].toString()
      // urlString = urlString.replace(/-internal/g, '')
      const urlString = urls[0].toString()
      var data_dict = {}
      data_dict = {
        'imgs': [{
          'index': 0,
          'url': urlString,
          'imgType': 'png',
          'dpi': 144
        }]
      }
      return new Promise((resolve, reject) => {
        axios({
          method: 'post',
          headers: { 'Cache-Control': 'no-cache', 'Content-Type': 'application/json', ...getHeaders() },
          url: this.url.gray + '/layout/scheduler/pic2latex/create',
          data: data_dict,
          responseType: 'json',
          processData: false,
          contentType: false,
          timeout: 80000 // 设置超时时间为80秒
        }).then(res => {
          if (res.data.code !== 200 || res.data.data === undefined || res.data.data.docID === undefined) {
            return Promise.reject('转化请求失败!')
          }
          // 处理完成得到string类型的公式
          console.log('create完成: ' + JSON.stringify(res.data.data.docID))
          resolve(res.data.data.docID)
        }).catch((error) => {
          this.$message({
            showClose: true,
            message: '文件处理失败! ' + error,
            center: true,
            duration: 4000,
            type: 'error'
          })
          // return Promise.resolve(0)
          reject(error)
        })
      })
    },
    async waitDocStatus(docID) {
      await axios_wait_doc({
        method: 'get',
        headers: { 'Cache-Control': 'no-cache', ...getHeaders() },
        url:
        // this.url.gray + '/layout/scheduler/pic2latex/query?docID=' + docID,
        // this.url.gray + '/alpha/layout/scheduler/paper_cut/query?docID=' + docID + '&getExtend=' + this.getExtend,
        // this.url.gray + '/layout/scheduler/paper_cut/query?docID=' + docID,
        // this.url.gray + this.grayUrl + '/query?docID=' + docID,
        this.grayUrl + '/query?docID=' + docID,
        handleProgress: this.progressPass()
      }).then(async(res) => {
        if (res === undefined) {
          this.status = -1
          return Promise.resolve(-1)
        }
        // 多页中有一项错误，不能算作整体状态码，需要去内部看错误信息
        if (res.data.status === 4) {
          console.log('错误信息:', res.data.pages_err[0].msg)
          this.$message({
            showClose: true,
            message: '文件处理失败! ' + res.data.pages_err[0].msg,
            center: true,
            duration: 4000,
            type: 'info'
          })
          this.status = 0
          return Promise.resolve(0)
        }

        // 处理完成得到string类型的公式
        console.log('处理完成，响应码为: ' + JSON.stringify(res.code))

        // 没有检测到公式!!!!!!!!!!!!
        // if (res.data.success[0][0] === undefined) {
        //   this.$message({
        //     showClose: true,
        //     message: '没有检测到公式',
        //     center: true,
        //     duration: 4000,
        //     type: 'info'
        //   })
        //   return Promise.resolve(1)
        // }

        // // 拿layout返回的矫正后的图片
        // if (res.data.extend_info.pageInfoMap[0].layoutAPIResult.shape.origin_change_image !== '') {
        //   this.originUrl = res.data.extend_info.pageInfoMap[0].layoutAPIResult.shape.origin_change_image
        // }

        // 服务耗时
        this.resultTime.push(
          {
            value: res.data.duration / 1000,
            comment: '接口返回的耗时：'
          })

        // 获取json_url
        try {
          // const json_url = res.data.json_url.replace(/http:\/\/zhai-datas.ks3-cn-beijing-internal\.ksyun\.com/g, 'http://zhai-datas.ks3-cn-beijing.ksyun.com')
          var json_url = res.data.json_url.replace(/^http:\/\/zhai-datas\.ks3-cn-beijing(-internal)?\.ksyuncs\.com/, '/kso-datas')
          json_url = json_url.replace(/^http:\/\/zhai-datas\.ks3-cn-beijing(-internal)?\.ksyun\.com/, '/kso-datas')
          console.log('json_url:', json_url)
          const resp = await axios.get(json_url)
          // this.jsonData = resp.data
          console.log('resp.data:', resp.data)
          this.jsonData = resp.data
        } catch (error) {
          this.$message({
            showClose: true,
            message: '获取结果json_url内容失败! ' + error,
            center: true,
            duration: 4000,
            type: 'warn'
          })
          this.status = 0
          return Promise.resolve(0)
        }

        // 试卷题目区域
        var result_topic = []
        var result_sub_topic = []
        // question类型
        var result_type_question = []
        var result_sub_type_question = []
        // 存储学科信息
        this.course_info = this.jsonData[0].course_info
        var blocks = this.jsonData[0].blocks
        // this.course_info = res.data.extend_info.pageInfoMap[0].paperCutResult.course_info // 算法返回的结果
        // var blocks = res.data.extend_info.pageInfoMap[0].paperCutResult.blocks
        // 存储试卷切题服务返回原始数据
        this.papercutData = this.jsonData
        // this.papercutData = res.data.extend_info.pageInfoMap[0].paperCutResult
        for (const b in blocks) {
          // sentences可能有多个
          var block = blocks[b]
          // 获取题目区域
          this.getTopicRectangle(block.box, this.originalRectanglesLatex, this.rectanglesLatex)
          // 获取type信息
          if (block.item.question_stem.type !== undefined) {
            result_type_question.push(block.item.question_stem.type)
          }
          // 获取text信息
          var text_question = []
          this.getTextInfos(block.item.question_stem.sentences, text_question)
          // 层级
          if (block.subquestion.length !== 0 && block.subquestion !== undefined) {
            block.subquestion.forEach(sub => {
              // 获取type信息
              if (sub.item.question_stem.type !== undefined) {
                result_sub_type_question.push(sub.item.question_stem.type)
              }
              // 获取题目区域
              this.getTopicRectangle(sub.box, this.originalRectanglesSub, this.rectanglesSub)
              // 获取text信息
              var text_sub_question = []
              this.getTextInfos(sub.item.question_stem.sentences, text_sub_question)
              var text_sub_option = []
              this.getTextInfos(sub.item.option.sentences, text_sub_option)
              var text_sub_answer = []
              this.getTextInfos(sub.item.answer.sentences, text_sub_answer)
              result_sub_topic.push({ question: text_sub_question, option: text_sub_option, answer: text_sub_answer })
            })
          }
          // 获取text信息
          var text_option = []
          this.getTextInfos(block.item.option.sentences, text_option)
          var text_answer = []
          this.getTextInfos(block.item.answer.sentences, text_answer)
          result_topic.push({ question: text_question, option: text_option, answer: text_answer })
        }

        // 公式原文
        this.originData = result_topic
        this.originSubData = result_sub_topic
        // 渲染左上角字体内容
        this.fontText = result_type_question
        this.subfontText = result_sub_type_question

        // 临时方案：重新触发绘制公式框
        this.zoomOut()
        // // 绘制框
        // this.drawRectangles(this.backupUrl)

        // 显示结果
        this.showResult = true
        this.status = 1

        return Promise.resolve(1)
      })
    },
    getTextInfos(sentences, result_text) {
      // 获取text信息
      if (sentences !== undefined && sentences !== null) {
        // var text_question = []
        sentences.forEach(sen => {
          // text_question.push(sen.text)
          result_text.push(sen.text)
        })
      }
    },
    getTopicRectangle(box, originalRectangles, rectangles) {
      // 获取题目区域
      // const rectangle = {
      //   x: sub.box[0],
      //   y: sub.box[1],
      //   width: sub.box[2] - sub.box[0],
      //   height: sub.box[5] - sub.box[1]
      // }
      // this.originalRectanglesSub.push(rectangle)
      const rectangle = {
        x: box[0],
        y: box[1],
        width: box[2] - box[0],
        height: box[5] - box[1]
      }
      originalRectangles.push(rectangle)
      // 根据缩放比例调整矩形框的坐标值
      const scaledRectangle = {
        x: rectangle.x * this.scale,
        y: rectangle.y * this.scale,
        width: rectangle.width * this.scale,
        height: rectangle.height * this.scale
      }
      // this.rectanglesSub.push(scaledRectangle)
      rectangles.push(scaledRectangle)
    },
    // 示例图片
    async tableextractDemo(arrayid, id) {
      // this.showResult = false
      this.resetResult()
      // 开始加载
      this.loading = true

      const file = this.urlFiles[parseInt(arrayid)][parseInt(id)]

      this.fileObj = { name: file.name, size: file.size }
      this.fileName = file.name

      const s_time = new Date().getTime()
      console.log('file.url: ', [file.url])
      this.beforeCreate(file.url)
      // const status = await this.examextract([file.url])
      // create请求
      const create_docID = await this.examextractCreate([file.url])
      if (create_docID !== undefined) {
        // 等待1s
        await sleep(1000)
        // query请求
        await this.waitDocStatus(create_docID)
      }
      // 结束加载
      this.loading = false
      const e_time = new Date().getTime()
      this.statistic = {
        // status: this.status,
        status,
        duration: this.result.duration || (e_time - s_time) / 1000
      }
      return Promise.resolve()
    },
    handleRemove(file, fileList) {
      this.fileList = fileList
    },
    // 将用户选择的文件添加到上传列表
    tableExtractUpload2(param) {
      this.fileList.push(param.file)
    },
    // 存储原始坐标以及缩放比例后的坐标
    getRectangle(box, scale) {
      const result = {
        rectangle: {},
        scaledRectangle: {}
      }

      result.rectangle = {
        x: box[0],
        y: box[1],
        width: box[2] - box[0],
        height: box[5] - box[1]
      }
      result.scaledRectangle = {
        x: result.rectangle.x * scale,
        y: result.rectangle.y * scale,
        width: result.rectangle.width * scale,
        height: result.rectangle.height * scale
      }

      return result
    },
    // 相应的请求体设置
    examextractCreate(urls) {
      // 对传入的url进行处理
      let urlString = urls[0].toString()
      urlString = urlString.replace(/-internal/g, '')
      var data_dict = {}
      data_dict = {
        'imgs': [{
          'index': 0,
          'url': urlString,
          'imgType': 'png',
          'dpi': 144
        }],
        'get_testpaper_level': this.switchTestPaperLevel
      }
      return new Promise((resolve, reject) => {
        axios({
          method: 'post',
          headers: { 'Cache-Control': 'no-cache', 'Content-Type': 'application/json', ...getHeaders() },
          // 试卷切题
          // url: this.url.value + '/layout/formula/block',
          // url: this.url.gray + '/alpha/layout/scheduler/paper_cut/create',
          // url: this.url.gray + '/layout/scheduler/paper_cut/create',
          // url: this.url.gray + this.grayUrl + '/create',
          url: this.grayUrl + '/create',
          data: data_dict,
          responseType: 'json',
          processData: false,
          contentType: false,
          timeout: 80000 // 设置超时时间为80秒
        }).then(res => {
          if (res.data.code !== 200 || res.data.msg !== 'success' || res.data.data === undefined) {
            return Promise.reject('转化请求失败!')
          }

          // 处理完成得到string类型的公式
          console.log('create完成: ' + JSON.stringify(res.data.data.docID))
          resolve(res.data.data.docID)
        }).catch((error) => {
          this.$message({
            showClose: true,
            message: '文件处理失败! ' + error,
            center: true,
            duration: 4000,
            type: 'error'
          })
          reject(error)
        })
      })
    }
  }
}
</script>

<style lang="scss">
// scoped style donot apply to v-html
// choose global style or deep selector
.extracted-table-container{
  overflow-x: auto;
  width: 100%;
  padding-top: 2%;
  padding-bottom: 2%;
  border-bottom: 1px solid #d9d9d9;
  table{
    // colspan-and-table-layoutfixed-break-the-table-style
    table-layout: auto;
    width: fit-content;
    margin: auto;
  }
}
</style>
<style lang="scss" scoped>
@import "~@/styles/mixin.scss";

// 设置滚动条
.formula-row {
  justify-content: center;
  // width: 100%; /* 设置父元素的宽度为100% */
  width: 40%; /* 设置父元素的宽度为100% */
  height: 1000px; /* 设置父元素的固定高度 */
  overflow: auto; /* 使用滚动条展示超过高度的内容 */
}
// 设置公式识别结果展示的样式
.result {
  display: flex;
  justify-content: center;
  // align-items: center;
  align-items: flex-start;
  //height: 100vh; /* 设置高度，使容器占据整个视口 */
}
.result-row {
  justify-content: center;
}

.result-button {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 对话框底部按钮区域 */
.dialog-footer {
  display: flex;
  justify-content: space-between;
}
.reset-button {
  order: 1;
}
.right-buttons {
  order: 2;
}
.container {
  padding-bottom: 2%;
  padding-left: 8%;
  padding-right: 8%;
}
.el-row {
  margin-bottom: 20px;
  &:last-child {
    margin-bottom: 0;
  }
}
.el-col {
  border-radius: 8px;
  border: 1px dashed #d9d9d9;
  background-color: #ffffff;
  color: #666666;
  padding: 1%;
}
.grid-content {
  border-radius: 8px;
  vertical-align: middle;
}

.upload-container {
  width: 100%;
  padding: 1%;
  position: relative;
  @include clearfix;
  .params-container {
    width: 50%;
    display: inline-block;
    padding-left: 1%;
    padding-right: 1%;
  }
  .params {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }
  .time-params {
    align-items: center;
    justify-content: space-between;
  }
  .param-label {
    width: 80%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-bottom: 2%;
  }
  .param-input {
    width: 50%;
    text-align: right;
    margin-bottom: 2%;
  }
}

.download-doc-btn{
    text-align: center;
    width: 100%;
    height: 4vh;
    font-size: 2vh;
    line-height: 4vh;
    color: #fff;
    background-color: #338ef0;
    border: none;
    border-radius: 4px;
}

.download-doc-btn a{
    text-decoration: none;
    color: #fff;
}

.canvas-container {
  position: relative;
  // width: 100%; /* 设置父元素的宽度为100% */
  width: 600px;
  height: 1000px; /* 设置父元素的固定高度 */
  overflow: auto; /* 使用滚动条展示超过高度的内容 */
}

.canvas-container canvas {
  position: absolute;
}

</style>
