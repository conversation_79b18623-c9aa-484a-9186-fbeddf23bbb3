/* eslint-disable no-unused-vars */
import axios from 'axios'
import store from '@/store'
import { getToken } from '@/utils/auth'
import { Message, MessageBox } from 'element-ui'
import service from '@/utils/request'

export const axios_transform = axios.create({
  baseURL: process.env.VUE_APP_TRANSFORM_BASE_API,
  timeout: 10000
})

// http request 拦截器
axios_transform.interceptors.request.use(
  config => {
    if (store.getters.token) {
      // let each request carry token
      // ['X-Token'] is a custom headers key
      // please modify it according to the actual situation
      config.headers['Authorization'] = 'Bearer ' + getToken()
      config.headers['Content-Type'] = 'application/json'
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// http response 拦截器
axios_transform.interceptors.response.use(
  async response => {
    const code = response.data.code
    if (code === 401) {
      if (response.data.msg !== undefined && response.data.msg === 'Token is expired') {
        // refresh_token
        const result = await store.dispatch('user/refreshToken'
        ).then(() => {
          console.log('refresh_token success')
          return service(response.config)
        }).then(retryRes => {
          return retryRes
        }).catch(error => {
          console.log(error)
          return undefined
        })
        console.log('retry result,', result)
        if (result !== undefined) {
          return result
        }
      }
      store.dispatch('user/resetToken')
      if (location.href.indexOf('login') !== -1) {
        location.reload() // 为了重新实例化vue-router对象 避免bug
      } else {
        MessageBox.confirm(
          '登录状态已过期，您可以继续留在该页面，或者重新登录!',
          '系统提示',
          {
            confirmButtonText: '重新登录',
            cancelButtonText: '取消',
            type: 'warning'
          }
        ).then(() => {
          location.reload() // 为了重新实例化vue-router对象 避免bug
        })
      }
    } else if (code === 6401) {
      const result = await store.dispatch('user/refreshToken'
      ).then(() => {
        console.log('refresh_token success')
        return service(response.config)
      }).then(retryRes => {
        console.log('充实结果', retryRes)
        return retryRes
      }).catch(error => {
        console.log(error)
        return undefined
      })
      console.log('retry result,', result)
      if (result !== undefined) {
        return result
      }
      MessageBox.confirm(
        '登录状态已过期，您可以继续留在该页面，或者重新登录。',
        '系统提示',
        {
          confirmButtonText: '重新登录',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {
        location.reload() // 为了重新实例化vue-router对象 避免bug
      })
      return false
    } else if (code === 400 || code === 403) {
      Message({
        message: response.data.msg,
        type: 'error',
        duration: 5 * 1000
      })
    } else if (code !== 200) {
      // Notification.error({
      //   title: response.data.msg
      // })
      Message({
        message: response.data.msg,
        type: 'error'
      })
      return Promise.reject('error')
    } else {
      return response.data
    }
  },
  error => {
    if (error.message === 'Network Error') {
      Message({
        message: '服务器连接异常，请检查服务器！',
        type: 'error',
        duration: 5 * 1000
      })
      return
    }
    console.log('err' + error) // for debug

    Message({
      message: error.message,
      type: 'error',
      duration: 5 * 1000
    })

    return Promise.reject(error)
  }
)

function get(url, params = {}, responseType = 'json') {
  return new Promise((resolve, reject) => {
    axios_transform({
      method: 'get',
      url: url,
      params: params,
      responseType: responseType
    }).then((data) => {
      resolve(data)
    }).catch(err => {
      reject(err)
    })
  })
}

function post(url, data = {}, contentType = 'application/json') {
  return new Promise((resolve, reject) => {
    axios_transform({
      headers: {
        'Content-Type': contentType
      },
      method: 'post',
      url,
      data
    }).then((data) => {
      resolve(data)
    }).catch(err => {
      reject(err)
    })
  })
}

export { get, post }

