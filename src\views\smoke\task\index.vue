<template>
  <BasicLayout>
    <template #wrapper>
      <el-card class="box-card">
        <el-form ref="queryForm" :model="queryParams" :inline="true" label-width="68px">
          <el-form-item label="业务" prop="service">
            <el-select v-model="queryParams.service" placeholder="请选择业务" size="small">
              <el-option
                v-for="item in serviceOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
            >新增
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              icon="el-icon-switch-button"
              size="mini"
              :disabled="multiple"
              @click="handleCancel"
            >取消
            </el-button>
          </el-col>
          <el-col :span="3">
            <el-button
              type="primary"
              icon="el-icon-data-analysis"
              size="mini"
              :disabled="multiple"
              @click="handleAnalysis"
            >性能报告
            </el-button>
          </el-col>
        </el-row>
        <div class="smoke-table">
          <el-table v-loading="loading" :data="taskList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" /><el-table-column
              label="任务"
              align="center"
              prop="name"
              :show-overflow-tooltip="true"
            /><el-table-column
              label="业务"
              align="center"
              prop="service"
              width="150"
              :show-overflow-tooltip="true"
            />
            <el-table-column
              label="host"
              align="center"
              prop="host"
              width="300"
              :show-overflow-tooltip="true"
            />
            <el-table-column
              label="接口"
              align="center"
              prop="uri"
              :show-overflow-tooltip="true"
            />
            <el-table-column label="备注" align="center" prop="remark" :show-overflow-tooltip="true" />
            <el-table-column
              label="执行时间"
              align="center"
              prop="exec_time"
              :show-overflow-tooltip="true"
            ><template slot-scope="scope">
              <span>{{ convertLongtime(scope.row.exec_time) }}</span>
            </template>
            </el-table-column>
            <el-table-column label="创建者" align="center" prop="creater" :show-overflow-tooltip="true" />
            <el-table-column
              label="状态"
              width="80"
              sortable="custom"
              prop="status"
            ><template slot-scope="scope">
              <span>{{ convertStatus(scope.row.status) }}</span>
            </template>
            </el-table-column>
            <el-table-column label="详情" align="center">
              <template slot-scope="scope">
                <SmokeInfoData
                  :id="scope.row.id"
                  :key="scope.row.id"
                  function="task"
                />
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
              <template slot-scope="scope">
                <el-popconfirm
                  class="delete-popconfirm"
                  title="确认要复制任务吗?"
                  confirm-button-text="复制"
                  @onConfirm="handleCopy(scope.row)"
                >
                  <el-button
                    slot="reference"
                    size="mini"
                    type="text"
                    icon="el-icon-document-copy"
                  >复制
                  </el-button>
                </el-popconfirm>
                <el-popconfirm
                  class="delete-popconfirm"
                  title="确认要修改吗?"
                  confirm-button-text="修改"
                  @onConfirm="handleUpdate(scope.row)"
                >
                  <el-button
                    slot="reference"
                    size="mini"
                    type="text"
                    :disabled="scope.row.status != 0"
                    icon="el-icon-edit"
                  >修改
                  </el-button>
                </el-popconfirm>
                <el-popconfirm
                  class="delete-popconfirm"
                  title="确认要取消吗?"
                  confirm-button-text="取消"
                  @onConfirm="handleCancel(scope.row)"
                >
                  <el-button
                    slot="reference"
                    size="mini"
                    type="text"
                    :disabled="!canCancel(scope.row)"
                    icon="el-icon-switch-button"
                  >取消
                  </el-button>
                </el-popconfirm>
                <el-popconfirm
                  class="delete-popconfirm"
                  title="确认要下载吗?"
                  confirm-button-text="下载"
                  @onConfirm="handleDownload(scope.row)"
                >
                  <el-button
                    slot="reference"
                    size="mini"
                    type="text"
                    :disabled="!hasResult(scope.row)"
                    icon="el-icon-download"
                  >结果
                  </el-button>
                </el-popconfirm>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.page_index"
          :limit.sync="queryParams.page_size"
          @pagination="getList"
        />
        <SmokeCreateDialog
          :task-id="taskId"
          :open.sync="open"
          mode="task"
          :title="title"
          :update="update"
          :form-options="formOptions"
          :service-options="serviceOptions"
          @close="cancel"
        />
      </el-card>
    </template>
  </BasicLayout>
</template>

<script>
import SmokeInfoData from '@/components/Smoke/SmokeInfoData'
import SmokeCreateDialog from '@/components/Smoke/SmokeCreateDialog'
import axios from 'axios'

export default {
  name: 'SmokeTask',
  components: {
    SmokeInfoData,
    SmokeCreateDialog
  },
  data() {
    return {
      statusOptions: [],

      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      configEdit: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 类型数据字典
      taskList: [],
      // 查询参数
      queryParams: {
        page_index: 1,
        page_size: 20,
        service: undefined
      },
      // 获取形式参数
      formOptions: [],
      // 业务类型
      serviceOptions: [],
      // 表单校验
      rules: {
        title: [{ required: true, message: '任务不能为空', trigger: 'blur' }],
        status: [{ required: true, message: '状态不能为空', trigger: 'blur' }]
      },
      taskId: undefined,
      // title: '',
      update: false
    }
  },
  created() {
    this.getList()
    this.getDicts('pdf_smoke_status').then((response) => {
      this.statusOptions = response.data
    })
    this.getDicts('pdf_smoke_form').then((response) => {
      this.formOptions = response.data
    })
    this.getDicts('pdf_smoke_service').then((response) => {
      this.serviceOptions = response.data
    })
  },
  methods: {
    setCellStyle() {
      return 'white-space: pre-wrap;'
    },
    convertStatus(item) {
      return this.selectDictLabel(this.statusOptions, item)
    },
    convertLongtime(time) {
      if (time === 0) {
        return ''
      }
      var date = new Date(time * 1000)
      var DD = String(date.getDate()).padStart(2, '0') // 获取日
      var MM = String(date.getMonth() + 1).padStart(2, '0') // 获取月份，1 月为 0
      var yyyy = date.getFullYear() // 获取年
      var hh = String(date.getHours()).padStart(2, '0') // 获取当前小时数(0-23)
      var mm = String(date.getMinutes()).padStart(2, '0') // 获取当前分钟数(0-59)
      var ss = String(date.getSeconds()).padStart(2, '0') // 获取当前秒数(0-59)
      var today = yyyy + '-' + MM + '-' + DD + ' ' + hh + ':' + mm + ':' + ss
      return today
    },
    /** 查询参数列表 */
    getList() {
      this.loading = true
      axios.post('http://10.213.40.66:8001/data-processing/smoke/tasks/search', this.queryParams).then((response) => {
        this.taskList = response.data.data.tasks
        this.total = response.data.data.total
        this.loading = false
      })
    },
    // 对话框取消
    cancel() {
      this.taskId = undefined
      this.handleQuery()
    },
    getImgList: function() {
      this.form[this.fileIndex] = this.$refs['fileChoose'].resultList[0].fullUrl
    },
    // 关系
    // 文件
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageIndex = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.getList()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.open = true
      this.title = '创建任务'
      this.update = false
      this.taskId = undefined
    },
    /** 取消按钮操作 */
    handleCancel(row) {
      let id = row.id || this.ids
      if (!Array.isArray(id)) {
        id = [id]
      }
      const data = {
        task_ids: id
      }
      axios.post('http://10.213.40.66:8001/data-processing/smoke/tasks/batch_cancel', data).then((response) => {
        if (response.data.code === 200) {
          this.msgSuccess(response.data.msg)
          this.getList()
        } else {
          this.msgError(response.data.msg)
        }
      }).catch((error) => {
        this.msgError(error.response.data.msg)
      })
    },
    // 性能报告
    handleAnalysis() {
      const id = this.ids
      const data = {
        task_ids: id
      }

      axios.post('/data-processing/smoke/tasks/analysis', data).then((response) => {
        if (response.data.code === 200) {
          const reaultUrl = response.data.data.result_url
          const link = document.createElement('a')
          link.href = reaultUrl
          link.target = '_blank'
          link.click()
          this.getList()
        } else {
          this.msgError(response.data.msg)
        }
      }).catch((error) => {
        this.msgError(error.response.data.msg)
      })
    },
    /** 下载按钮操作 */
    handleDownload(row) {
      axios.get(`http://10.213.40.66:8001/data-processing/smoke/tasks/result`, { params: { task_id: row.id }}
      ).then((response) => {
        if (response.data.code === 200) {
          const reaultUrl = response.data.data.result_url
          const link = document.createElement('a')
          link.href = reaultUrl
          link.target = '_blank'
          link.click()
        } else {
          this.msgError(response.data.msg)
        }
      }).catch((error) => {
        this.msgError(error.response.data.msg)
      })
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.title = '修改任务'
      this.taskId = row.id
      this.update = true
      this.open = true
    },
    handleCopy(row) {
      this.taskId = row.id
      this.open = true
      this.update = false
      this.title = '创建任务'
    },
    canCancel(row) {
      // 非完成状态可取消
      if (row.status < 5) {
        return true
      } else {
        return false
      }
    },
    hasResult(row) {
      if (row.result && row.status !== 8) {
        return true
      } else {
        return false
      }
    }
  }
}
</script>
<style>
.smoke-table .el-table .cell {
    display: inline-block;
    justify-content: center;
    align-items: center;
}
</style>
