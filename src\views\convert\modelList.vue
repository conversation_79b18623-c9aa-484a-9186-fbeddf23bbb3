<template>
  <BasicLayout>
    <template #wrapper>
      <el-card class="box-card">
        <el-form
          ref="queryForm"
          :model="queryParams"
          :inline="true"
          label-width="88px"
        >
          <el-form-item
            label="模型名称"
            prop="modelName"
            ><el-input
              v-model="queryParams.modelName"
              placeholder="请输入模型名称"
              clearable
              size="small"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>

          <el-form-item
            label="负责人"
            prop="device"
            ><el-input
              v-model="queryParams.author"
              placeholder="请输入负责人"
              clearable
              size="small"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>

          <el-form-item label="日期">
            <el-date-picker
              v-model="queryParams.createTime"
              size="small"
              type="date"
              align="right"
              value-format="yyyy-MM-dd"
            />
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select
              v-model="queryParams.status"
              placeholder="状态"
              clearable
              size="small"
            >
              <el-option
                v-for="(dict, index) in statusOptions"
                :key="index"
                :label="dict"
                :value="dict === '全部' ? undefined : index"
              />
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-row :gutter="10" justify="end">
              <el-col :span="8">
                <UploadModelOperation @formSubmitted="handleQuery"/>
              </el-col>

              <el-col :span="8">
                <el-button
                  type="primary"
                  icon="el-icon-search"
                  size="mini"
                  @click="handleQuery"
                  >搜索</el-button>
              </el-col>

              <el-col :span="8">
                <el-button
                  icon="el-icon-refresh"
                  size="mini"
                  type="warning"
                  @click="resetQuery"
                  >重置</el-button>
              </el-col>

            </el-row>
          </el-form-item>
        </el-form>

        <el-table
          v-loading="loading"
          :data="groupedRequestsList"
          span="11.1"
          :cell-style="{ padding: '5' }"
        >
          <el-table-column
            v-if="false"
            type="selection"
            width="1"
            align="left"
          /><el-table-column
            label="模型名称"
            align="center"
            prop="name"
            width="350"
            :show-overflow-tooltip="true"
            :span-method="mergeRows"
          />
          <el-table-column
            label="模型版本"
            align="center"
            prop="version"
            width="300"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="项目名"
            align="center"
            prop="project"
            width="130"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="日期"
            align="center"
            prop="create_time"
            width="180"
            :show-overflow-tooltip="true"
            :formatter="formatDate"
          />
          <el-table-column
            label="负责人"
            align="center"
            prop="author"
            width="180"
            :show-overflow-tooltip="true"
          />
          <el-table-column label="状态" align="center" prop="status" width="80">
            <template slot-scope="scope">
              <el-tag
                :type="getTagType(scope.row.status)"
                effect="plain"
                size="mini"
              >
                {{ statusFormat(scope.row) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column
            label="设备"
            align="center"
            prop="device"
            width="120"
            :show-overflow-tooltip="true"
          />

          <!-- 添加“参数”按钮 -->
          <el-table-column label="参数" align="center">
            <template slot-scope="scope">
              <el-button
                size="mini"
                :disabled="
                  !scope.row.parameters || scope.row.parameters === 'null'
                "
                @click="showParameters(scope.row.parameters)"
              >参数</el-button>
            </template>
          </el-table-column>

          <el-table-column
            label="下载"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-download"
                @click="handleDowloadModel(scope.row, scope.row.name, 0)"
                >onnx</el-button
              >

              <el-button
                v-if="scope.row.status === 1"
                size="mini"
                type="text"
                icon="el-icon-download"
                @click="handleDowloadModel(scope.row, ['plan'], 1)"
                >plan</el-button>

              <el-button
                v-if="scope.row.status === 1"
                size="mini"
                type="text"
                icon="el-icon-download"
                @click="handleDowloadModel(scope.row, downloadFilename, 3)"
                >更多</el-button>
              
            </template>
          </el-table-column>

          <el-table-column
            label="重新转换"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-popconfirm
                class="delete-popconfirm"
                title="确认要重新转换吗?"
                confirm-button-text="确定"
                @onConfirm="handleUpdate(scope.row)"
              >
                <el-button
                  slot="reference"
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                  >重新转换
                </el-button>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>

        <!-- 弹窗显示 JSON 数据 -->
        <el-dialog
          title="参数详情"
          :visible.sync="parameterDialogVisible"
          width="500px"
        >
          <pre
            style="background-color: #f8f8f8; padding: 10px; border-radius: 4px;overflow-y: auto;max-height: 600px;"
            >{{ formattedParameters }}</pre>
          <span slot="footer" class="dialog-footer">
            <el-button @click="parameterDialogVisible = false">关闭</el-button>
          </span>
        </el-dialog>

        <!-- 下载对话框 -->
        <el-dialog
          :title="downloadTitle"
          :visible.sync="downloadOpen"
          width="500px"
          @before-close="handleDialogClose"
        >
          <el-form
            ref="downlod_form"
            :model="form"
            :rules="rules"
            label-width="80px"
          >
            <el-form-item label="模型" prop="name">
              <div>
                <el-input v-model="form.name" disabled />
              </div>
            </el-form-item>
            <el-form-item label="目录" prop="object_key">
              <div>
                <el-input
                  v-model="form.object_key"
                  type="textarea"
                  disabled
                  style="width: 380px; height: 50px; overflow-y: auto"
                />
              </div>
            </el-form-item>
            <el-form-item label="用途描述" prop="purposes">
              <el-input
                v-model="form.purposes"
                placeholder="用途"
                type="textarea"
              />
            </el-form-item>

            <el-form-item label="审批人" prop="approver">
              <div>
                <el-radio
                  v-for="option in approver_list"
                  :key="option.value"
                  v-model="form.approver"
                  :label="option.value"
                >
                  {{ option.label }}
                </el-radio>
              </div>
            </el-form-item>
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" placeholder="" type="textarea" />
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="handleSubmit">确 定</el-button>
            <el-button @click="downloadCancel">取 消</el-button>
          </div>
        </el-dialog>

        <!-- 修改状态对话框 -->
        <el-dialog
          :title="updateTitle"
          :visible.sync="updateOpen"
          width="1000px"
        >
        <el-form ref="form" :model="updateForm" label-width="80px">
        <div>自定义参数
          <span class="custom-hint">【参数无需括号，使用英文逗号分隔参数】</span>
        </div>

          <div>
            <el-form-item label="动态尺寸">
              <el-button
                type="primary"
                @click="addDynamicConfig"
              >添加动态配置</el-button>
            </el-form-item>

            <!-- 动态配置组列表 -->
            <div
              v-for="(config, index) in updateForm.parameters.dynamic_configs"
              :key="index"
              style="margin-bottom: 20px"
            >
              <el-form-item :label="'配置 ' + (index + 1)">
                <el-container style="display: flex; align-items: center">
                  <el-input
                    v-model="config.name"
                    size="small"
                    placeholder="指定层名称"
                    style="width: 120px; margin-right: 10px"
                  />
                  <el-input
                    v-model="config.min"
                    size="small"
                    placeholder="最小形状 (格式: 1,3,640,640)"
                    style="width: 200px; margin-right: 10px"
                  />
                  <el-input
                    v-model="config.opt"
                    size="small"
                    placeholder="理想形状 (格式: 1,3,640,640)"
                    style="width: 200px; margin-right: 10px"
                  />
                  <el-input
                    v-model="config.max"
                    size="small"
                    placeholder="最大形状 (格式: 1,3,640,640)"
                    style="width: 200px; margin-right: 10px"
                  />
                  <el-button
                    type="danger"
                    icon="el-icon-delete"
                    @click="removeDynamicConfig(index)"
                  />
                </el-container>
              </el-form-item>
            </div>
          </div>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="updateSubmit">确 定</el-button>
            <el-button @click="updateCancel">取 消</el-button>
          </div>
        </el-dialog>

        <!-- 文件预览 -->
        <el-dialog :visible.sync="downloadPreviewOpen" title="选择文件进行预览"  width="800px" @close="handlePreviewDialogClose"
        >
       
          
        <div>
          <!-- 文件选择列表 -->
          <el-radio-group v-model="selectedFile" @change="previewFile">
              <el-radio v-for="(fileObj, index) in fileLinks" :key="index" :label="fileObj.file" :disabled="!fileObj.success">
                {{ fileObj.file.split('?')[0].split('/').pop() }}
                <span v-if="fileObj.success" >✔️</span>
              </el-radio>
          </el-radio-group>
          
          <!-- 文件预览内容 -->
          <div  style="margin-top: 20px;position: relative; min-height: 150px;" v-loading="isPreviewLoading" 
            element-loading-text="加载中..." 
            element-loading-spinner="el-icon-loading"> 
            <h4>预览内容</h4>
            <div v-if="!previewContent" class="empty-message">
              数据为空，请选择文件。
            </div>

            <!-- 加载完成后显示文件内容 -->
            <div v-else>
              <pre class="file-preview">{{ previewContent }}</pre>
            </div>
           
          </div>
        </div>
        <div slot="footer">
          <el-button v-if="previewContent" type="primary" @click="downloadPreviewFile">下载</el-button>
          <el-button @click="handlePreviewDialogClose ">关闭</el-button>
        </div>
      </el-dialog>

        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageIndex"
          :limit.sync="queryParams.pageSize"
          @pagination="handleQuery"
        />
      </el-card>
    </template>
  </BasicLayout>
</template>

<script>
import {
  getModelList,
  getObjWithSuffix,
  updateConvert,
} from '@/api/convert/convert'
import { getUserProfile } from '@/api/admin/sys-user'
import UploadModelOperation from '@/components/Convert/UploadModel.vue'
import { addUpload2Ks3 } from '@/api/tools/upload2ks3'
import axios from 'axios'

export default {
  name: 'ModelList',
  components: {
    UploadModelOperation
  },
  data() {
    return {
      // 遮罩层
      loading: true,

      // 总条数
      total: 0,

      // 是否显示弹出层
      open: false,
      isEdit: false,
      showErrMsgs: false,

      requestsList: [],
      statusOptions: [
        '未转换',
        '已转换',
        '就绪',
        '正在转换',
        '转换失败',
        '全部'
      ],
      // 查询参数
      queryParams: {
        modelName: '', // 模型名称
        author: '',

        createTime: '', // 创建时间
        status: undefined, // [0:"未转换",1:"已转换",2:"就绪",3:"正在转换",4:"转换失败",undefined:"全部"]
        pageIndex: 1,
        pageSize: 10
      },
      // 表单参数
      form: {
        name: '',
        object_key: '',
        purposes: '',
        approver: '',
        remark: ''
      },
      isUploadPanelVisible: false, // 将变量设为 true 显示上传界面
      fileUpLoading: false, // 文件控制加载状态
      fileList: [], // 存储上传的文件列表
      parameterDialogVisible: false,
      formattedParameters: '',
      downloadOpen: false,
      downloadTitle: '',
      // 表单校验
      rules: {
        name: [
          {
            required: true,
            message: '名称不能为空',
            trigger: ['blur', 'change']
          }
        ],
        object_key: [
          {
            required: true,
            message: '地址不能为空',
            trigger: ['blur', 'change']
          }
        ],

        approver: [
          {
            required: true,
            message: '审批人必选',
            trigger: ['blur', 'change']
          }
        ],
        purposes: [
          {
            required: true,
            message: '下载模型目的不能为空',
            trigger: ['blur', 'change']
          }
        ]
      },
      approver_list: [],
      is_onnx: false,
      submit_type: 0, // 0 代表下载onnx文件;1 代表需要权限下载多个结果文件：result.json/pbtxt/plan/triton_throughput.txt; 2 代表复制路径; 3 代表无需权限下载多个结果文件
      download_path: '',
      suffix: '',
      is_query_all: false,
      user_email: '',
      updateForm: { id: undefined, status: undefined ,parameters: {
          dynamic_configs: []
        }},
      updateOpen: false,
      updateTitle: '数据修改',
      downloadPreviewOpen: false,
      fileLinks: [],
      selectedFile: null, // 选择的文件
      previewContent: "", // 文件预览内容
      VUE_APP_BASE_API: process.env.VUE_APP_BASE_API.replace('/api', ''),
      isPreviewLoading:false,
      downloadFilename: [ 'pbtxt', 'result.json', 'error.json', 'triton_throughput.txt']

    };
  },
  computed: {
    // 格式化数据以支持表格分组
    groupedRequestsList() {
      return this.requestsList
    },
  },
  async created() {
    await this.getUserInfo()
    this.getList()
    this.getDicts('model_approver_list').then((response) => {
      this.approver_list = response.data;
    })
  },
  methods: {
    async getUserInfo() {
    try {
      const response = await getUserProfile();  // 使用 await 等待 getUserProfile 完成
      if (response.code==200){
           var roleArray = response.data.user.rolekeys
            if (roleArray.includes('admin') || roleArray.includes('convert_query')) {
              this.is_query_all = true
            }
            this.user_email = response.data.user.email
      }else{
        throw new Error(response.msg || 'An error occurred.') // 抛出错误
      }
    } catch (error) {
      this.$message({
          message: '获取用户信息失败',
          type: 'error',
          duration: 3000
        })
      console.log("Error fetching user info:", error.message)
    }
  },
    /** 查询参数列表 */
    getList() {
      this.loading = true

      this.handleQuery()
      this.loading = false
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined
      }
      this.resetForm('form')
    },

    statusFormat(row) {
      return this.statusOptions[row.status] || '未知状态'
    },

    // 获取状态的样式类型（el-tag 的 type 属性）  //statusOptions: ['未转换','已转换','就绪','正在转换','转换失败','全部',],
    getTagType(status) {
      switch (status) {
        case 0: // 未转换
          return 'info' // 蓝色边框
        case 1: // 已转换
          return 'success' // 绿色边框
        case 2: // 就绪
          return 'primary' // 深蓝色边框
        case 3: // 正在转换
          return 'warning' // 橙色边框
        case 4: // 转换失败
          return 'danger' // 红色边框
        default:
          return '' // 默认无样式
      }
    },

    // 线上后端接口
    async handleQuery() {
      this.loading = true
      // 组装查询参数，映射到后端的 ConvertGetPageReq

      const params = {
        name: this.queryParams.modelName,
        author: this.is_query_all ? this.queryParams.author : this.user_email, //非管理员只能看到自己的数据
        status: this.queryParams.status,
        create_time: this.queryParams.createTime
          ? new Date(this.queryParams.createTime).getTime() / 1000
          : '',
        update_time: '',
        pageIndex: this.queryParams.pageIndex,
        pageSize: this.queryParams.pageSize
      }
      try {
        // 调用接口获取数据

        const response = await getModelList(params)
        // console.log('handleQuery response:', response)
        this.requestsList = response.data.list
        this.total = response.data.count
      } catch (error) {
        console.error('查询失败:', error)
        this.$message.error('查询失败，请重试')
      } finally {
        this.loading = false
      }
    },

    handleDialogClose() {
      this.approvalFromReset()
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.queryParams = {
        modelName: '',
        author: '',
        createTime: '',
        status: undefined,
        pageIndex: 1,
        pageSize: 10
      },
        this.handleQuery()
    },

    formatDate(row) {
      const timestamp = row.create_time

      if (!timestamp) return '' // 防止空值错误

      const date = new Date(timestamp * 1000)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}/${month}/${day}`
    },

    mergeRows({ row, column, rowIndex }) {
      if (column.property === 'name') {
        const name = row.name
        let count = 0

        for (let i = rowIndex; i < this.groupedRequestsList.length; i++) {
          if (this.groupedRequestsList[i].name === name) {
            count++
          } else {
            break
          }
        }

        return {
          rowspan: count,
          colspan: count ? 1 : 0
        }
      }
    },

    cleanFileUrl(fileUrl) {
      fileUrl = fileUrl.replace(
        'http://zhai-datas.ks3-cn-beijing.ksyun.com/',
        ''
      )
      fileUrl = fileUrl.replace(
        'http://zhai-datas.ks3-cn-beijing-internal.ksyun.com/',
        ''
      )
      fileUrl = fileUrl.replace(
        'https://zhai-datas.ks3-cn-beijing.ksyuncs.com/',
        ''
      )

      fileUrl = fileUrl.split('?')[0]
      return fileUrl
    },
    cleanResultFileUrl(fileUrl) {
      fileUrl = fileUrl.replace('ks3://zhai-datas/', '')
      return fileUrl
    },
    // 复制链接
    async copyToClipboard() {
      // fileUrl = this.cleanFileUrl(fileUrl)
      this.download_path = this.form.object_key
      await this.downloadSubmitForm()
    },

    // 有权限弹窗
    async listPrefixes(is_permission) {
      const fileUrl = this.cleanResultFileUrl(this.form.object_key)
      try {
        const response = await getObjWithSuffix({
          url: fileUrl,
          suffix: this.suffix
        })

        // Check if the response code is not 200, show an error message
        if (response.code !== 200) {
          this.$message({
            message: response.msg || 'An error occurred.',
            type: 'error',
            duration: 3000
          })
          console.log('获取文件 失败:', response.msg)
          throw new Error(response.msg || 'An error occurred.') // 抛出错误
        }

        const { result, failList } = response.data

        if (is_permission) {
          if (failList.length > 0) {
            this.$message({
              message: `以下文件获取失败: ${failList.join(', ')}`,
              type: 'error',
              duration: 5000
            })
            this.downloadOpen = false
          }
          
          for (let linkUrl of result) {  
            linkUrl = this.cleanFileUrl(linkUrl)
            this.download_path = linkUrl;
            // 等待 downloadSubmitForm 完成
            await this.downloadSubmitForm()
          }
          
        } else {
          // 无需权限
          this.downloadPreviewOpen = true
          this.fileLinks = result.map(file => ({ file, success: true }))
          failList.forEach(file => {
            this.fileLinks.push({ file, success: false })
          })
        }
      } catch (error) {
        console.log('Error retrieving files:', error.message)
        this.$message({
          message: '查找文件失败,后台可能没有该文件!',
          type: 'error',
          duration: 3000
        })
        throw error
      } finally {
        // 隐藏加载提示
        // this.$message.closeAll(); // 关闭所有提示消息
        // this.resetForm("dowloadForm");
      }
    },
    approvalFromReset() {
      this.submit_type = 0
      this.suffix = ''
      this.download_path = ''
      this.form = {
        object_key: '',
        purposes: '',
        approver: '',
        name: ''
      }
    },

    // 提交下载权限表单
    async downloadSubmitForm() {
      if (this.submit_type === 0) {
        this.download_path = this.form.object_key
      }
      const isValid = await this.$refs['downlod_form'].validate()
      if (isValid) {
        // 构造数据
        const data = {
          purposes: this.form.purposes,
          application: 3,
          approver: this.form.approver,
          is_encrypt: 0,
          status: 0,
          default_storage: 1,
          specify_storage: this.download_path,
          object_key: this.download_path,
          path: this.download_path,
          remark: this.form.remark
        }

        try {
          // 调用上传审批接口
          const response = await addUpload2Ks3(data)

          if (response.code === 200) {
            this.msgSuccess(response.msg)
            this.downloadOpen = false
          } else {
            this.msgError(response.msg)
            throw new Error(response.msg)
          }
        } catch (error) {
          console.log('Error in downloadSubmitForm:', error.message)
          throw error // 将错误抛出，供外部捕获
        }
      } else {
        console.log('表单验证失败')
        // 使用 Element UI 的 $message 提示用户
        this.$message({
          type: 'error',
          message: '表单验证失败，请检查输入项',
          duration: 3000 // 提示框显示时间（毫秒）
        })
      }
    },

    // 下载按钮操作
    async handleDowloadModel(row, fileName, submitType) {
      // this.reset()
      this.submit_type = submitType
      var fileUrl = ''
      if (submitType === 0) {
        // onnx
        this.downloadOpen = true
        this.downloadTitle = '下载申请'
        fileUrl = this.cleanFileUrl(row.model_ks3_key)
        this.form.name = fileName //.onnx
        this.form.object_key = fileUrl
        //弹窗后 等待用户点击确定提交表单 手动触发handleSubmit
      } else if (submitType === 1 || submitType === 2) {
        // 结果文件和复制链接 需要权限
        this.downloadOpen = true
        this.downloadTitle = '下载申请'

        fileUrl = this.cleanResultFileUrl(row.result_ks3_key)
        this.suffix = fileName; //fileName=[.plan]
        this.form.name = row.name + ' |【' + this.suffix + '结尾的文件】'
        this.form.object_key = fileUrl
        //弹窗后 等待用户点击确定提交表单 手动触发handleSubmit
      } else {
        //(submitType===3) 无需权限
        fileUrl = this.cleanResultFileUrl(row.result_ks3_key)
        this.suffix = fileName; //fileName=[pbtxt,result.json,error.json,triton_throughput.txt]
        this.form.object_key = fileUrl;
        //无审批弹窗 自动触发handleSubmit
        await this.handleSubmit();
      }
    },
    showParameters(parameters) {
      this.formattedParameters = JSON.stringify(
        JSON.parse(parameters),
        null,
        2
      ) // 格式化 JSON 数据
      this.parameterDialogVisible = true
    },
    downloadCancel() {
      this.downloadOpen = false
      this.approvalFromReset()
      this.resetForm('dowloadForm')
    },
    async handleSubmit() {
      try {
        if (this.submit_type === 0) {
          await this.downloadSubmitForm()
        } else if (this.submit_type === 1) {
          // 需要权限下载多个文件
          await this.listPrefixes(true)
        } else if (this.submit_type === 2) {
          // submit_type===2 复制链接
          await this.copyToClipboard()
        } else if (this.submit_type === 3) {
          // 不需要权限下载多个文件
          await this.listPrefixes(false)
        }

        this.approvalFromReset()
      } catch (error) {
        console.error('Error in handleSubmit:', error)
      }
    },

    updateCancel() {
      this.updateOpen = false;
    },

    // 打开更新对话框
    handleUpdate(row) {
      this.updateForm.id = row.id;
      this.updateForm.status = row.status;
      this.updateTitle = '数据修改【修改后重新转换】';
      //json字符串转对象
      try {
          if ( row.parameters !== 'null') {
            this.updateForm.parameters = JSON.parse(row.parameters);
          }
      } catch (e) {
        this.$message({
          message: "Failed to parse parameters:"+row.parameters,
          type: 'error',
          duration: 3000,
        });
        console.error('Failed to parse parameters:', e);
        this.updateForm.parameters = { dynamic_configs: [] }; // 解析失败时，设置默认值
      }
      this.updateOpen = true;
    },
    // 提交更新
    async updateSubmit() {
      // 检查当前状态非“已转换”或“转换失败”,则不允许更新
      if (this.updateForm.status !== 1 && this.updateForm.status !== 4) {
        this.$message.error('当前状态不能更新')
        return; // 阻止提交
      }
      // 遍历 dynamic_configs 数组，检查每一个配置项的字段
      for (
        let i = 0;
        i < this.updateForm.parameters.dynamic_configs.length;
        i++
      ) {
        const config = this.updateForm.parameters.dynamic_configs[i]

        if (!config.name || !config.min || !config.opt || !config.max) {
          // 如果 config 的任何一个字段为空，弹出提示
          this.$message({
            type: 'error',
            message: `动态配置项 ${
              i + 1
            } 中有未填写的字段，请检查 "name"、"min"、"opt" 和 "max" 是否都已填写。或者删除配置项`
          })
          // this.submitLoading = false
          return; // 停止提交，提醒用户补充填写
        }
      }
      const formCopy = {
        id: this.updateForm.id,
        status:0,
        parameters_flag:true,
      }

      // 动态添加 parameters 参数
      if (this.updateForm.parameters.dynamic_configs.length > 0) {
        formCopy.parameters = {
          dynamic_configs: this.updateForm.parameters.dynamic_configs.map(
            (config) => {
              return {
                name: config.name ? config.name : null,
                min: (typeof config.min === 'string') 
                  ? config.min.split(',').map(Number) 
                  : (Array.isArray(config.min) ? config.min : []), // 兼容数组或其他类型
                opt: (typeof config.opt === 'string') 
                  ? config.opt.split(',').map(Number) 
                  : (Array.isArray(config.opt) ? config.opt : []),
                max: (typeof config.max === 'string') 
                  ? config.max.split(',').map(Number) 
                  : (Array.isArray(config.max) ? config.max : [])
              }
            }
          )
        }
      }

      const response = await updateConvert(formCopy)
      // Check if the response code is not 200, show an error message
      if (response.code !== 200) {
        this.$message({
          message: response.msg || 'An error occurred.',
          type: 'error',
          duration: 3000,
        });
        console.log('更新 失败:', response.msg)
        return;
      }
      this.$message({
        message: response.msg,
        type: 'success',
        duration: 3000, // 提示持续 3 秒
      });
      
      this.handleQuery()
      this.updateOpen = false;
      this.updateForm = { id: undefined, status: undefined ,parameters: {
          dynamic_configs: []
        }}
        
    },
    addDynamicConfig() {
      this.updateForm.parameters.dynamic_configs.push({
        name: '',
        min: '',
        opt: '',
        max: ''
      })
    },
    removeDynamicConfig(index) {
      this.updateForm.parameters.dynamic_configs.splice(index, 1)
    },
    // 预览文件
    previewFile() {
      if (!this.selectedFile) return;

      // 假设预览内容是文本或图片，扩展时可以增加其他格式的支持
      const fileUrl = this.selectedFile;
      this.isPreviewLoading = true;  
      this.fetchFileContent(fileUrl); // 获取文件内容
     
    },
    // 获取文件内容（适用于文本文件）
    async fetchFileContent(fileUrl) {
      var json_url = fileUrl.replace(/^http:\/\/zhai-datas\.ks3-cn-beijing(-internal)?\.ksyuncs\.com/, '/kso-datas')
          json_url = fileUrl.replace(/^http:\/\/zhai-datas\.ks3-cn-beijing(-internal)?\.ksyun\.com/, '/kso-datas')
       
      try {
        const resp = await axios.get(this.VUE_APP_BASE_API+json_url)
          // this.jsonData = resp.data
          // console.log('resp.data:', resp)
        if (resp.status !== 200) {
          throw new Error("获取文件内容失败")
        }
   
       
    
        this.previewContent = resp.data;
        this.isPreviewLoading = false;
      } catch (error) {
        console.error("获取文件内容失败", error);
        this.$message.error({
        message: `获取文件内容失败：${error.message}`,
        duration: 3000, // 持续 3 秒
      });
      }
    },
    // 关闭文件预览对话框
    handlePreviewDialogClose() {
      
      // 清空文件预览内容
      this.previewContent = '';
      // 可选：重置文件选择
      this.selectedFile = null;
      this.isPreviewLoading = false;
      this.downloadPreviewOpen = false;
      this.fileLinks = [];
    },
    // 下载预览文件
    downloadPreviewFile() {
      // 检查是否有内容可供下载
      if (!this.previewContent || !this.selectedFile) {
        this.$message.error('没有可下载的内容或文件名未设置');
        return;
      }

      // 提取文件名
      const fileName = this.selectedFile.split('?')[0].split('/').pop();

      // 根据文件名后缀动态设置类型
      const fileExtension = fileName.split('.').pop().toLowerCase();
      let fileType = 'text/plain'; // 默认类型为文本
      if (fileExtension === 'json') {
        fileType = 'application/json';
      } else if (fileExtension === 'txt' || fileExtension === 'pbtxt') {
        fileType = 'text/plain';
      } else {
        this.$message.error(`不支持的文件格式: ${fileExtension}`);
        return;
      }
      //将对象转换为格式化的 JSON 字符串
      if (typeof this.previewContent === 'object'  && this.previewContent !== null) {
        this.previewContent = JSON.stringify(this.previewContent, null, 2); // 转换为字符串
      } 

      // 创建 Blob 对象
      const blob = new Blob([this.previewContent], { type: `${fileType};charset=utf-8` });

      // 创建下载链接
      const url = window.URL.createObjectURL(blob);
      const downloadLink = document.createElement('a');
      downloadLink.href = url;
      downloadLink.download = fileName; // 设置文件名

      // 模拟点击触发下载
      document.body.appendChild(downloadLink);
      downloadLink.click();
      document.body.removeChild(downloadLink);

      // 释放 URL 对象
      window.URL.revokeObjectURL(url);

      // 提示下载成功
      this.$message.success('文件已成功下载');
    }

  },
};
</script>
<style scoped>
.custom-hint {
    color: rgb(255, 0, 0);
    font-size: 12px;
    margin-left: 5px;
  }

.file-preview {
  background-color: #f5f5f5;
  padding: 10px;
  border: 1px solid #ddd;
  max-height: 500px;
  overflow: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.empty-message {
  background-color: #f5f5f5;
  color: #999;
  font-size: 14px;
  text-align: center;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

</style>
