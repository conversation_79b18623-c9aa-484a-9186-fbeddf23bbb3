server {
    listen       443 ssl;
    server_name  pcv-test-admin.wps.cn;

    #access_log  /var/log/nginx/host.access.log  main;

    ssl_certificate /cert/server.crt;
    ssl_certificate_key /cert/server.key;

    location /api/ {
        proxy_pass http://**************:8887/;
        #proxy_set_header X-Client-IP *************;
        #proxy_set_header X-Client-IP ************;
        #proxy_set_header X-Client-IP $remote_addr;
        proxy_set_header            X-Forwarded-For *************;
    }

    location / {
        root   /usr/share/nginx/html;
        index  index.html index.htm;
        try_files $uri $uri/ /index.html;
    }

    # used for ks3(CORS)
    location /kso/ {
        proxy_pass http://zhai-platereduction.ks3-cn-beijing.ksyun.com/;
    }
    location /kso-datas/ {
        proxy_pass http://zhai-datas.ks3-cn-beijing.ksyun.com/;
    }

    #error_page  404              /404.html;

    # redirect server error pages to the static page /50x.html
    #
    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }

    # proxy the PHP scripts to Apache listening on 127.0.0.1:80
    #
    #location ~ \.php$ {
    #    proxy_pass   http://127.0.0.1;
    #}

    # pass the PHP scripts to FastCGI server listening on 127.0.0.1:9000
    #
    #location ~ \.php$ {
    #    root           html;
    #    fastcgi_pass   127.0.0.1:9000;
    #    fastcgi_index  index.php;
    #    fastcgi_param  SCRIPT_FILENAME  /scripts$fastcgi_script_name;
    #    include        fastcgi_params;
    #}

    # deny access to .htaccess files, if Apache's document root
    # concurs with nginx's one
    #
    #location ~ /\.ht {
    #    deny  all;
    #}
}

