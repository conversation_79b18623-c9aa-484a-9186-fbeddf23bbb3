
<template>
  <BasicLayout>
    <template #wrapper>
      <el-card class="box-card">
        <el-form ref="queryForm" :model="queryParams" :inline="true" label-width="68px">
          <el-form-item label="requestID">
            <el-input
              v-model="queryParams.requestID"
              placeholder="请输入requestID"
              clearable
              size="small"
            />
          </el-form-item>
          <el-form-item label="日期">
            <el-date-picker
              v-model="queryParams.startTime"
              size="small"
              type="date"
              align="right"
              value-format="yyyyMMdd"
            /> </el-form-item>
          <el-form-item label="引擎版本" prop="engineVersion">
            <el-select
              v-model="queryParams.engineVersion"
              placeholder="请选择引擎版本"
              clearable
              filterable
              size="small"
            >
              <el-option
                v-for="dict in engineVersionOptions"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="算法路由" prop="router">
            <el-select
              v-model="queryParams.router"
              placeholder="请选择算法路由"
              clearable
              filterable
              size="small"
            >
              <el-option
                v-for="dict in routerTypeOptions"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="来源" prop="origin">
            <el-input
              v-model="queryParams.origin"
              placeholder="请输入来源"
              clearable
              size="small"
            />
          </el-form-item>

          <el-form-item label="票据类型" prop="cardType">
            <el-input
              v-model="queryParams.cardType"
              placeholder="请输入票据类型"
              clearable
              size="small"
            />
          </el-form-item>

          <el-form-item label="状态" prop="status"><el-select
            v-model="queryParams.status"
            placeholder="状态"
            clearable
            filterable
            size="small"
          >
            <el-option
              v-for="dict in statusOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
          </el-form-item>

          <el-form-item label="错误信息" prop="errMsg"><el-input
            v-model="queryParams.errMsg"
            placeholder="请输入错误信息"
            clearable
            size="small"
          />
          </el-form-item>

          <el-form-item>
            <el-button v-permisaction="['vie:request:select']" type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button v-permisaction="['vie:request:refresh']" icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
          <el-form-item label="错误详情">
            <el-checkbox
              @change="errShowMsgClick()"
            />
          </el-form-item>
        </el-form>

        <el-table v-loading="loading" :data="vieRequestList" span="1.36">
          <el-table-column v-if="false" type="selection" width="5" align="center" />
          <el-table-column
            label="requestID"
            align="center"
            prop="request_id"
            width="280"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="来源"
            align="center"
            prop="origin"
            width="280"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="日期"
            align="center"
            prop="start_time"
            width="180"
            :formatter="formatDate"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="引擎版本"
            align="center"
            prop="engine_version"
            width="150"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="算法路由"
            align="center"
            prop="router"
            width="150"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="票证类型"
            align="center"
            prop="card_type"
            width="160"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="状态"
            align="center"
            prop="status"
            :formatter="statusFormat"
            width="60"
          >
            <template slot-scope="scope">
              {{ statusFormat(scope.row) }}
            </template>
          </el-table-column><el-table-column
            label="耗时(s)"
            align="center"
            prop="duration"
            width="120"
            sortable
            :show-overflow-tooltip="true"
          />
          <el-table-column
            v-if="showErrMsgs"
            label="错误信息"
            align="center"
            prop="err_msg"
            width="250"
            :show-overflow-tooltip="true"
          />
          <el-table-column label="详情" align="center">
            <template slot-scope="scope">
              <CVdataParameter
                :key="scope.row.request_id"
                :docid="scope.row.request_id"
                :starttime="queryParams.startTime"
                function="vie"
              />
            </template>
          </el-table-column>
          <el-table-column label="下载" width="300" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <VieOperation
                :jobid="scope.row.request_id"
                :dowdate="queryParams.startTime"
                :show-img="scope.row.img_url != ''"
                :img-urls="[scope.row.img_url]"
                :img-names="['链接']"
                :extra-info="JSON.parse(scope.row.ext)"
              />
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageIndex"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>

    </template>
  </BasicLayout>
</template>

<script>
import { getVieRequestList } from '@/api/vie/vie-request'
import VieOperation from '@/components/VieOperation/index.vue'
import CVdataParameter from '@/components/Cdata/Parameter.vue'

export default {
  name: 'VieRequest',
  components: {
    VieOperation, CVdataParameter
  },
  data() {
    return {
      showErrMsgs: false,
      // 总条数
      total: 0,
      // 弹出层标题
      title: '',
      open: false,
      loading: false,
      // 类型数据字典
      typeOptions: [],
      engineVersionOptions: [],
      vieRequestList: [],
      routerTypeOptions: [],
      // 状态选项
      statusOptions: [],
      // 查询参数
      queryParams: {
        pageIndex: 1,
        pageSize: 10,
        origin: undefined,
        startTime: undefined,
        status: 'noFinished',
        engineVersion: undefined,
        requestID: undefined,
        cardType: undefined,
        errMsg: undefined,
        router: undefined
      },
      showViewer: false,
      viewPhotoList: [],
      // 表单参数
      form: {
      },
      // 表单校验
      rules: { startTime: [{ required: true, message: '日期不能为空', trigger: 'blur' }]
      }
    }
  },

  created() {
    if (this.$route.query !== undefined) {
      const startTime = this.$route.query.startTime
      const router = this.$route.query.router
      const engineVersion = this.$route.query.engineVersion
      const cardType = this.$route.query.cardType
      this.queryParams.startTime = startTime
      this.queryParams.engineVersion = engineVersion
      this.queryParams.cardType = cardType
      this.queryParams.router = router
    }
    this.getDicts('vie_router_type').then(response => {
      this.routerTypeOptions = response.data
    })
    this.getDicts('vie_engine_version').then(response => {
      this.engineVersionOptions = response.data
    })
    this.getDicts('vie_status').then(response => {
      this.statusOptions = response.data
    })
    this.getList()
  },
  methods: {
    /** 查询参数列表 */
    getList() {
      this.loading = true
      getVieRequestList(this.addDateRange(this.queryParams)).then(response => {
        this.vieRequestList = response.data.list
        this.total = response.data.count
        this.loading = false
      }, _ => {
        this.vieRequestList = []
        this.total = 0
        this.loading = false
      }
      )
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined
      }
      this.resetForm('form')
    },
    statusFormat(row) {
      return this.selectDictLabel(this.statusOptions, row.status)
    },
    errShowMsgClick() {
      this.showErrMsgs = !this.showErrMsgs
    },
    formatDate(row, column) {
      const data = row[column.property]
      if (data == null) {
        return ''
      }
      return data.split('+')[0]
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageIndex = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.requestID = undefined
      this.queryParams.startTime = undefined
      this.queryParams.errMsg = undefined
      this.queryParams.engineVersion = undefined
      this.queryParams.status = undefined
      this.queryParams.cardType = undefined
      this.queryParams.router = undefined
      this.resetForm('queryForm')
      this.handleQuery()
    }
  }
}
</script>
