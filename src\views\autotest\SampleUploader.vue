<template>
  <el-upload
    ref="upload"
    action=""
    :on-change="uploadSample"
    accept="*"
    :auto-upload="false"
    :multiple="false"
    :limit="1"
  >
    <el-button size="mini">选择文件</el-button>
    <div slot="tip" class="el-upload__tip">支持所有文件类型</div>
  </el-upload>
</template>

<script>
export default {
  name: 'SampleUploader',
  data() {
    return {
      sampleFile: ''
    }
  },
  methods: {
    uploadSample(file) {
      this.sampleFile = file.raw
      this.$emit('getSampleFile', this.sampleFile)
    }
  }
}
</script>

<style scoped>

</style>
