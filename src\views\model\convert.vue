<template>
  <el-tabs v-model="activeName" style="width: 60%; min-width: 600px; margin: 1px 3%">
    <el-tab-pane label="Openvino" name="openvino">
      <el-form
        ref="formOpenvino"
        v-loading="loadingOpenvino"
        element-loading-text="上传文件中，请耐心等待"
        element-loading-spinner="el-icon-loading"
        :model="formOpenvino"
        label-width="220px"
      >
        <el-form-item
          label="模型文件上传"
          prop="raw_model_file"
          :rules="{required: true, message: '模型文件不能为空', trigger: 'blur'}"
        >
          <el-upload
            ref="upload"
            action=""
            :on-change="uploadChangeModelOpenvino"
            :before-upload="beforeupload"
            accept=".onnx"
            :auto-upload="false"
            :multiple="false"
            :limit="1"
          >
            <el-button size="mini">选择文件</el-button>
            <div slot="tip" class="el-upload__tip">只能上传.onnx文件</div>
          </el-upload>
        </el-form-item>
        <el-collapse v-model="collapseActiveNamesOpenvino">
          <el-collapse-item title="自定义参数" name="1">
            <el-form-item label="输入层名称 (input name)">
              <el-tooltip class="item" effect="dark" placement="right">
                <div slot="content">例如，input
                </div>
                <el-input
                  v-model="formOpenvino.input_name"
                  placeholder="请输入内容"
                  style="width: 30%; min-width: 100px; max-width: 210px"
                  size="small"
                  clearable
                />
              </el-tooltip>
            </el-form-item>
            <el-form-item label="输入层形状 (input shape)">
              <el-tooltip class="item" effect="dark" placement="right">
                <div slot="content">例如 [1,3,227,227] 或 [1,3,227,227],[2,4] （不支持变长）
                </div>
                <el-input
                  v-model="formOpenvino.input_shape"
                  placeholder="请输入内容"
                  style="width: 30%; min-width: 100px; max-width: 210px"
                  size="small"
                  clearable
                />
              </el-tooltip>
            </el-form-item>
            <el-form-item label="输出层名称 (output name)">
              <el-tooltip class="item" effect="dark" placement="right">
                <div slot="content">例如， output
                </div>
                <el-input
                  v-model="formOpenvino.output_name"
                  placeholder="请输入内容"
                  style="width: 30%; min-width: 100px; max-width: 210px"
                  size="small"
                  clearable
                />
              </el-tooltip>
            </el-form-item>
          </el-collapse-item>
        </el-collapse>
        <el-form-item>
          <el-button type="primary" size="small" @click="onSubmitOpenvino">提交</el-button>
        </el-form-item>
      </el-form>
    </el-tab-pane>

    <el-tab-pane label="TensorRt" name="trt">
      <el-form
        ref="formTrt"
        v-loading="loadingTrt"
        element-loading-text="上传文件中，请耐心等待"
        element-loading-spinner="el-icon-loading"
        :model="formTrt"
        label-width="220px"
      >
        <el-form-item
          label="模型文件上传"
          prop="raw_model_file"
          :rules="{required: true, message: '模型文件不能为空', trigger: 'blur'}"
        >
          <el-upload
            ref="upload"
            action=""
            :on-change="uploadChangeModelTrt"
            :before-upload="beforeupload"
            accept=".onnx"
            :auto-upload="false"
            :multiple="false"
            :limit="1"
          >
            <el-button size="mini">选择文件</el-button>
            <div slot="tip" class="el-upload__tip">只能上传.onnx文件</div>
          </el-upload>
        </el-form-item>
        <el-collapse v-model="collapseActiveNamesTrt">
          <el-collapse-item title="自定义参数" name="1">
            <el-form-item label="数据类型">
              <el-radio-group v-model="formTrt.data_type">
                <el-radio size="small" label="fp32">FP32</el-radio>
                <el-radio size="small" label="fp16">FP16</el-radio>
                <el-radio size="small" label="int8" disabled>INT8</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="动态尺寸">
              <el-tooltip class="item" effect="dark" placement="bottom">
                <div slot="content">例如 input [1,3,640,640] [32,3,640,640] [64,3,640,640]
                </div>
                <el-container>
                  <el-input v-model="formTrt.binding_name" class="trt_dynamic_input" size="small" placeholder="指定层名称" />
                  <el-input v-model="formTrt.min_shape" class="trt_dynamic_input" size="small" placeholder="最小形状" />
                  <el-input v-model="formTrt.opt_shape" class="trt_dynamic_input" size="small" placeholder="理想形状" />
                  <el-input v-model="formTrt.max_shape" class="trt_dynamic_input" size="small" placeholder="最大形状" />
                </el-container>
              </el-tooltip>
            </el-form-item>
          </el-collapse-item>
        </el-collapse>
        <el-form-item>
          <el-button type="primary" size="small" @click="onSubmitTrt">提交</el-button>
        </el-form-item>
      </el-form>
    </el-tab-pane>
  </el-tabs>
</template>

<script>
import store from '@/store'
export default {
  name: 'convert',
  data() {
    return {
      tempLoader: null,
      activeName: 'openvino',
      collapseActiveNamesTrt: [],
      collapseActiveNamesOpenvino: [],
      props: {
        multiple: true,
        expandTrigger: 'hover'
      },
      rules: {
      },
      formTrt: {
        data_type: '',
        binding_name: '',
        min_shape: '',
        opt_shape: '',
        max_shape: '',
        raw_model_file: '',
        transform_method: 'tensorrt',
        task: 'transform',
        user: store.getters.email
      },
      formOpenvino: {
        input_name: '',
        input_shape: '',
        output_name: '',
        raw_model_file: '',
        transform_method: 'openvino',
        task: 'transform',
        user: store.getters.email
      },
      loadingTrt: false,
      loadingOpenvino: false
    }
  },
  methods: {
    async onSubmitOpenvino() {
      this.loadingOpenvino = true
      if (this.formOpenvino.raw_model_file === '') {
        console.log('test', this.formOpenvino.raw_model_file, this.form.raw_model_file)
        this.$message.warning('模型文件不能为空')
        this.loadingOpenvino = false
      } else {
        this.$ks3.fileHandler(this, this.formOpenvino, this.loadingOpenvino)
      }
    },
    async onSubmitTrt() {
      this.loadingTrt = true
      if (this.formTrt.raw_model_file === '') {
        this.$message.warning('模型文件不能为空')
        this.loadingTrt = false
      } else {
        this.$ks3.fileHandler(this, this.formTrt, this.loadingTrt)
      }
    },
    toRecords() {
      this.$router.replace({ name: 'Records' })
    },
    uploadChangeModelOpenvino(file) {
      this.formOpenvino.raw_model_file = file.raw
    },
    uploadChangeModelTrt(file) {
      this.formTrt.raw_model_file = file.raw
    },
    beforeupload() {}
  }
}
</script>

<style scoped>
/deep/ .el-collapse-item__header{
  background-color: #f0f1f5 !important;
  margin: 0 auto 0 180px;
  display: block;
  min-width: 180px;
}
/deep/ .el-collapse-item__wrap{
  background-color: #f0f1f5 !important;
}
</style>
