<template>
  <div v-if="node.sub_nodes.length > 0" :style="getIndentStyle()">
    <div v-for="(subNode, subIndex) in node.sub_nodes" :key="subIndex">
      <div style="display: flex; align-items: flex-end;">
        <template v-if="subNode.value === 2">
          <h2 class="title-level-2" style="white-space: nowrap;margin-left: 5px;margin-top: 5px;">{{ subNode.nodes[0].text }}</h2>
        </template>
        <template v-else-if="subNode.value === 3">
          <h3 class="title-level-3" style="white-space: nowrap;margin-left: 15px;margin-top: 5px;">{{ subNode.nodes[0].text }}</h3>
        </template>
        <template v-else-if="subNode.value === 4">
          <h4 class="title-level-4" style="white-space: nowrap;margin-left: 25px;margin-top: 5px;">{{ subNode.nodes[0].text }}</h4>
        </template>
        <template v-else-if="subNode.value === 5">
          <h5 class="title-level-5" style="white-space: nowrap;margin-left: 35px;margin-top: 5px;">{{ subNode.nodes[0].text }}</h5>
        </template>
        <template v-else-if="subNode.value === 6">
          <h6 class="title-level-6" style="white-space: nowrap;margin-left: 45px;margin-top: 5px;">{{ subNode.nodes[0].text }}</h6>
        </template>
        <template v-else-if="subNode.value === 7">
          <h7 class="title-level-7" style="white-space: nowrap;margin-left: 55px;margin-top: 5px;">{{ subNode.nodes[0].text }}</h7>
        </template>
        <span class="page-number" style="margin-left: 5px;">页码：{{ subNode.nodes[0].pageIndex }}</span>
      </div>
      <TitleItem v-if="subNode.sub_nodes.length" :node="subNode" :level="subNode.value" />
    </div>
  </div>
</template>
<script>
export default {
  name: 'TitleItem',
  props: {
    node: Object,
    level: Number
  },
  methods: {
    getIndentStyle() {
      const indentSize = 20; // 缩进大小
      return {
        marginLeft: `${(this.level - 2) * indentSize}px`
      };
    }
  }
};
</script>
<style scoped>
.title-level-1, .title-level-2, .title-level-3, .title-level-4, .title-level-5, .title-level-6, .title-level-7 {
  margin: 0;
}

.page-number {
  margin-left: 5px;
  white-space: nowrap;
}
</style>