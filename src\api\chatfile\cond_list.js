import request from '@/utils/request'

// 查询CondList
export function listCondList(query) {
  return request({
    url: '/api/v1/chatfile/cond_list',
    method: 'get',
    params: query
  })
}

// 新增CondList
export function addCondList(data) {
  return request({
    url: '/api/v1/chatfile/cond_list',
    method: 'post',
    data: data
  })
}

// 修改CondList
export function updateCondList(data) {
  return request({
    url: '/api/v1/chatfile/cond_list/' + data.id,
    method: 'put',
    data: { ...data }
  })
}

// 删除CondList
export function delCondList(id) {
  return request({
    url: '/api/v1/chatfile/cond_list/' + id,
    method: 'delete'
  })
}

// 新增CondList
export function addMatch(data) {
  return request({
    url: '/api/v1/chatfile/cond_list/match',
    method: 'post',
    data: data
  })
}

// 修改单条规则
export function updateMatch(data) {
  return request({
    url: '/api/v1/chatfile/cond_list/match/' + data.id,
    method: 'put',
    data: { ...data }
  })
}

// 删除单条match
export function delMatch(id) {
  return request({
    url: '/api/v1/chatfile/cond_list/match/' + id,
    method: 'delete'
  })
}
