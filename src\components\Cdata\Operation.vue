<template>
  <div>
    <el-button
      v-if="showImg&&!showImgs"
      size="mini"
      type="text"
      :disabled="!showImg"
      @click="approval('img')"
    >图片 |</el-button>
    <el-button
      v-if="showImgs"
      size="mini"
      type="text"
      :disabled="!showImgs"
      @click="approval('imgs')"
    >多张图片 |</el-button>
    <el-button
      size="mini"
      type="text"
      :disabled="!showInputPdf"
      @click="approval('pdf_input')"
    >源pdf |</el-button>
    <el-button
      v-show="!isPdfEditV2"
      size="mini"
      type="text"
      :disabled="!showInputZip"
      @click="approval('zip_input')"
    >源zip |</el-button>
    <el-button
      v-show="!isPdfEditV2"
      size="mini"
      type="text"
      :disabled="!showPdf"
      @click="approval('pdf')"
    >目标pdf |</el-button>
    <el-button
      v-show="!isPdfEditV2"
      size="mini"
      type="text"
      :disabled="!showDocxKs"
      @click="approval('docx_ks')"
    >docx |</el-button>
    <!-- <el-button
      size="mini"
      type="text"
      :disabled="!showZip"
      @click="approval('zip')"
    >目标zip |</el-button> -->
    <el-button
      v-show="!isPdfEditV2"
      type="text"
      size="mini"
      :disabled="!showXml"
      @click="approval('xml')"
    >xml |</el-button>
    <el-button
      v-show="!isPdfEditV2"
      type="text"
      size="mini"
      :disabled="!showJson"
      @click="approval('json')"
    >json |</el-button>
    <el-button
      v-show="!isPdfEditV2"
      type="text"
      size="mini"
      :disabled="!showXlsx"
      @click="approval('xlsx')"
    >xlsx |</el-button>
    <el-button
      v-show="!isPdfEditV2"
      type="text"
      size="mini"
      :disabled="!showPptx"
      @click="approval('pptx')"
    >pptx |</el-button>
    <!-- <el-link
      slot="reference"
      type="primary"
      :href="approval()"
    > docx2
    </el-link> -->

    <!-- 添加或修改对话框 -->
    <el-dialog :visible.sync="approvalOpen" :title="title" width="500px">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="申请目的" prop="purposes">
          <div>
            <el-input v-model="form.purposes" placeholder="默认申请目的为：获取下载链接" type="textarea" rows="1" />
          </div>
        </el-form-item>
        <!-- <el-form-item label="审批人" prop="approver_id">
          <div>
            <el-radio v-for="option in approverList" :key="option.value" v-model="form.approver_id" :label="option.value">
              {{ option.label }}
            </el-radio>
          </div>
        </el-form-item> -->
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="填写需要补充的信息" type="textarea" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="loading" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-image-viewer
      v-if="showViewer"
      style="width: 98%; height: 98%"
      :on-close="closeViewer"
      :url-list="viewPhotoList"
    />
  </div>
</template>

<script>
import { string } from 'clipboard'
import elImageViewer from 'element-ui/packages/image/src/image-viewer.vue'
import { postCvApproval } from '@/api/layout'

export default {
  name: 'CVdataOperation',
  components: {
    elImageViewer
  },
  props: {
    approverList: {
      type: Array,
      required: true
    },
    layoutModel: {
      type: string,
      default: ''
    },
    imgUrls: {
      type: Array,
      default: () => []
    },
    dialog: {
      type: Boolean,
      default: false
    },
    showPdf: {
      type: Boolean,
      default: false
    },
    showInputPdf: {
      type: Boolean,
      default: false
    },
    showInputZip: {
      type: Boolean,
      default: false
    },
    showDocx: {
      type: Boolean,
      default: false
    },
    showDocxKs: {
      type: Boolean,
      default: false
    },
    showImg: {
      type: Boolean,
      default: false
    },
    showImgs: {
      type: Boolean,
      default: false
    },
    showXml: {
      type: Boolean,
      default: false
    },
    showJson: {
      type: Boolean,
      default: false
    },
    showZip: {
      type: Boolean,
      default: false
    },
    showXlsx: {
      type: Boolean,
      default: false
    },
    showPptx: {
      type: Boolean,
      default: false
    },
    pdfUrl: {
      type: string,
      default: ''
    },
    dsturl: {
      type: string,
      default: ''
    },
    inputPdfUrl: {
      type: string,
      default: ''
    },
    zipSourceUrl: {
      type: string,
      default: ''
    },
    xmlUrl: {
      type: string,
      default: ''
    },
    zipUrl: {
      type: string,
      default: ''
    },
    docxUrl: {
      type: string,
      default: ''
    },
    docxKsUrl: {
      type: string,
      default: ''
    },
    docid: {
      type: string,
      default: ''
    },
    dowdate: {
      type: string,
      default: ''
    },
    dowtype: {
      type: string,
      default: ''
    },
    cvmodel: {
      type: string,
      default: ''
    },
    exporttype: {
      type: string,
      default: 'docx'
    },
    tableExportXlsxUrl: {
      type: string,
      default: ''
    },
    tableExportZipUrl: {
      type: string,
      default: ''
    },
    isPdfEditV2: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 查询参数
      queryParams: {
        // filetype: undefined,
        // docid: this.docid,
        starttime: undefined,
        cvmodel: this.cvmodel
      },
      url_download: 'download',
      url_downloadurl: 'downloadurl',
      imgUrlsCopy: [...this.imgUrls],
      imgsUrl: [],
      file_pdf: 1,
      file_img: 2,
      file_xml: 3,
      file_docx: 4,
      file_zip: 5,
      file_docxks: 6,
      file_input_pdf: 7,
      file_png: 8,
      file_jpeg: 9,
      file_xlsx: 10,
      file_input_zip: 11,
      file_json: 12,
      open: false,
      copyOpen: false,
      showViewer: false,
      viewPhotoList: [],
      styleObject: {
        overflow: 'auto',
        height: this.imgUrls.length > 20 ? '500px' : '200px'
      },
      // 是否开启审批表单
      approvalOpen: false,
      // 标题
      title: '创建审批-获取图片链接',
      // 表单参数
      form: {
        purposes: '',
        doc_id: ''
      },
      // 表单校验
      rules: {
        approver_id: [{ required: true, message: '选择一个审批人', trigger: 'blur' }]
      },
      approver_list: [],
      // 文件类型
      fileType: '',
      // 正在加载
      loading: false
    }
  },
  created() {
    this.getDicts('approver_cvdata_list').then((response) => {
      console.log(response)
      console.log(response.data)
      this.approver_list = response.data
    })
    console.log('xlsx:',this.showXlsx)
    console.log(this.docid)
    
  },
  methods: {
    /* 发起审批 */
    async approval(fileType) {
      this.form.file_type = fileType
      // 开启审批表单
      this.approvalOpen = true
    },
    // 获取表单参数
    getFormParams() {
      this.form.doc_id = this.docid
      this.form.approver_id = parseInt(this.form.approver_id)
      this.form.starttime = this.dowdate
      // 判断function
      if (this.cvmodel === 'requests') {
        this.form.function = 'pdf2docx'
      } else if (this.cvmodel === 'pdf-edit-request') {
        this.form.function = 'pdfEdit'
      } else if (this.cvmodel === 'table-extract-requests') {
        this.form.function = 'tableextract'
      } else if (this.cvmodel === 'pdf-edit-v2-request') {
        this.form.function = 'pdfEditV2'
      } else {
        this.form.function = ''
      }
      // 设置purposes
      if (this.form.purposes === '') {
        this.form.purposes = '获取[' + this.form.function + ']下载链接'
      }
    },
    /** 提交按钮 */
    submitForm: function() {
      this.loading = true
      this.getFormParams()
      this.$refs['form'].validate((valid) => {
        if (valid) {
          postCvApproval(this.form).then((response) => {
            this.reset()
            if (response.code === 200) {
              this.msgSuccess(response.msg)
              this.approvalOpen = false
              // this.getList()
            } else {
              this.msgError(response.msg)
            }
          })
        } else {
          this.$message({
            showClose: true,
            message: '参数设置有误，审批表单提交失败! ',
            center: true,
            type: 'info'
          })
        }
      })
    },
    // 取消按钮
    cancel() {
      this.approvalOpen = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.loading = false
      this.form = {
        remark: '',
        purposes: '',
        approver_id: undefined,
        starttime: '',
        file_type: '',
        function: '',
        doc_id: undefined
      }
      this.resetForm('form')
    }
  }
}
</script>
