import qs from 'qs'
import request from '@/utils/request'
import { getHeaders } from '@/utils/get-header'

export function listPrefixes() {
  return request({
    url: '/layout/profiling/prefix',
    method: 'get',
    headers: {
      ...getHeaders()
    }
  })
}

export function listTraces(params) {
  return request({
    url: '/layout/profiling/trace',
    method: 'get',
    headers: {
      ...getHeaders()
    },
    params,
    paramsSerializer: function(params) {
      return qs.stringify(params, { arrayFormat: 'repeat' })
    }
  })
}

export function getTrace(rootId, params) {
  return request({
    url: '/layout/profiling/trace/' + rootId,
    method: 'get',
    headers: {
      ...getHeaders()
    },
    params
  })
}

export function getResult(params) {
  return request({
    url: '/layout/profiling/result',
    method: 'get',
    headers: {
      ...getHeaders()
    },
    params,
    responseType: 'blob', // 后台返回的数据会被强制转为blob类型
    timeout: 30000
  })
}
