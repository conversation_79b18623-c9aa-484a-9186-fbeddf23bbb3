
<template>
  <BasicLayout>
    <template #wrapper>
      <el-card class="box-card">
        <el-form ref="queryForm" :model="queryParams" :inline="true" label-width="65px">
          <el-form-item label="日期">
            <el-date-picker
              v-model="queryParams.begin_t"
              size="small"
              type="date"
              align="right"
              value-format="yyyyMMdd"
            /></el-form-item>
          <el-form-item label="client_request_id" label-width="110px">
            <el-input
              v-model="queryParams.client_request_id"
              placeholder="请输入client_request_id"
              clearable
              size="small"
            />
          </el-form-item>
          <el-form-item label="code">
            <el-input
              v-model="queryParams.code"
              placeholder="请输入errno"
              clearable
              size="small"
            />
          </el-form-item>
          <el-form-item label="user_id">
            <el-input
              v-model="queryParams.user_id"
              placeholder="请输入user_id"
              clearable
              size="small"
            />
          </el-form-item>
          <el-form-item label="session_id" label-width="70px">
            <el-input
              v-model="queryParams.session_id"
              placeholder="请输入session_id"
              clearable
              size="small"
            />
          </el-form-item>
          <el-form-item label="chat_id">
            <el-input
              v-model="queryParams.chat_id"
              placeholder="请输入chat_id"
              clearable
              size="small"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-table v-loading="loading" :data="gatewayRequestList" span="1.36">
          <el-table-column v-if="false" type="selection" width="5" align="center" />
          <el-table-column
            label="日期"
            align="center"
            prop="begin_t"
            width="180"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="client_request_id"
            align="center"
            prop="client_request_id"
            width="280"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="code"
            align="center"
            prop="code"
            width="150"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="session_id"
            align="center"
            prop="session_id"
            width="160"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="chat_id"
            align="center"
            prop="chat_id"
            width="160"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="product_name"
            align="center"
            prop="product_name"
            width="160"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="user_id"
            align="center"
            prop="user_id"
            width="160"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="cmd"
            align="center"
            prop="cmd"
            width="60"
          /><el-table-column
            label="耗时(s)"
            align="center"
            prop="duration"
            width="120"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="total_tokens"
            align="center"
            prop="total_tokens"
            width="120"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="prompt_tokens"
            align="center"
            prop="prompt_tokens"
            width="120"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="completion_tokens"
            align="center"
            prop="completion_tokens"
            width="120"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="num_pages"
            align="center"
            prop="num_pages"
            width="120"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="num_texts"
            align="center"
            prop="num_texts"
            width="120"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="sha256"
            align="center"
            prop="sha256"
            width="120"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="file_type"
            align="center"
            prop="file_type"
            width="120"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="model"
            align="center"
            prop="model"
            width="120"
            :show-overflow-tooltip="true"
          />
          <el-table-column label="操作" width="300" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <HandleOperation
                :client-request-id="scope.row.client_request_id"
                :user_id="scope.row.user_id"
                :session-id="scope.row.session_id"
                :chat-id="scope.row.chat_id"
                :input-data="scope.row.input_data"
                :output-data="scope.row.output_data"
                :begin-t="formatDate(scope.row.begin_t)"
              />
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageIndex"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>

    </template>
  </BasicLayout>
</template>

<script>
import { getHandleRequestList } from '@/api/chatfile/handle-request'
import { format_current_data_YYYYMMDD } from '@/api/chatfile/utils'
import HandleOperation from '@/components/Chatfile/HandleOperation/index.vue'

export default {
  name: 'HandleRequest',
  components: {
    HandleOperation
  },
  data() {
    return {
      // 总条数
      total: 0,
      // 弹出层标题
      title: '',
      open: false,
      loading: false,
      // 类型数据字典
      typeOptions: [],
      routerTypeOptions: [],
      gatewayRequestList: [],
      // 查询参数
      queryParams: {
        pageIndex: 1,
        pageSize: 10,
        client_request_id: undefined,
        begin_t: format_current_data_YYYYMMDD(),
        code: undefined,
        session_id: undefined,
        chat_id: undefined,
        product_name: undefined,
        user_id: undefined,
        cmd: undefined,
        duration: undefined,
        total_tokens: undefined,
        prompt_tokens: undefined,
        completion_tokens: undefined,
        num_pages: undefined,
        num_texts: undefined,
        sha256: undefined,
        file_type: undefined,
        model: undefined
      },
      // 表单参数
      form: {
      },
      // 表单校验
      rules: { begin_t: [{ required: true, message: '日期不能为空', trigger: 'blur' }]
      }
    }
  },
  created() {
    if (this.$route.query !== undefined) {
      this.queryParams.client_request_id = this.$route.query.client_request_id
      this.queryParams.session_id = this.$route.query.session_id
      this.queryParams.chat_id = this.$route.query.chat_id
      if (this.$route.query.user_id > 0) {
        this.queryParams.user_id = this.$route.query.user_id
      }
      if (this.$route.query.begin_t !== undefined) {
        this.queryParams.begin_t = this.$route.query.begin_t
      }
    }
    this.getList()
  },
  methods: {
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageIndex = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.client_request_id = undefined
      this.queryParams.begin_t = format_current_data_YYYYMMDD()
      this.queryParams.code = undefined
      this.queryParams.session_id = undefined
      this.queryParams.chat_id = undefined
      this.queryParams.product_name = undefined
      this.queryParams.user_id = undefined
      this.queryParams.cmd = undefined
      this.queryParams.duration = undefined
      this.queryParams.total_tokens = undefined
      this.queryParams.prompt_tokens = undefined
      this.queryParams.completion_tokens = undefined
      this.queryParams.num_pages = undefined
      this.queryParams.num_texts = undefined
      this.queryParams.sha256 = undefined
      this.queryParams.file_type = undefined
      this.queryParams.model = undefined
      this.resetForm('queryForm')
      this.handleQuery()
    },
    formatDate(data) {
      if (data == null) {
        return ''
      }
      const datastr = data.split('T')[0]
      return datastr.replaceAll('-', '')
    },
    getList() {
      this.loading = true
      getHandleRequestList(this.addDateRange(this.queryParams)).then(response => {
        this.gatewayRequestList = response.data.list
        this.total = response.data.count
        this.loading = false
      }, _ => {
        this.gatewayRequestList = []
        this.total = 0
        this.loading = false
      }
      )
    }
  }
}
</script>
