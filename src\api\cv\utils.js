const strUrlRegex = '^((https:|http:|ftp:|rtsp:|mms:)?//)' +
'?(([0-9a-z_!~*\'().&=+$%-]+: )?[0-9a-z_!~*\'().&=+$%-]+@)?' + // ftp的user@
'(([0-9]{1,3}.){3}[0-9]{1,3}' + // IP形式的URL- **************
'|' + // 允许IP和DOMAIN（域名）
'([0-9a-z_!~*\'()-]+.)*' + // 域名- www.
'([0-9a-z][0-9a-z-]{0,61})?[0-9a-z].' + // 二级域名
'[a-z]{2,6})' + // first level domain- .com or .museum
'(:[0-9]{1,4})?' + // 端口- :80
'((/?)|' + // a slash isn't required if there is no file name
'(/[0-9a-zA-Z_!~*\'().;?:@&=+$,%#-]+)+/?)$'
export const urlRegex = new RegExp(strUrlRegex)

export function formatfileSize(size) {
  if (size < 1024) {
    return size + 'B'
  }

  size = parseInt(size / 1024)
  if (size < 1024) {
    return size + 'KB'
  }

  size = parseInt(size / 1024)
  if (size < 1024) {
    return size + 'MB'
  }
  return size + 'GB'
}

export function convertOpTimes(opTimes, times) {
  opTimes.fill(0) // 存放时间戳
  for (let i = 0; i < times.length; i++) {
    if (i === 0) {
      opTimes.splice(i, 1, times[i] / 1000)
    }
    if (i > 0) {
      // let _t = (timeStamp[i] - timeStamp[i-1]) / 1000;
      opTimes.splice(i, 1, (times[i] - times[i - 1]) / 1000)
    }
  }
}

export function isValidFile(ext, types) {
  let valid = false
  for (const index in types) {
    if (types[index].search(ext) >= 0) {
      valid = true
      break
    }
  }
  return valid
}

export function downloadFileType(url, exporttype) {
  // https://pcv-test.wps.cn/layout/abbyy/pdfconvert/docID
  console.log('开始下载文件 ', url)

  const xhr = new XMLHttpRequest()
  xhr.open('GET', url, true) // 也可以使用POST方式，根据接口
  xhr.responseType = 'blob' // 返回类型blob
  // 定义请求完成的处理函数，请求前也可以增加加载框/禁用下载按钮逻辑
  xhr.onload = () => {
    // 请求完成
    var blob = xhr.response
    console.log('下载文件 ', blob)
    if (blob.size > 0) {
      var reader = new FileReader()
      reader.readAsDataURL(blob) // 转换为base64，可以直接放入a表情href
      reader.onload = (e) => {
        // 转换完成，创建一个a标签用于下载
        const a = document.createElement('a')
        a.id = 'tempdownload'
        a.style.display = 'none'
        a.href = e.target.result
        a.download = new Date().getTime() + '.' + exporttype

        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
      }
    }
    // const url = window.URL.createObjectURL(xhr.response)
    // const elem_a = document.createElement('a')
    // elem_a.style.display = 'none'
    // elem_a.href = url
    // elem_a.download = fileName
    // elem_a.click()
  }
  // 发送ajax请求
  xhr.send()
}

export function downloadFile(url) {
  // https://pcv-test.wps.cn/layout/abbyy/pdfconvert/docID
  console.log('开始下载文件 ', url)

  const xhr = new XMLHttpRequest()
  xhr.open('GET', url, true) // 也可以使用POST方式，根据接口
  xhr.responseType = 'blob' // 返回类型blob
  // 定义请求完成的处理函数，请求前也可以增加加载框/禁用下载按钮逻辑
  xhr.onload = () => {
    // 请求完成
    var blob = xhr.response
    console.log('下载文件 ', blob)
    if (blob.size > 0) {
      var reader = new FileReader()
      reader.readAsDataURL(blob) // 转换为base64，可以直接放入a表情href
      reader.onload = (e) => {
        // 转换完成，创建一个a标签用于下载
        const a = document.createElement('a')
        a.id = 'tempdownload'
        a.style.display = 'none'
        a.href = e.target.result
        a.download = new Date().getTime() + '.docx'

        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
      }
    }
    // const url = window.URL.createObjectURL(xhr.response)
    // const elem_a = document.createElement('a')
    // elem_a.style.display = 'none'
    // elem_a.href = url
    // elem_a.download = fileName
    // elem_a.click()
  }
  // 发送ajax请求
  xhr.send()
}

export function sleep(delay) {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      resolve()
    }, delay || 1000)
  })
}
