
<template>
  <BasicLayout>
    <template #wrapper>
      <el-card class="box-card">
        <el-form
          ref="queryForm"
          :model="queryParams"
          :inline="true"
          label-width="68px"
        >
          <el-form-item label="创建时间">
            <el-date-picker
              v-model="dateRange"
              size="small"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              align="right"
              value-format="yyyy-MM-dd"
            />
          </el-form-item>
          <el-form-item label="类型" prop="status"><el-select
            v-model="queryParams.busType"
            placeholder="类型"
            clearable
            size="small"
          >
            <el-option
              v-for="dict in bustypeOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
          </el-form-item>
          <el-form-item>
            <el-button
              v-permisaction="['cvdata:requestsDayStatistics:select']"
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
            >搜索</el-button>
            <el-button
              v-permisaction="['cvdata:requestsDayStatistics:refresh']"
              icon="el-icon-refresh"
              size="mini"
              @click="resetQuery"
            >重置</el-button>
          </el-form-item>
        </el-form>
        <el-row>
          <el-col>
            <MyLineChart
              :x-axis-datas="xAxisDatas"
              :task-datas="taskDatas"
              :fail-count="successRate"
              :avg-page-datas="avgPageCount"
              :total-pages="totalPageCount"
              :avg-duration="avgDuration"
            />
          </el-col>
        </el-row>
        <el-table
          v-loading="loading"
          :data="requestsDayStatisticsList"
          @selection-change="handleSelectionChange"
        >
          <el-table-column
            v-if="false"
            type="selection"
            width="5"
            align="center"
          /><el-table-column
            label="日期（天)"
            align="center"
            prop="date"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="任务总数"
            align="center"
            prop="count"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="失败任务数"
            align="center"
            prop="failCount"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="成功率"
            align="center"
            prop="successRate"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="平均耗时(s)"
            align="center"
            prop="avgDuration"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="平均页数"
            align="center"
            prop="avgPageCount"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="总页数"
            align="center"
            prop="totalPageCount"
            :show-overflow-tooltip="true"
          />
        </el-table>

        <!-- <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageIndex"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        /> -->

        <!-- 添加或修改对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="500px">
          <el-form ref="form" :model="form" :rules="rules" label-width="80px" />
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
          </div>
        </el-dialog>
      </el-card>
    </template>
  </BasicLayout>
</template>

<script>
import {
  addRequestsDayStatistics,
  delRequestsDayStatistics,
  getRequestsDayStatistics,
  listRequestsDayStatistics,
  updateRequestsDayStatistics
} from '@/api/cvdata/requests-day-statistics'

import MyLineChart from './linechart'

export default {
  name: 'RequestsDayStatistics',
  components: { MyLineChart },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      isEdit: false,
      // 类型数据字典
      typeOptions: [],
      requestsDayStatisticsList: [],
      xAxisDatas: [],
      taskDatas: [],
      bustypeOptions: [],
      // failCountDatas: [],
      avgDuration: [],
      successRate: [],
      avgPageCount: [],
      totalPageCount: [],
      // 查询参数
      queryParams: {
        busType: 'pdf2word',
        pageIndex: 1,
        pageSize: 10,
        date: undefined
      },
      // 表单参数
      form: {},
      dateRange: [],
      // 表单校验
      rules: {
        date: [
          { required: true, message: '日期（天)不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getDicts('pdf_statistics_type').then(response => {
      this.bustypeOptions = response.data
      this.queryParams.busType = this.bustypeOptions[0].value
    })
    this.getList()
  },
  methods: {
    /** 查询参数列表 */
    getList() {
      this.xAxisDatas = []
      this.taskDatas = []
      // this.failCountDatas = []

      this.avgDuration = []
      this.avgPageCount = []
      this.totalPageCount = []
      this.successRate = []
      this.loading = true
      // 默认查30条
      this.queryParams.pageIndex = 1
      this.queryParams.pageSize = 30
      var d = new Date()

      if (this.dateRange.length === 0) {
        var sd = new Date(d.getTime() - 14 * 24 * 60 * 60 * 1000)

        var smonth = sd.getMonth() < 9 ? '0' + (sd.getMonth() + 1).toString() : (sd.getMonth() + 1).toString()
        var sday = sd.getDate() < 9 ? '0' + (sd.getDate() + 1).toString() : (sd.getDate() + 1).toString()

        var edmonth = d.getMonth() < 9 ? '0' + (d.getMonth() + 1).toString() : (d.getMonth() + 1).toString()
        var edday = d.getDate() < 9 ? '0' + (d.getDate() + 1).toString() : (d.getDate() + 1).toString()

        this.dateRange.push(sd.getFullYear() + '-' + smonth + '-' + sday)
        this.dateRange.push(d.getFullYear() + '-' + edmonth + '-' + edday)
      }
      listRequestsDayStatistics(
        this.addDateRange(this.queryParams, this.dateRange)
      ).then((response) => {
        this.loading = false
        if (this.dateRange.length > 0) {
          for (var i = response.data.list.length - 1; i >= 0; i--) {
            this.xAxisDatas.push(response.data.list[i].date)
            this.taskDatas.push(response.data.list[i].count)
            if (response.data.list[i].count === '0') {
              this.successRate.push(0)
              response.data.list[i].successRate = 0
            } else {
              var successrage = ((response.data.list[i].count - response.data.list[i].failCount) / response.data.list[i].count * 100).toFixed(3)
              this.successRate.push(successrage)
              response.data.list[i].successRate = successrage
            }

            this.avgDuration.push(response.data.list[i].avgDuration)
            this.avgPageCount.push(response.data.list[i].avgPageCount)
            this.totalPageCount.push(response.data.list[i].totalPageCount)
          }
        }
        this.requestsDayStatisticsList = response.data.list
        this.total = response.data.count
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {}
      this.resetForm('form')
    },
    getImgList: function() {
      this.form[this.fileIndex] =
        this.$refs['fileChoose'].resultList[0].fullUrl
    },
    fileClose: function() {
      this.fileOpen = false
    },
    // 关系
    // 文件
    /** 搜索按钮操作 */
    handleQuery() {
      // if ((this.stringToDate(this.dateRange[1], '-') - this.stringToDate(this.dateRange[0], '-')) / 1000 / 24 / 60 / 60 > 30) {
      //   this.msgError('请选择30天范围内的数据')
      //   return
      // }
      this.queryParams.pageIndex = 1
      this.getList()
    },
    stringToDate(dateStr, separator) {
      if (!separator) {
        separator = '-'
      }
      var dateArr = dateStr.split(separator)
      var year = parseInt(dateArr[0])
      var month
      if (dateArr[1].indexOf('0') === 0) {
        month = parseInt(dateArr[1].substring(1))
      } else {
        month = parseInt(dateArr[1])
      }
      var day = parseInt(dateArr[2])
      var date = new Date(year, month - 1, day)
      return date
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.busType = this.bustypeOptions[0].value
      this.dateRange = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加RequestsDayStatistics'
      this.isEdit = false
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.date)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const date = row.date || this.ids
      getRequestsDayStatistics(date).then((response) => {
        this.form = response.data
        this.open = true
        this.title = '修改RequestsDayStatistics'
        this.isEdit = true
      })
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          if (this.form.date !== undefined) {
            updateRequestsDayStatistics(this.form).then((response) => {
              if (response.code === 200) {
                this.msgSuccess(response.msg)
                this.open = false
                this.getList()
              } else {
                this.msgError(response.msg)
              }
            })
          } else {
            addRequestsDayStatistics(this.form).then((response) => {
              if (response.code === 200) {
                this.msgSuccess(response.msg)
                this.open = false
                this.getList()
              } else {
                this.msgError(response.msg)
              }
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      var Ids = (row.date && [row.date]) || this.ids

      this.$confirm('是否确认删除编号为"' + Ids + '"的数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(function() {
          return delRequestsDayStatistics({ ids: Ids })
        })
        .then((response) => {
          if (response.code === 200) {
            this.msgSuccess(response.msg)
            this.open = false
            this.getList()
          } else {
            this.msgError(response.msg)
          }
        })
        .catch(function() {})
    }
  }
}
</script>
