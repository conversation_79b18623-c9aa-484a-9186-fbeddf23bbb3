
<template>
  <BasicLayout>
    <template #wrapper>
      <el-card class="box-card">
        <el-form ref="queryForm" :model="queryParams" :inline="true" label-width="88px">
          <el-form-item label="docid" prop="docid"><el-input
            v-model="queryParams.docid"
            placeholder="请输入docid"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="origin" prop="origin"><el-input
            v-model="queryParams.origin"
            placeholder="请输入origin"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="thirdclienttype" prop="thirdclienttype"><el-input
            v-model="queryParams.thirdclienttype"
            placeholder="请输入thirdclienttype"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="日期">
            <el-date-picker
              v-model="queryParams.starttime"
              size="small"
              type="date"

              align="right"
              value-format="yyyy-MM-dd"
            /> </el-form-item>
          <el-form-item label="状态" prop="status"><el-select
            v-model="queryParams.status"
            placeholder="表格编辑状态"
            clearable
            size="small"
          >
            <el-option
              v-for="dict in statusOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
          </el-form-item>
          <el-form-item label="错误"><el-select
            v-model="queryParams.errorstatus"
            placeholder="错误个数"
            clearable
            size="small"
          >
            <el-option
              v-for="dict in errstatusOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
          </el-form-item>
          <el-form-item>
            <div style="display: flex; align-items: center;">
              <el-button v-permisaction="['admin:cvdataTableExtract:select']" type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
              <el-button v-permisaction="['admin:cvdataTableExtract:refresh']" icon="el-icon-refresh" size="mini" style="margin-right: 10px;" @click="resetQuery">重置</el-button>
              <CVdataExport
                :query-params="{ ...queryParams, pageIndex: queryParams.pageIndex, pageSize: queryParams.pageSize }"
                :approver-list="approver_list"
                :approver-function="approver_function"
              />
            </div>
          </el-form-item>
        </el-form>

        <el-table v-loading="loading" :data="tableExtractRequestsList" span="1.23" @selection-change="handleSelectionChange">
          <el-table-column v-if="false" type="selection" width="5" align="center" /><el-table-column
            label="docid"
            align="center"
            prop="docid"
            width="320"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="origin"
            align="center"
            prop="origin"
            width="90"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="third_client_type"
            align="center"
            prop="thirdclienttype"
            width="130"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="日期"
            align="center"
            prop="starttime"
            width="180"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="状态"
            align="center"
            prop="status"
            :formatter="statusFormat"
            width="60"
          >
            <template slot-scope="scope">
              {{ statusFormat(scope.row) }}
            </template>
          </el-table-column><el-table-column
            label="耗时(s)"
            align="center"
            prop="duration"
            width="150"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="页数"
            align="center"
            prop="pagecount"
            width="80"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="Model"
            align="center"
            prop="models"
            width="80"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="错误个数"
            align="center"
            prop="errcount"
            width="90"
            :show-overflow-tooltip="true"
          />
          <el-table-column label="详情" align="center">
            <template slot-scope="scope">
              <CVdataParameter
                :key="scope.row.docid"
                :docid="scope.row.docid"
                :starttime="queryParams.starttime"
                function="tableextract"
              />
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <!-- <el-button
                            v-if="JSON.parse(scope.row.imgurl).length>0"
                            @click="onPreview(scope.row)"
                            size="mini"
                            type="text"
                            >图片预览 |</el-button>
                              <el-button
                            v-if="JSON.parse(scope.row.imgurl).length>0"
                            type="text"
                            size="mini"
                            @click="handlePopImgs(scope.row)">
                            图片明细 |</el-button>   -->
              <CVdataOperation
                cvmodel="table-extract-requests"
                :approver-list="approver_list"
                :docid="scope.row.docid"
                :dowdate="dowdate"
                :show-img="scope.row.imgurl=='true'"
                :show-zip="scope.row.dsturl=='true'&&scope.row.exporttype!='xlsx'"
                :show-xlsx="scope.row.dsturl=='true'&&scope.row.exporttype=='xlsx'"
                :dsturl="scope.row.dsturl"
                :zip-url="scope.row.dsturl"
                :zip-source-url="scope.row.zipurl"
                :input-pdf-url="scope.row.pdfurl"
                :table-export-zip-url="scope.row.dsturl"
                :table-export-xlsx-url="scope.row.dsturl"
                :show-input-pdf="scope.row.pdfurl=='true'"
                :show-input-zip="scope.row.zipurl=='true'"
                :show-imgs="scope.row.pagecount!=0&&scope.row.pagecount!=1&&scope.row.imgurl=='true'&&scope.row.imgurl!='[]'"
              />
              <!-- <el-link
                             v-if="scope.row.dsturl!=''"
                            slot="reference"
                            type="primary"
                                :href="fileDownload(scope.row,2)"
                                >下载zip |
                            </el-link>                     -->
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageIndex"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />

        <!-- 图片显示对话框 -->
        <!-- <el-dialog :title="title" :visible.sync="open" width="500px">
                    <div>
                        <ul>
                            <li v-for="(item,index) in imgurls" :key="index" >

                                   <el-link
                                    slot="reference"
                                    type="primary"
                                        :href="replaceUrl2(item.url)"
                                        download="a"
                                        target="_blank"
                                        >
                                        {{getName(item.url)}}
                                    </el-link>
                                    |
                                <el-link
                                     slot="reference"
                                        type="primary"
                                    :href="fileDownload2(item.url)"
                                 >下载
                                 </el-link>
                            </li>
                        </ul>
                    </div>
                </el-dialog> -->
      </el-card>
      <!-- <el-image-viewer
                        v-if="showViewer"
                        :initial-index="1"
                        style="width: 98%; height: 98%"
                        :on-close="closeViewer"
                        :url-list="viewPhotoList"
                        /> -->
    </template>
  </BasicLayout>
</template>

<script>
import { addTableExtractRequests, delTableExtractRequests, getTableExtractRequests, listTableExtractRequests, updateTableExtractRequests } from '@/api/cvdata/table-extract-requests'
// import elImageViewer from 'element-ui/packages/image/src/image-viewer.vue'
import CVdataOperation from '@/components/Cdata/Operation.vue'
import CVdataExport from '@/components/Cdata/Export.vue'
import CVdataParameter from '@/components/Cdata/Parameter.vue'
export default {
  name: 'TableExtractRequests',
  components: {
    //  elImageViewer,
    CVdataOperation, CVdataExport, CVdataParameter
  },
  data() {
    return {
      // 下载参数日期
      dowdate: undefined,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      isEdit: false,
      // 类型数据字典
      typeOptions: [],
      tableExtractRequestsList: [],
      statusOptions: [],
      errstatusOptions: [],
      // 关系表类型

      // 查询参数
      queryParams: {
        pageIndex: 1,
        pageSize: 10,
        cvmodel: 'table-extract-requests',
        origin: undefined,
        errorstatus: undefined,
        docid: undefined,
        starttime: undefined,
        status: 'noFinished',
        models: undefined,
        thirdclienttype: undefined,
        isExport: false
      },
      // 图片电子
      // imgurls:[],
      approver_list: [],
      approver_function: 'tableextract',
      // 表单参数
      form: {
      },
      showViewer: false,
      viewPhotoList: [],
      // 表单校验
      rules: { docid: [{ required: true, message: 'id不能为空', trigger: 'blur' }],
        starttime: [{ required: true, message: '(:s)不能为空', trigger: 'blur' }],
        status: [{ required: true, message: ', 0:, 1:, 2:不能为空', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.getList()
    this.getDicts('pdf_table_status').then(response => {
      this.statusOptions = response.data
    })
    this.getDicts('pdf_edit_errstatus').then(response => {
      this.errstatusOptions = response.data
    })
    this.getDicts('approver_cvdata_list').then((response) => {
      this.approver_list = response.data
    })
  },
  methods: {
    /** 查询参数列表 */
    getList() {
      this.loading = true
      listTableExtractRequests(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.tableExtractRequestsList = response.data.list
        this.total = response.data.count
        this.loading = false
        console.log(this.tableExtractRequestsList)
        console.log('response:',response)
      }
      )
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {

        id: undefined
      }
      this.resetForm('form')
    },
    getImgList: function() {
      this.form[this.fileIndex] = this.$refs['fileChoose'].resultList[0].fullUrl
    },
    fileClose: function() {
      this.fileOpen = false
    },
    statusFormat(row) {
      return this.selectDictLabel(this.statusOptions, row.status)
    },
    // 图片弹出框
    handlePopImgs(row) {
      this.imgurls = JSON.parse(row.imgurl)
      this.open = true
    },
    replaceUrl2(imgurl) {
      var s = imgurl
      return s.replace('"', '').replace('-internal', '')
    },
    getName(url) {
      const i = url.lastIndexOf('/')
      const name = url.substring(i + 1)
      console.log(name)
      return name
    },
    fileDownload(row, filetype) {
      return process.env.VUE_APP_BASE_API + '/api/v1/table-extract-requests/download/' + row.docid + '/' + this.dowdate
      //    downloadPdfEditRequest(row).then()
    },
    fileDownload2(url) {
      var s = url.replaceAll('/', '@')
      return process.env.VUE_APP_BASE_API + '/api/v1/table-extract-requests/downloadurl/' + s
      //    downloadPdfEditRequest(row).then()
    },
    // 关系
    // 文件
    /** 搜索按钮操作 */
    handleQuery() {
      this.dowdate = this.queryParams.starttime
      this.queryParams.pageIndex = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dowdate = undefined
      this.dateRange = []
      this.queryParams.errorstatus = undefined
      this.queryParams.starttime = undefined
      this.queryParams.models = undefined
      this.queryParams.thirdclienttype = undefined
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加TableExtractRequests'
      this.isEdit = false
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.docid)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const docid =
                row.docid || this.ids
      getTableExtractRequests(docid).then(response => {
        this.form = response.data
        this.open = true
        this.title = '修改TableExtractRequests'
        this.isEdit = true
      })
    },
    closeViewer() {
      this.showViewer = false
    },
    onPreview(row) {
      const objectlist = JSON.parse(row.imgurl)
      const temp = []
      objectlist.forEach(element => {
        temp.push(element.url)
      })
      this.viewPhotoList = temp

      this.showViewer = true
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.docid !== undefined) {
            updateTableExtractRequests(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(response.msg)
                this.open = false
                this.getList()
              } else {
                this.msgError(response.msg)
              }
            })
          } else {
            addTableExtractRequests(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(response.msg)
                this.open = false
                this.getList()
              } else {
                this.msgError(response.msg)
              }
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      var Ids = (row.docid && [row.docid]) || this.ids

      this.$confirm('是否确认删除编号为"' + Ids + '"的数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function() {
        return delTableExtractRequests({ 'ids': Ids })
      }).then((response) => {
        if (response.code === 200) {
          this.msgSuccess(response.msg)
          this.open = false
          this.getList()
        } else {
          this.msgError(response.msg)
        }
      }).catch(function() {
      })
    }
  }
}
</script>
