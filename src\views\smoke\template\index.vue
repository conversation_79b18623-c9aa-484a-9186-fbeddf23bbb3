<template>
  <BasicLayout>
    <template #wrapper>
      <el-card class="box-card">
        <el-form ref="queryForm" :model="queryParams" :inline="true" label-width="68px">
          <el-form-item label="业务" prop="service">
            <el-select
              v-model="queryParams.service"
              placeholder="请选择业务"
              size="small"
              clearable
            >
              <el-option
                v-for="item in serviceOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
            >新增
            </el-button>
          </el-col>
        </el-row>
        <div class="smoke-table">
          <el-table v-loading="loading" :data="templateList" style="width: 100%;" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" /><el-table-column
              label="模板"
              align="center"
              prop="name"
              :show-overflow-tooltip="true"
            /><el-table-column
              label="业务"
              align="center"
              prop="service"
              width="150"
              :show-overflow-tooltip="true"
            />
            <el-table-column
              label="host"
              align="center"
              prop="host"
              width="300"
              :show-overflow-tooltip="true"
            />
            <el-table-column
              label="接口"
              align="center"
              prop="uri"
              :show-overflow-tooltip="true"
            />
            <el-table-column label="备注" align="center" prop="remark" :show-overflow-tooltip="true" />
            <el-table-column label="创建者" align="center" prop="creater" :show-overflow-tooltip="true" />
            <el-table-column label="详情" align="center">
              <template slot-scope="scope">
                <SmokeInfoData
                  :id="scope.row.id"
                  :key="scope.row.id"
                  function="template"
                />
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
              <template slot-scope="scope">
                <el-popconfirm
                  class="delete-popconfirm"
                  title="确认要新建任务吗?"
                  confirm-button-text="创建"
                  @onConfirm="handleCreateTaskByTemplate(scope.row)"
                >
                  <el-button
                    slot="reference"
                    size="mini"
                    type="text"
                    icon="el-icon-plus"
                  >创建任务
                  </el-button>
                </el-popconfirm>
                <el-popconfirm
                  class="delete-popconfirm"
                  title="确认要修改吗?"
                  confirm-button-text="修改"
                  @onConfirm="handleUpdate(scope.row)"
                >
                  <el-button
                    slot="reference"
                    size="mini"
                    type="text"
                    icon="el-icon-edit"
                  >修改
                  </el-button>
                </el-popconfirm>
                <el-popconfirm
                  class="delete-popconfirm"
                  title="确认要复制模板吗?"
                  confirm-button-text="复制"
                  @onConfirm="handleCopy(scope.row)"
                >
                  <el-button
                    slot="reference"
                    size="mini"
                    type="text"
                    icon="el-icon-document-copy"
                  >复制
                  </el-button>
                </el-popconfirm>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.page_index"
          :limit.sync="queryParams.page_size"
          @pagination="getList"
        />
        <SmokeCreateDialog
          :template-id="templateId"
          :open.sync="open"
          :mode="mode"
          :title="title"
          :update="update"
          :form-options="formOptions"
          :service-options="serviceOptions"
          @close="cancel"
        />
      </el-card>
    </template>
  </BasicLayout>
</template>

<script>
import SmokeInfoData from '@/components/Smoke/SmokeInfoData'
import SmokeCreateDialog from '@/components/Smoke/SmokeCreateDialog'
import axios from 'axios'

export default {
  name: 'SmokeTemplate',
  components: {
    SmokeInfoData,
    SmokeCreateDialog

  },
  data() {
    return {
      statusOptions: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      configEdit: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      isEdit: false,
      isUseTemplate: false,
      // 模板列表
      templateList: [],
      // 获取形式参数
      formOptions: [],
      // 业务类型
      serviceOptions: [],
      // 查询参数
      queryParams: {
        page_index: 1,
        page_size: 10,
        service: undefined
      },
      // 表单校验
      rules: {
        title: [{ required: true, message: '任务不能为空', trigger: 'blur' }],
        status: [{ required: true, message: '状态不能为空', trigger: 'blur' }]
      },
      templateId: undefined,
      // title: '',
      update: false,
      mode: 'template'
    }
  },
  created() {
    this.getList()
    this.getDicts('pdf_smoke_status').then((response) => {
      this.statusOptions = response.data
    })
    this.getDicts('pdf_smoke_service').then((response) => {
      this.serviceOptions = response.data
    })
  },
  methods: {
    setCellStyle() {
      return 'white-space: pre-wrap;'
    },
    convertStatus(item) {
      return this.selectDictLabel(this.statusOptions, item)
    },
    convertLongtime(time) {
      if (time === 0) {
        return ''
      }
      var date = new Date(time * 1000)
      var DD = String(date.getDate()).padStart(2, '0') // 获取日
      var MM = String(date.getMonth() + 1).padStart(2, '0') // 获取月份，1 月为 0
      var yyyy = date.getFullYear() // 获取年
      var hh = String(date.getHours()).padStart(2, '0') // 获取当前小时数(0-23)
      var mm = String(date.getMinutes()).padStart(2, '0') // 获取当前分钟数(0-59)
      var ss = String(date.getSeconds()).padStart(2, '0') // 获取当前秒数(0-59)
      var today = yyyy + '-' + MM + '-' + DD + ' ' + hh + ':' + mm + ':' + ss
      return today
    },
    /** 查询参数列表 */
    getList() {
      this.loading = true
      axios.post('http://10.213.40.66:8001/data-processing/smoke/templates/search', this.queryParams).then((response) => {
        this.templateList = response.data.data.templates
        this.total = response.data.data.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.templateId = undefined
      this.handleQuery()
    },
    // 关系
    // 文件
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageIndex = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.open = true
      this.templateId = undefined
      this.title = '添加模板'
      this.update = false
      this.mode = 'template'
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.title = '修改模板'
      this.templateId = row.id
      this.update = true
      this.open = true
      this.mode = 'template'
    },
    handleCreateTaskByTemplate(row) {
      this.templateId = row.id
      this.open = true
      this.update = false
      this.title = '创建任务'
      this.mode = 'task'
    },
    handleCopy(row) {
      this.templateId = row.id
      this.open = true
      this.update = false
      this.title = '创建模板'
      this.mode = 'template'
    }
  }
}
</script>

<style>
.smoke-table .el-table .cell {
    display: inline-block;
    justify-content: center;
    align-items: center;
}
</style>
