import axios from 'axios'
import store from '@/store'
import { sleep } from './utils'

export const axios_get_docID = axios.create()
axios_get_docID.interceptors.response.use(
  async(response) => {
    return Promise.resolve(response.request.responseText)
  }
)

// 发起axios请求，设置响应的拦截器
// 若无响应，则进行一定次数的重试（每次失败次数都会累计）
export const axios_wait_doc = axios.create({
  timeout: 10000,
  __retryCount: 0,
  retry: 1000,
  retryDelay: 1000 // 请求失败时，重试延时
})

axios_wait_doc.interceptors.request.use(config => {
  if (config.cancelToken === undefined) {
    config.cancelToken = new axios.CancelToken(function executor(cancel) {
      store.commit('request/addHttpRequest', cancel)// 存储 cancle
    })
  }
  return config
})
axios_wait_doc.interceptors.response.use(
  async(response) => {
    const { status, config } = response

    if (status === 200) {
      return Promise.resolve(response.data)
    } else if (status === 201) {
      // created
      if (config.__retryCount >= config.retry) {
        return Promise.reject('超过等待时间(重试次数)! ')
      }
      config.__retryCount += 1

      console.log('等待重试...')
      await sleep(config.retryDelay)
      return axios_wait_doc(config)
    } else {
      // error
      return Promise.reject('获取转化结果失败')
    }
  },
  async(error) => {
    if (error.message === 'interrupt') {
      console.log('文件处理中断')
      return Promise.resolve()
    }

    const config = error.config
    if (config.__retryCount >= config.retry) {
      return Promise.reject('超过等待时间(重试次数)! ' + error)
    }
    config.__retryCount += 1

    console.log('错误重试...')
    await sleep(config.retryDelay)
    return axios_wait_doc(config)
  }
)
