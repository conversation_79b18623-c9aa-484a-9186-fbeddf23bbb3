FROM hub-mirror.wps.cn/sreopen/node:16.15 as build-stage
WORKDIR /app
COPY . .

ARG GOADMIN=//pcv-test-admin.wps.cn/api/
ARG TRANSFORM=//pcv-test.wps.cn/toolsys/model/
ARG AUTOTEST=https://pcv-test.wps.cn/layout/autotest/
ARG WPSOAUTH='https://openapi.wps.cn/oauthapi/v2/authorize?response_type=code\&appid=AK20211129JSIFLT\&autologin=false\&redirect_uri=https://pcv-test-admin.wps.cn/auth-redirect\&scope=user_info'


RUN sed -i "s|\${GOADMIN}|${GOADMIN}|g" .env.production \
    && sed -i "s|\${TRANSFORM}|${TRANSFORM}|g" .env.production \
    && sed -i "s|\${AUTOTEST}|${AUTOTEST}|g" .env.production \
    && sed -i "s|\${WPSOAUTH}|${WPSOAUTH}|g" .env.production \
    && cat .env.production \
    && rm -rf node_modules \
    && tar -xzf node_modules.tar.gz \
    && npm run build:prod \
    && rm -rf node_modules

# production stage
FROM hub-mirror.wps.cn/sreopen/nginx:1.23
COPY --from=build-stage /app/default.conf /etc/nginx/conf.d/default.conf
COPY --from=build-stage /app/nginx.conf /etc/nginx/nginx.conf
COPY --from=build-stage /app/dist /usr/share/nginx/html

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
