<template>
  <el-form v-model="data" label-width="120px" label-position="left">
    <div v-for="(row,index) in struct" :key="index">
      <EasyForm v-if="row.value.type === 'form'" v-model="data[row.value.prop]" :struct="row.value.value" />
      <el-form-item
        v-else
        :key="row.prop"
        :label="row.label"
        :prop="row.prop"
      >
        <component
          :is="row.value.type"
          v-if="row.value.type !== 'el-select'"
          v-model="data[row.value.prop]"
          v-bind="getComponentProps(row)"
          :disabled="!editable"
        />
        <!--el-select单独处理-->
        <el-select
          v-else
          v-model="data[row.value.prop]"
          placeholder="请选择"
          size="small"
          :disabled="!editable"
          :multiple="row.value.multi === true"
        >
          <el-option
            v-for="option in row.value.options"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>
    </div>
  </el-form>
</template>

<script>

export default {
  name: 'EasyForm',
  props: {
    data: {
      type: Object,
      default: () => ({})
    },
    struct: {
      type: Array,
      default: () => ([])
    },
    editable: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      // data: {}
    }
  },
  watch: {
    data: {
      immediate: true,
      handler(newVal) {
        this.$emit('input', newVal)
      },
      deep: true
    },
    struct: {
      immediate: true,
      handler(newVal, oldVal) {
      },
      deep: true
    }
  },
  methods: {
    getComponentProps(field) {
    },
    handleChange(row, value) {
      this.$set(this.easyData, row.value.prop, value)
    },
    getEasyData() {
      return this.easyData
    }
  }
}
</script>

