import request from '@/utils/request'

// 查询样张列表
export function listSamples(form) {
  return request({
    url: '/api/v1/sample/search',
    method: 'post',
    data: form
  })
}

// 新增样张
export function uploadSample(query) {
  return request({
    url: '/api/v1/sample/upload',
    method: 'post',
    params: query
  })
}

// 修改样张信息
export function updateSample(form) {
  return request({
    url: '/api/v1/sample',
    method: 'put',
    data: form
  })
}

// 删除样张信息
export function deleteSample(form) {
  return request({
    url: '/api/v1/sample',
    method: 'delete',
    data: form
  })
}

// 根据object_key获取url
export function getSampleUrl(form) {
  return request({
    url: '/api/v1/sample/getUrl',
    method: 'post',
    data: form
  })
}

// 查询样张列表
export function SearchSamples(form) {
  return request({
    url: '/api/v1/sample/search',
    method: 'post',
    data: form
  })
}
