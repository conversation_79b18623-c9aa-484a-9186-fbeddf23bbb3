
<template>
  <BasicLayout>
    <template #wrapper>
      <el-card class="box-card">
        <el-form ref="queryForm" :model="queryParams" :inline="true" label-width="68px">
          <el-form-item label="docid">
            <el-input
              v-model="queryParams.docid"
              placeholder="请输入docid"
              clearable
              size="small"
            />
          </el-form-item>
          <el-form-item label="origin" prop="origin"><el-input
            v-model="queryParams.origin"
            placeholder="请输入origin"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="日期">
            <el-date-picker
              v-model="queryParams.starttime"
              size="small"
              type="date"

              align="right"
              value-format="yyyy-MM-dd"
            /> </el-form-item>
          <el-form-item label="状态" prop="status"><el-select
            v-model="queryParams.status"
            placeholder="PdfEditV2Request状态"
            clearable
            size="small"
          >
            <el-option
              v-for="dict in statusOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
          </el-form-item>

          <el-form-item label="errmsg" prop="errmsg"><el-input
            v-model="queryParams.errmsg"
            placeholder="请输入异常信息"
            clearable
            size="small"
          />
          </el-form-item>
          <el-form-item>
            <el-button v-permisaction="['cvdata:pdfEditV2Request:select']" type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button v-permisaction="['cvdata:pdfEditV2Request:refresh']" icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
          <el-form-item label="错误详情">
            <el-checkbox
              @change="errShowMsgClick()"
            />
          </el-form-item>
        </el-form>

        <el-table v-loading="loading" :data="pdfEditV2RequestList" span="1.36">
          <el-table-column v-if="false" type="selection" width="5" align="center" /><el-table-column
            label="docid"
            align="center"
            prop="docid"
            width="320"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="origin"
            align="center"
            prop="origin"
            width="70"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="日期"
            align="center"
            prop="starttime"
            width="180"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="状态"
            align="center"
            prop="status"
            :formatter="statusFormat"
            width="60"
          >
            <template slot-scope="scope">
              {{ statusFormat(scope.row) }}
            </template>
          </el-table-column><el-table-column
            label="耗时(s)"
            align="center"
            prop="duration"
            width="120"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            v-if="showErrMsgs"
            label="错误详情"
            align="center"
            prop="err_msg"
            width="500"
            :show-overflow-tooltip="true"
          />
          <el-table-column label="详情" align="center">
            <template slot-scope="scope">
              <CVdataParameter
                :key="scope.row.docid"
                :docid="scope.row.docid"
                :starttime="queryParams.starttime"
                function="pdfEditV2"
              />
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <CVdataOperation
                cvmodel="pdf-edit-v2-request"
                :approver-list="approver_list"
                :docid="scope.row.docid"
                :dowdate="dowdate"
                :show-img="scope.row.img_url=='true'"
                :show-imgs="scope.row.img_urls=='true'"
                :show-input-pdf="scope.row.pdf_url=='true'"
                :is-pdf-edit-v2="true"
              />
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageIndex"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>

    </template>
  </BasicLayout>
</template>

<script>
import { listPdfEditV2Request } from '@/api/cvdata/pdf-edit-v2-request'
import CVdataOperation from '@/components/Cdata/Operation.vue'
import CVdataParameter from '@/components/Cdata/Parameter.vue'

export default {
  name: 'PdfEditV2Request',
  components: {
    CVdataOperation, CVdataParameter
  },
  data() {
    return {
      // 下载参数日期
      dowdate: undefined,
      // 下载参数状态
      dowstatus: undefined,
      // 下载docid
      dowdocid: undefined,
      // 遮罩层
      loading: true,
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      showErrMsgs: false,
      // 总条数
      total: 0,
      islatest: false,
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      // 图片电子
      imgurls: [],

      open: false,
      isEdit: false,
      // 类型数据字典
      typeOptions: [],
      pdfEditV2RequestList: [],
      statusOptions: [],
      errstatusOptions: [],
      // 关系表类型
      showPageserr: false,
      // 查询参数
      queryParams: {
        pageIndex: 1,
        pageSize: 10,
        cvmodel: 'pdf-edit-v2-request',
        origin: undefined,
        errorstatus: undefined,
        starttime: undefined,
        status: 'noFinished',
        docid: undefined,
        errmsg: undefined
      },
      showViewer: false,
      viewPhotoList: [],
      approver_list: [],
      // 表单参数
      form: {
      },
      // 表单校验
      rules: { starttime: [{ required: true, message: '日期不能为空', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.getList()
    this.getDicts('pdf_edit_v2_status').then(response => {
      this.statusOptions = response.data
    })
    this.getDicts('pdf_edit_v2_errstatus').then(response => {
      this.errstatusOptions = response.data
    })
    this.getDicts('approver_cvdata_list').then((response) => {
      this.approver_list = response.data
    })
  },
  methods: {
    /** 查询参数列表 */
    getList() {
      this.loading = true
      listPdfEditV2Request(this.addDateRange(this.queryParams)).then(response => {
        this.pdfEditV2RequestList = response.data.list
        this.total = response.data.count
        this.loading = false
      }
      )
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined
      }
      this.resetForm('form')
    },
    statusFormat(row) {
      return this.selectDictLabel(this.statusOptions, row.status)
    },
    errShowMsgClick() {
      this.showErrMsgs = !this.showErrMsgs
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.dowdate = this.queryParams.starttime
      this.dowstatus = this.queryParams.status
      this.dowdocid = this.queryParams.docid
      this.pageserr = this.queryParams.pageserr
      this.queryParams.pageIndex = 1
      this.getList()
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.dowstatus = undefined
      this.queryParams.dowdate = undefined
      this.queryParams.docid = undefined
      this.queryParams.errorstatus = undefined
      this.queryParams.starttime = undefined
      this.queryParams.errmsg = undefined
      this.resetForm('queryForm')
      this.handleQuery()
    }
  }
}
</script>
