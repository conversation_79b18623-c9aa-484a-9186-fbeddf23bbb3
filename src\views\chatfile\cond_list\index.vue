<template>
  <BasicLayout>
    <template #wrapper>
      <el-card class="box-card">
        <!-- 查询模块 -->
        <el-form ref="queryForm" :model="queryParams" :inline="true" label-width="68px">
          <el-form-item label="规则类型">
            <el-select v-model="queryParams.cond_type" placeholder="请选择规则类型" clearable size="small"
              style="width: 140px">
              <el-option v-for="dict in chatfileCondTyeOptions" :key="dict.value" :label="dict.label"
                :value="dict.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select v-model="queryParams.status" placeholder="状态" clearable size="small" style="width: 120px">
              <el-option v-for="dict in condlistStatusOptions" :key="dict.value" :label="dict.label"
                :value="dict.value" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
        <!-- 新增按钮 -->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd">新增
            </el-button>
          </el-col>
        </el-row>
        <!-- 数据展示模块 -->
        <el-table v-loading="loading" :data="chatfileCondList" span="1.36" @sort-change="handleSort">
          <el-table-column v-if="hasMatchesColumn" type="expand">
            <template slot-scope="props">
              <el-table :data="props.row.matches" style="width: 100%;" :show-header="false" stripe border>
                <el-table-column prop="key" label="Key" />
                <el-table-column label="Type">
                  <template slot-scope="scope">
                    {{ getCondListMatchTypeLabelByValue(scope.row.type) }}
                  </template>
                </el-table-column>
                <el-table-column label="Value">
                  <template slot-scope="scope">
                    <div v-for="(item, index) in scope.row.value.split(',')" :key="index" class="value-item">
                      {{ item }}
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </template>
          </el-table-column>
          <el-table-column label="规则描述" align="center" prop="description" width="200" />
          <el-table-column label="规则类型" align="center" prop="cond_type" width="110" :show-overflow-tooltip="true">
            <template slot-scope="scope">
              {{ getCondTypeLabelByValue(scope.row.cond_type) }}
            </template>
          </el-table-column>
          <el-table-column label="模型提供方" align="center" prop="provider" width="150" :show-overflow-tooltip="true" />
          <el-table-column label="模型名称" align="center" prop="model_name" width="150" :show-overflow-tooltip="true" />
          <el-table-column label="是否强制" align="center" prop="force" width="110" :show-overflow-tooltip="true">
            <template slot-scope="scope">
              {{ getForceLabelByValue(parseInt(scope.row.force, 10)) }}
            </template>
          </el-table-column>
          <el-table-column label="权重" align="center" prop="level" sortable width="110" :show-overflow-tooltip="true" />
          <el-table-column label="创建人" align="center" prop="createBy" width="110" />
          <el-table-column label="更新人" align="center" prop="updateBy" width="110" :show-overflow-tooltip="true" />
          <el-table-column label="更新时间" align="center" prop="updatedAt" sortable width="180"
            :show-overflow-tooltip="true">
            <template slot-scope="scope">
              <span>{{ formatDate(scope.row.updatedAt) }}</span>
            </template>
          </el-table-column>
          <!-- 切换状态按钮 -->
          <el-table-column label="是否启用" align="center" prop="status" width="120">
            <template slot-scope="scope">
              <el-switch v-model="scope.row.status" :active-value="1" :inactive-value="0"
                @change="updateStatus(scope.row)" />
            </template>
          </el-table-column>
          <!-- 操作模块 -->
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="180">
            <template slot-scope="scope">
              <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">修改</el-button>
              <el-button size="mini" type="text" icon="el-icon-delete" @click="deleteItem(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageIndex"
          :limit.sync="queryParams.pageSize" @pagination="getList" />

        <!-- 添加cond list对话框 -->
        <el-drawer ref="drawer" :title="title" :visible="open" direction="rtl" :before-close="cancel"
          custom-class="demo-drawer" size="900px" :close-on-click-modal="false">
          <div class="demo-drawer__content">
            <el-form ref="form" :model="form" label-position="top" label-width="106px">
              <el-row>
                <el-col :span="8">
                  <el-form-item prop="cond_type" :rules="{ required: true, message: '请输入内容', trigger: 'blur' }">
                    <span slot="label">
                      规则类型
                      <el-tooltip content="还需要修改字典" placement="top">
                        <i class="el-icon-question" />
                      </el-tooltip>
                    </span>
                    <el-select v-model="form.cond_type" clearable>
                      <el-option v-for="dict in chatfileCondTyeOptions" :key="dict.value" :label="dict.label"
                        :value="dict.value" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item prop="provider" :rules="{ required: true, message: '请输入内容', trigger: 'blur' }">
                    <span slot="label">
                      模型提供方
                      <el-tooltip content="model provider" placement="top">
                        <i class="el-icon-question" />
                      </el-tooltip>
                    </span>
                    <el-input v-model="form.provider" placeholder="" />
                  </el-form-item>
                </el-col>
                <el-col :span="6" :offset="2">
                  <el-form-item prop="model_name" :rules="{ required: true, message: '请输入内容', trigger: 'blur' }">
                    <span slot="label">
                      模型名称
                      <el-tooltip content="model name" placement="top">
                        <i class="el-icon-question" />
                      </el-tooltip>
                    </span>
                    <el-input v-model="form.model_name" placeholder="" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item prop="force" :rules="{ required: true, message: '请输入内容', trigger: 'blur' }">
                    <span slot="label">
                      是否强制生效
                      <el-tooltip content="只对模型选择生效，匹配成功后，将强制切换模型" placement="top">
                        <i class="el-icon-question" />
                      </el-tooltip>
                    </span>
                    <el-radio-group v-model.number="form.force">
                      <el-radio :label="1">是</el-radio>
                      <el-radio :label="0">否</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item prop="level" :rules="{ required: true, message: '请输入内容', trigger: 'blur' }">
                    <span slot="label">
                      权重
                      <el-tooltip content="同类型规则降序匹配,min=0,max=999" placement="top">
                        <i class="el-icon-question" />
                      </el-tooltip>
                    </span>
                    <el-input-number v-model="form.level" controls-position="right" :min="0" :max="999" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item prop="need_audit" :rules="{ required: true, trigger: 'blur' }">
                    <span slot="label">
                      是否开启高召分流
                      <el-tooltip content="开启高召分流，会对送入大模型的内容审核一遍，若审核未通过则会切换为指定的模型" placement="top">
                        <i class="el-icon-question" />
                      </el-tooltip>
                    </span>
                    <el-radio-group v-model.number="form.need_audit">
                      <el-radio :label="1">是</el-radio>
                      <el-radio :label="0">否</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col v-if="form.need_audit === 1" :span="6">
                  <el-form-item prop="audit_provider" :rules="{ required: true, message: '请输入内容', trigger: 'blur' }">
                    <span slot="label">
                      高召分流-模型提供方
                      <el-tooltip content="model provider" placement="top">
                        <i class="el-icon-question" />
                      </el-tooltip>
                    </span>
                    <el-input v-model="form.audit_provider" placeholder="" />
                  </el-form-item>
                </el-col>
                <el-col v-if="form.need_audit === 1" :span="6" :offset="2">
                  <el-form-item prop="audit_model_name" :rules="{ required: true, message: '请输入内容', trigger: 'blur' }">
                    <span slot="label">
                      高召分流-模型名称
                      <el-tooltip content="model name" placement="top">
                        <i class="el-icon-question" />
                      </el-tooltip>
                    </span>
                    <el-input v-model="form.audit_model_name" placeholder="" />
                  </el-form-item>
                </el-col>

                <el-col>
                  <el-form-item prop="matches" :rules="matchNotEmpty">
                    <span slot="label">
                      匹配条件
                      <el-button type="primary" @click="addMatchItem(form.id)">新增</el-button>
                    </span>
                  </el-form-item>
                </el-col>
                <el-col>
                  <el-row :gutter="20">
                    <el-form-item v-for="(match, index) in form.matches" :key="index" :label="'规则' + (index + 1)"
                      :prop="'matches.' + index + '.value'"
                      :rules="[{ required: true, message: '请输入内容', trigger: 'blur' }]">
                      <el-row :gutter="10">
                        <el-col :span="8">
                          <el-input v-model="match.key" disabled />
                        </el-col>
                        <el-col :span="8">
                          <el-select v-model="match.type" disabled>
                            <el-option v-for="dict in chatfileCondListMatchTypeOptions" :key="dict.value"
                              :label="dict.label" :value="dict.value" />
                          </el-select>
                        </el-col>
                        <el-col :span="4">
                          <el-button type="primary" @click.prevent="updateMatch(match)">修改</el-button>
                        </el-col>
                        <el-col :span="4">
                          <el-button type="danger" @click.prevent="removeMatch(match)">删除</el-button>
                        </el-col>
                      </el-row>
                      <el-row :style="{ marginTop: '10px' }">
                        <el-col :span="24">
                          <el-input v-model="match.value" type="textarea" :rows="3" disabled />
                        </el-col>
                      </el-row>
                    </el-form-item>
                  </el-row>
                </el-col>
                <el-col>
                  <el-form-item prop="description" :rules="{ required: true, message: '请输入内容', trigger: 'blur' }">
                    <span slot="label">
                      规则描述
                    </span>
                    <el-input v-model="form.description" type="textarea" :rows="3" placeholder="请输入规则描述" />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
            <div class="demo-drawer__footer">
              <el-button type="primary" @click="submitForm">确 定</el-button>
              <el-button @click="cancel">取 消</el-button>
            </div>
          </div>
        </el-drawer>

        <!-- match规则对话框 -->
        <el-dialog :title="match_title" :visible="match_open" :before-close="matchCancel" width="500px"
          :close-on-click-modal="false">
          <el-form ref="match_form" :model="match_form" :rules="rules" label-width="80px">
            <el-form-item label="字段名称" prop="key">
              <el-input v-model="match_form.key" placeholder="字段名称" clearable />
            </el-form-item>
            <el-form-item label="匹配类型" prop="type">
              <el-select v-model="match_form.type" placeholder="匹配类型" clearable>
                <el-option v-for="dict in chatfileCondListMatchTypeOptions" :key="dict.value" :label="dict.label"
                  :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="字段值" prop="value" :rules="matchValueRule">
              <span slot="label">
                字段值
              </span>
              <el-input v-model="match_form.value" type="textarea" :rows="3" placeholder="请输入描述" />
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitMatchForm">确 定</el-button>
            <el-button @click="matchCancel">取 消</el-button>
          </div>
        </el-dialog>
      </el-card>
    </template>
  </BasicLayout>
</template>
<style>
.value-item {
  font-weight: bold;
  background-color: hsl(0, 0%, 94%);
  padding: 2px 4px;
  margin-right: 8px;
  margin-bottom: 4px;
  border-radius: 4px;
  display: inline-block;
}
</style>
<script>
import { addCondList, delCondList, listCondList, updateCondList, delMatch, addMatch, updateMatch } from '@/api/chatfile/cond_list'
import { isNumberStr } from '@/utils/generator'
import moment from 'moment'

export default {
  name: 'CondList',
  components: {},
  data() {
    return {
      loading: true,
      open: false,
      isEdit: false,
      showModal: false,
      total: 0,

      // match 表单提交
      match_title: '',
      match_open: false,
      match_form: {},
      match_isEdit: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 弹出层标题
      title: '',
      chatfileCondTyeOptions: [],
      chatfileCondListMatchTypeOptions: [],
      chatfileForceTyeOptions: [],
      condlistStatusOptions: [],
      chatfileCondList: [],
      // 查询参数
      queryParams: {
        pageIndex: 1,
        pageSize: 10
      },
      // 表单参数
      form: {
        matches: [{ key: '', type: '', value: '' }]
      },
      oldForm: {},
      // 表单校验
      rules: {
        key: [{ required: true, message: '规则名称不能为空', trigger: 'blur' }],
        type: [{ required: true, message: '规则类型不能为空', trigger: 'blur' }]
      }
    }
  },
  computed: {
    hasMatchesColumn() {
      return this.chatfileCondList.some(row => row.matches && row.matches.length)
    },
    matchValueRule() {
      return [
        { required: true, message: '规则不能为空', trigger: 'blur' },
        { validator: this.validateMatchValue, trigger: 'blur' }
      ]
    },
    matchNotEmpty() {
      return [
        { validator: this.validateMatchNotEmpty, trigger: 'blur' }
      ]
    }
  },
  created() {
    this.getList()
    this.getDicts('chatfile_cond_list_match_type').then(response => {
      this.chatfileCondListMatchTypeOptions = response.data
    })
    this.getDicts('chatfile_cond_type').then(response => {
      this.chatfileCondTyeOptions = response.data
    })
    this.getDicts('chatfile_cond_list_status').then(response => {
      this.condlistStatusOptions = response.data
    })
    this.getDicts('chatfile_force_type').then(response => {
      this.chatfileForceTyeOptions = response.data
    })
  },
  methods: {
    // 列表页
    getList() {
      this.loading = true
      listCondList(this.queryParams).then(response => {
        if (response.code === 200) {
          this.chatfileCondList = response.data.list
          this.total = response.data.count
        } else {
          this.msgError(response.msg)
        }
        this.loading = false
      })
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      this.open = true
      this.title = '修改规则列表'
      this.isEdit = true
      this.form = { ...row }
      this.oldForm = JSON.parse(JSON.stringify(row)) // 深拷贝
    },
    updateStatus(row) {
      var data = {
        id: row.id,
        status: row.status,
        updateBy: this.$store.state.user.name
      }
      updateCondList(data).then(response => {
        if (response.code === 200) {
          this.reset()
        } else {
          this.msgError(response.msg)
        }
      })
    },
    /** 删除按钮操作 */
    deleteItem(row) {
      this.$confirm('是否确认删除数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function () {
        return delCondList(row.id)
      }).then((response) => {
        if (response.code === 200) {
          this.msgSuccess(response.msg)
          this.open = false
          this.getList()
        } else {
          this.msgError(response.msg)
        }
      }).catch(function () {
      })
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加规则列表'
      this.isEdit = false
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        cond_type: undefined,
        cond_value: undefined,
        description: undefined,
        force: undefined,
        level: undefined,
        status: undefined,
        matches: []
      }
      this.resetForm('form')
      this.getList()
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageIndex = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.cond_type = undefined
      this.queryParams.cond_value = undefined
      this.queryParams.status = undefined
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs['form'].validate(valid => {
        if (valid) {
          const formData = this.isEdit ? this.getChangedFormValues() : { ...this.form }
          if (this.isEdit && (formData === undefined || Object.keys(formData).length === 0)) {
            this.open = false
            return
          }

          if (!this.isEdit) {
            formData.status = 0
            formData.createBy = this.$store.state.user.name
          }

          formData.updateBy = this.$store.state.user.name

          const action = this.isEdit ? updateCondList : addCondList
          action(formData).then(response => {
            if (response.code === 200) {
              this.msgSuccess(response.msg)
              this.open = false
              this.getList()
            } else {
              this.msgError(response.msg)
            }
          })
        }
      })
    },
    getChangedFormValues() {
      const changedValues = {}

      Object.keys(this.form).forEach(key => {
        if (this.form[key] !== this.oldForm[key] && key !== 'matches') {
          changedValues[key] = this.form[key]
        }
      })

      if (Object.keys(changedValues).length > 0 && this.form.id) {
        changedValues.id = this.form.id
      }

      return changedValues
    },
    closeAdd() {
      this.open = false
      this.reset()
    },
    formatDate(dateString) {
      return moment(dateString).format('YYYY-MM-DD HH:mm:ss')
    },
    handleSort({ prop, order }) {
      this.chatfileCondList.sort((a, b) => {
        let primary = 0
        if (a[prop] < b[prop]) primary = -1
        if (a[prop] > b[prop]) primary = 1
        if (order === 'descending') primary *= -1
        let secondary = (a['level'] < b['level']) ? 1 : -1
        if (a['level'] === b['level']) secondary = 0

        return primary || (primary === 0 ? secondary : 0)
      })
    },
    submitMatchForm: function () {
      this.$refs['match_form'].validate(valid => {
        if (valid) {
          this.match_form.createBy = this.$store.state.user.name
          this.match_form.updateBy = this.$store.state.user.name
          // 主表单为新增模式暂时存到this.form
          if (!this.isEdit) {
            if (!this.match_isEdit) {
              this.form.matches.push(JSON.parse(JSON.stringify(this.match_form)))
            }
            this.match_open = false
            return
          }
          // 判断match表单是否为编辑模式
          var action = this.match_isEdit ? updateMatch : addMatch
          action(this.match_form).then(response => {
            if (response.code === 200) {
              this.msgSuccess(response.msg)
              this.match_open = false
              this.getList()
              if (!this.match_isEdit) {
                this.form.matches.push(response.data)
              }
            } else {
              this.msgError(response.msg)
            }
          })
        }
      })
    },
    matchCancel() {
      this.match_open = false
      this.match_form = {}
      this.resetForm('match_form')
    },
    addMatchItem(cond_id) {
      this.match_form = {}
      this.match_isEdit = false
      this.match_title = '添加条件'
      this.match_form.cond_id = cond_id
      this.match_open = true
    },
    updateMatch(item) {
      this.match_title = '修改规则'
      this.match_isEdit = true
      this.match_form = item
      this.match_open = true
    },
    removeMatch(item) {
      this.$confirm('是否确认删除规则?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 主表单为新增模式直接从this.form删除
        if (!this.isEdit) {
          return { code: 200, msg: '删除成功' }
        }
        // 主表单为编辑模式 提交请求删除
        return delMatch(item.id)
      }).then((response) => {
        if (response.code === 200) {
          this.msgSuccess(response.msg)
          var index = this.form.matches.indexOf(item)
          if (index !== -1) {
            this.form.matches.splice(index, 1)
          }
          this.getList()
        } else {
          this.msgError(response.msg)
        }
      }).catch(() => {
      })
    },
    getCondTypeLabelByValue(value) {
      const option = this.chatfileCondTyeOptions.find(option => option.value === value)
      return option ? option.label : value
    },
    getCondListMatchTypeLabelByValue(value) {
      const option = this.chatfileCondListMatchTypeOptions.find(option => option.value === value)
      return option ? option.label : value
    },
    getForceLabelByValue(value) {
      const option = this.chatfileForceTyeOptions.find(option => parseInt(option.value, 10) === value)
      return option ? option.label : value
    },
    validateMatchValue(rule, value, callback) {
      if (!value) {
        return callback()
      }
      if (!this.match_form.type) {
        return callback(new Error('请先选择规则类型'))
      }
      if (this.match_form.type === 'exist') {
        if (this.match_form.value !== 'false' && this.match_form.value !== 'true') {
          return callback(new Error('请输入true或者false'))
        }
        return callback()
      } else if (this.match_form.type === 'equal') {
        return callback()
      } else if (this.match_form.type === 'parity' || this.match_form.type === 'more' || this.match_form.type === 'less') {
        return isNumberStr(this.match_form.value) ? callback() : callback(new Error('请输入数字，例如:1,2.3'))
      } else if (this.match_form.type === 'in' || this.match_form.type === 'not_in') {
        return callback()
      } else if (this.match_form.type === 'ones') {
        return this.match_form.value.split(',').every(element => isNumberStr(element)) ? callback() : callback(new Error('请输入一个数字序列，例如：1,2,3,4'))
      }
    },
    validateMatchNotEmpty(rule, value, callback) {
      if (this.isEdit) {
        return callback()
      }

      if (!this.form.matches || !this.form.matches.length) {
        return callback(new Error('规则列表个数不能为空'))
      }

      return callback()
    }
  }
}
</script>
