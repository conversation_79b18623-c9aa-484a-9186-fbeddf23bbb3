
<template>
  <BasicLayout>
    <template #wrapper>
      <el-card class="box-card">
        <el-form ref="queryForm" :model="queryParams" :inline="true" label-width="110px">
          <!-- <el-form-item label="日期">
            <el-date-picker
              v-model="queryParams.starttime"
              size="small"
              type="date"
              align="right"
              value-format="yyyy-MM-dd"
            />
          </el-form-item> -->
          <el-form-item label="dataset_id">
            <el-input
              v-model="queryParams.dataset_id"
              placeholder="请输入dataset_id"
              clearable
              size="small"
              style="width: 150px"
            />
          </el-form-item>
          <el-form-item label="评测人">
            <el-input
              v-model="queryParams.reviewer"
              placeholder="请输入评测人"
              clearable
              size="small"
              style="width: 180px"
            />
          </el-form-item>
          <!-- <el-form-item v-if="showLabelList" label="标签类别"><el-select
            v-model="queryParams.label_name"
            placeholder="选择标签类别"
            clearable
            size="small"
            multiple
          >
            <el-option
              v-for="dict in labelNameOptions"
              :key="dict.value"
              :label="dict.LabelName"
              :value="dict.LabelName"
            />
          </el-select>
          </el-form-item> -->
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
            >搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
          <!-- <el-form-item label="显示object_key">
            <el-checkbox
              @change="showOtherInfosClick()"
            />
          </el-form-item> -->
        </el-form>

        <!-- <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="success"
              icon="el-icon-edit"
              size="mini"
              :disabled="single"
              @click="handleUpdate"
            >修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="warning"
              icon="el-icon-edit"
              size="mini"
              :disabled="multiple"
              @click="handleUpdateLabel"
            >批量修改样张标签</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              icon="el-icon-delete"
              size="mini"
              :disabled="isEmpty"
              @click="handleDelete"
            >删除</el-button>
          </el-col>
        </el-row> -->
        <el-table v-loading="loading" :data="samplesList" span="11.1" :cell-style="{padding:'5'}" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column v-if="false" type="selection" width="5" align="left" /><el-table-column
            label="recordID"
            align="center"
            prop="record_id"
            width="80"
            :show-overflow-tooltip="true"
          />
          <!-- <el-table-column v-if="false" type="selection" width="5" align="left" /><el-table-column
            label="index"
            align="center"
            prop="index"
            width="60"
            :show-overflow-tooltip="true"
          /> -->
          <!-- <el-table-column
            label="sampleID"
            align="center"
            prop="sample_id"
            width="120"
            :show-overflow-tooltip="true"
          /> -->

          <el-table-column
            label="sampleID"
            align="center"
            width="160"
            prop="sample_id"
            sortable
            :show-overflow-tooltip="true"
          />
          <el-table-column v-if="false" type="selection" width="5" align="left" /><el-table-column
            label="scene"
            align="center"
            prop="scene"
            width="120"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="创建时间"
            align="center"
            prop="date"
            width="150"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="状态"
            align="center"
            prop="status"
            width="50"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="源文件"
            align="center"
            prop="origin_url.0.object_key"
            width="200"
            :show-overflow-tooltip="true"
          />
          <!-- <el-table-column label="源文件" align="center" width="200">
            <template slot-scope="scope">
              <div v-for="(url, index) in scope.row.origin_url" :key="index">{{ url.object_key }}</div>
            </template>
          </el-table-column> -->

          <el-table-column
            label="目标文件"
            align="center"
            prop="result_url"
            width="200"
            :show-overflow-tooltip="true"
          />
          <el-table-column label="渲染文件" align="center" width="200">
            <template slot-scope="scope">
              <div v-if="scope.row.rendering_url.length>1">
                <el-dropdown trigger="click">
                  <span class="el-dropdown-link">
                    点击查看
                    <i class="el-icon-arrow-down el-icon--right" />
                  </span>
                  <el-dropdown-menu slot="dropdown">
                    <div v-for="(url, index) in scope.row.rendering_url" :key="index">{{ url.object_key }}</div>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
              <div v-else>
                <div v-for="(url, index) in scope.row.rendering_url" :key="index">{{ url.object_key }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="其他文件" align="center" width="200">
            <template slot-scope="scope">
              <div v-if="scope.row.other_url.length>1">
                <el-dropdown trigger="click">
                  <span class="el-dropdown-link">
                    点击查看
                    <i class="el-icon-arrow-down el-icon--right" />
                  </span>
                  <el-dropdown-menu slot="dropdown">
                    <div v-for="(url, index) in scope.row.other_url" :key="index">{{ url.object_key }}</div>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
              <div v-else>
                <div v-for="(url, index) in scope.row.other_url" :key="index">{{ url.object_key }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            label="评测人"
            align="center"
            prop="reviewer"
            width="100"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="总分"
            align="center"
            prop="score"
            width="80"
            :show-overflow-tooltip="true"
          />
          <el-table-column label="评分细则" align="center">
            <template slot-scope="scope">
              <div>
                <!-- v-permisaction="['cvdata:All:viewparameter']" -->
                <el-button icon="el-icon-view" size="mini" @click="handleView(scope.row.score_detail)">查看</el-button>
              </div>
              <el-dialog :visible.sync="viewScoreDetail" :title="'评分细则'" class="json-viewer-container" width="500px">
                <!-- <el-button icon="el-icon-check" size="mini" @click="handleCopy">复制</el-button> -->
                <json-viewer :value="scoreDetailData" theme="dark" :expand-depth="300" />
                <div slot="footer" class="dialog-footer">
                  <el-button @click="cancelScoreDetail">关 闭</el-button>
                </div>
              </el-dialog>
            </template>
          </el-table-column>
          <el-table-column
            label="备注"
            align="center"
            prop="remark"
            width="100"
            :show-overflow-tooltip="true"
          />
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="danger"
                icon="el-icon-delete"
                @click="handleDeleteSingle(scope.row.record_id)"
              >删除</el-button>
            </template>
          </el-table-column>
          <!-- <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <div>
                <el-button
                  size="mini"
                  type="text"
                  @click="copyLink(scope.row.url)"
                >复制链接 |</el-button>
                <el-button
                  size="mini"
                  type="text"
                  :disabled="!acceptFileType.includes(scope.row.file_type)"
                  @click="onPreview(scope.row.file_type,scope.row.url)"
                >在线预览</el-button>
              </div>
              <el-image-viewer
                v-if="showViewer"
                style="width: 98%; height: 98%"
                :on-close="closeViewer"
                :url-list="viewPhotoList"
              />
            </template>
          </el-table-column> -->
        </el-table>

        <!-- 修改样张信息对话框 -->
        <!-- <el-dialog :title="title" :visible.sync="open" width="500px">
          <el-form ref="form" :model="form" :rules="rules" label-width="80px">
            <el-form-item v-show="!showHandleUpdateLabel" label="文件名" prop="label">
              <el-input v-model="form.initial_name" type="text" placeholder="请输入内容" />
            </el-form-item>
            <el-form-item label="标签类别:" prop="label">
              <el-select v-model="form.labels" placeholder="请选择" multiple @change="$forceUpdate()">
                <el-option
                  v-for="dict in labelNameOptions"
                  :key="dict.value"
                  :label="dict.LabelName"
                  :value="dict.LabelId"
                />
              </el-select>
            </el-form-item>
            <el-form-item v-show="!showHandleUpdateLabel" label="备注" prop="remark">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
          </div>
        </el-dialog> -->

        <pagination
          v-show="total>0"
          :total="total"
          :page-sizes="[100, 200, 300, 500]"
          :page.sync="queryParams.pageIndex"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>
    </template>
  </BasicLayout>
</template>

<script>
import axios from 'axios'
import { getHeaders } from '@/utils/get-header'
// import { listLabels } from '@/api/sample/label'
// import elImageViewer from 'element-ui/packages/image/src/image-viewer.vue'
export default {
  name: 'RecordSearch',
  // components: {
  //   elImageViewer
  // },
  props: {
    acceptFileType: {
      type: Array,
      default: function() {
        // 支持预览图片和pdf文件
        return ['image/png', 'image/jpg', 'image/jpeg', 'application/pdf', 'image/webp']
      }
    }
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 非空禁用
      isEmpty: true,
      showHandleUpdateLabel: false,
      // 总条数
      total: 0,
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      isEdit: false,
      showErrMsgs: false,
      // 类型数据字典
      samplesList: [],
      statusOptions: [],
      labelNameOptions: [],
      fileTypeOptions: undefined,
      // 是否只返回object_key
      isOnlyObjectKeyOptions: [
        { label: '是', value: false },
        { label: '否', value: true }
      ],
      // 是否显示标签列表
      showLabelList: false,
      // 标签列表
      labelList: [],
      // 关系表类型
      dowdate: undefined,
      imgUrls: [],
      // 查询参数
      queryParams: {
        pageIndex: 1,
        pageSize: 100,
        dataset_id: undefined,
        reviewer: undefined
      },
      // 是否展示其他信息
      showOtherInfos: false,
      // 表单参数
      form: {
        initial_name: '',
        labels: [],
        remark: ''
      },
      // 表单校验
      rules: { docid: [{ required: true, message: 'id不能为空', trigger: 'blur' }],
        starttime: [{ required: true, message: '(:s)不能为空', trigger: 'blur' }],
        status: [{ required: true, message: ', 0:, 1:, 2:不能为空', trigger: 'blur' }]
      },
      // 文件预览相关
      showViewer: false,
      viewPhotoList: [],
      // 查看评分细则
      viewScoreDetail: false,
      // 评分细则json数据
      scoreDetailData: {}
    }
  },
  created() {
    console.log('queryParams:', this.$route.query)
    this.initializeData()
    this.getDicts('sample_filetype').then(response => {
      this.fileTypeOptions = response.data
    })
  },
  watch: {
    // 监听路由变化，当dataset_id参数变化时重新加载数据
    '$route'(to, from) {
      console.log('路由变化:', to.query, from.query)
      if (to.query.dataset_id !== from.query.dataset_id) {
        this.initializeData()
      }
    }
  },
  methods: {
    /** 初始化数据 */
    initializeData() {
      const { dataset_id } = this.$route.query
      console.log('initializeData dataset_id:', dataset_id)

      // 重置数据
      this.samplesList = []
      this.total = 0
      this.queryParams.pageIndex = 1

      if (dataset_id) {
        this.queryParams.dataset_id = dataset_id
        console.log('设置 queryParams.dataset_id:', dataset_id)
        this.getList()
      } else {
        this.loading = false
      }
    },
    /** 查看评分细则 */
    handleView(score_detail) {
      // this.handleSelectDocid()
      this.scoreDetailData = score_detail
      this.viewScoreDetail = true
    },
    // 取消评分细则按钮
    cancelScoreDetail() {
      this.viewScoreDetail = false
      // this.reset()
    },
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.sample_id)
      this.single = selection.length !== 1
      this.multiple = this.getMultiple(selection.length)
      this.isEmpty = selection.length === 0
    },
    handleDeleteSingle(recordId) {
      this.$confirm('是否确认删除该记录？', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
      // 发送删除请求
        return axios({
          method: 'delete',
          headers: { ...getHeaders() },
          url: `/data-processing/records/${recordId}`,
          timeout: 80000
        })
      }).then((res) => {
        if (res.data.code === 200) {
          this.$message.success('删除成功')

          this.handleQuery()
        } else {
          this.$message.error(`删除失败：${res.data.msg}`)
        }
      }).catch((error) => {
        this.$message.error('网络请求失败，请重试')
        console.error('删除记录失败:', error)
      })
    },
    getMultiple(length) {
      return length <= 1
    },
    /** 查询参数列表 */
    getList() {
      this.loading = true
      // listRequests接口传入两个参数：pageIndex 和 pageSize
      // listEffect(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
      //   // const form = new FormData()
      //   // listSamples(form).then(response => {
      //   this.samplesList = response.data.result_data
      //   this.total = response.data.result_count
      //   this.loading = false
      // }
      // )
      var data_dict = {
        'dataset_id': parseInt(this.queryParams.dataset_id),
        // 'dataset_id': 2,
        'page_index': this.queryParams.pageIndex,
        'page_size': this.queryParams.pageSize
      }
      console.log('param:', data_dict)
      return new Promise((resolve, reject) => {
        axios({
          method: 'post',
          headers: { 'Cache-Control': 'no-cache', 'Content-Type': 'application/json', ...getHeaders() },
          url: 'http://10.213.40.66:8001/data-processing' + '/records/search',
          data: data_dict,
          responseType: 'json',
          processData: false,
          contentType: false,
          timeout: 80000 // 设置超时时间为80秒
        }).then(res => {
          if (res.data.code !== 200 || res.data.data === undefined || res.data.data.dataset_data === undefined) {
            return Promise.reject('服务请求失败!')
          }
          // 处理完成得到string类型的公式
          // this.samplesList = res.data.data.dataset_data
          console.log('res.data.data:', res.data.data)
          if (res.data.data.data_count <= 0) {
            this.message('warn', res.data.msg, '但数据为空 data_count = 0')
            this.loading = false
            return Promise.resolve(1)
          }
          const result_data = res.data.data.dataset_data
          var record_datas = []
          result_data.forEach(data => {
            data.record_data.forEach((record, index) => {
              console.log('record:', record)
              record.index = index
              if (record.sample_id === undefined) {
                record.sample_id = 0
              }
              record_datas.push(record)
            })
            // this.samplesList.index =
          })
          console.log('record_datas:', record_datas)
          this.samplesList = record_datas
          console.log('this.samplesList:', this.samplesList)
          this.total = res.data.data.data_count
          this.loading = false
          // console.log('create完成: ' + JSON.stringify(res.data.data.docID))
          resolve(res.data.data.data_count)
        }).catch((error) => {
          this.message('error', '服务请求失败! ' + error, '。具体信息: ' + error.response.data.msg)
          // return Promise.resolve(0)
          reject(error)
        })
      })
    },
    message(type, msg, dataMsg) {
      this.$message({
        showClose: true,
        message: msg + dataMsg,
        center: true,
        duration: 4000,
        type: type
      })
    },
    // 格式化
    format(imgsUrl) {
      const externalUrl = imgsUrl.replace(/-internal/g, '')
      const cleanUrl = externalUrl.replace(/\[|\]/g, '')
      return cleanUrl
    },
    closeViewer() {
      this.showViewer = false
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        sample_id: undefined,
        initial_name: '',
        labels: [],
        remark: ''
      }
      this.showHandleUpdateLabel = false
      this.resetForm('form')
    },
    /** 是否展示其他信息 */
    showOtherInfosClick() {
      this.showOtherInfos = !this.showOtherInfos
    },
    /** 搜索按钮操作 */
    handleQuery() {
      // this.dowdate = this.queryParams.starttime
      this.queryParams.pageIndex = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.dataset_id = undefined
      this.queryParams.reviewer = undefined
      this.queryParams.pageIndex = 1
      this.queryParams.pageSize = 10
      // 清空数据
      this.samplesList = []
      this.loading = false
      // this.resetForm('queryForm')
      // this.handleQuery()
    }
  }
}
</script>

<style>
.json-viewer-container {
    text-align: left;
}
</style>
