const zlib = require('zlib')

export function decode_and_uncompress(data) {
  const decodedData = Buffer.from(data, 'base64')
  const uncompressedData = zlib.unzipSync(decodedData).toString('utf-8')
  try {
    return JSON.stringify(JSON.parse(uncompressedData), null, 2)
  } catch (e) {
    console.error(e)
    return uncompressedData
  }
}

export function format_current_data_YYYYMMDD() {
  var date = new Date()
  var year = (date.getFullYear()).toString()
  var month = (date.getMonth() + 1).toString()
  var strDate = (date.getDate()).toString()
  if (month >= 1 && month <= 9) {
    month = '0' + month
  }
  if (strDate >= 0 && strDate <= 9) {
    strDate = '0' + strDate
  }
  var currentdate = year + month + strDate
  console.log('当前的日期 ', currentdate)
  return currentdate
}
