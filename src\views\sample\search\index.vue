
<template>
  <BasicLayout>
    <template #wrapper>
      <el-card class="box-card">
        <el-form ref="queryForm" :model="queryParams" :inline="true" label-width="110px">
          <!-- <el-form-item label="日期">
            <el-date-picker
              v-model="queryParams.starttime"
              size="small"
              type="date"
              align="right"
              value-format="yyyy-MM-dd"
            />
          </el-form-item> -->
          <el-form-item label="sampleId">
            <el-input
              v-model="queryParams.sample_id"
              placeholder="请输入sampleId"
              clearable
              size="small"
              style="width: 150px"
            />
          </el-form-item>
          <el-form-item label="文件名">
            <el-input
              v-model="queryParams.initial_name"
              placeholder="请输入initial_name"
              clearable
              size="small"
              style="width: 180px"
            />
          </el-form-item>
          <el-form-item v-if="showLabelList" label="标签类别"><el-select
            v-model="queryParams.label_name"
            placeholder="选择标签类别"
            clearable
            size="small"
            multiple
            filterable
          >
            <el-option
              v-for="dict in labelNameOptions"
              :key="dict.value"
              :label="dict.LabelName"
              :value="dict.LabelName"
            />
          </el-select>
          </el-form-item>
          <!-- <el-form-item label="文件类型" prop="file_type"><el-input
            v-model="queryParams.file_type"
            placeholder="文件类型"
            clearable
            size="small"
          />
          </el-form-item> -->
          <el-form-item label="文件类型"><el-select
            v-model="queryParams.file_type"
            placeholder="选择文件类型"
            clearable
            size="small"
            multiple
          >
            <el-option
              v-for="dict in fileTypeOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
            >搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
          <el-form-item label="显示object_key">
            <el-checkbox
              @change="showOtherInfosClick()"
            />
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="success"
              icon="el-icon-edit"
              size="mini"
              :disabled="single"
              @click="handleUpdate"
            >修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="warning"
              icon="el-icon-edit"
              size="mini"
              :disabled="multiple"
              @click="handleUpdateLabel"
            >批量修改样张标签</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              icon="el-icon-delete"
              size="mini"
              :disabled="isEmpty"
              @click="handleDelete"
            >删除</el-button>
          </el-col>
        </el-row>
        <el-table v-loading="loading" :data="samplesList" span="11.1" :cell-style="{padding:'5'}" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column v-if="false" type="selection" width="5" align="left" /><el-table-column
            label="sampleId"
            align="center"
            prop="sample_id"
            width="100"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="文件名"
            align="center"
            prop="initial_name"
            width="250"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="文件类型"
            align="center"
            prop="file_type"
            width="140"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="标签类别"
            align="center"
            prop="labelNames"
            width="150"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <div v-for="(label, index) in scope.row.labelNames" :key="index">
                <span>{{ label }}</span>
                <span v-if="index !== scope.row.labelNames.length - 1">, </span>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            label="文件大小(KB)"
            align="center"
            prop="file_size"
            width="100"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="图片高度(px)"
            align="left"
            prop="image_height"
            width="100"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="图片宽度(px)"
            align="center"
            prop="image_width"
            width="100"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            v-if="showOtherInfos"
            label="object_key"
            align="center"
            prop="url"
            width="350"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="remark"
            align="center"
            prop="remark"
            width="250"
            :show-overflow-tooltip="true"
          />

          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <div>
                <el-button
                  size="mini"
                  type="text"
                  @click="copyLink(scope.row.url)"
                >复制链接 |</el-button>
                <el-button
                  size="mini"
                  type="text"
                  :disabled="!acceptFileType.includes(scope.row.file_type)"
                  @click="onPreview(scope.row.file_type,scope.row.url)"
                >在线预览</el-button>
              </div>
              <el-image-viewer
                v-if="showViewer"
                style="width: 98%; height: 98%"
                :on-close="closeViewer"
                :url-list="viewPhotoList"
              />
            </template>
          </el-table-column>
        </el-table>

        <!-- 修改样张信息对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="500px">
          <el-form ref="form" :model="form" :rules="rules" label-width="80px">
            <el-form-item v-show="!showHandleUpdateLabel" label="文件名" prop="label">
              <el-input v-model="form.initial_name" type="text" placeholder="请输入内容" />
            </el-form-item>
            <el-form-item label="标签类别:" prop="label">
              <el-select v-model="form.labels" placeholder="请选择" multiple @change="$forceUpdate()">
                <el-option
                  v-for="dict in labelNameOptions"
                  :key="dict.value"
                  :label="dict.LabelName"
                  :value="dict.LabelId"
                />
              </el-select>
            </el-form-item>
            <el-form-item v-show="!showHandleUpdateLabel" label="备注" prop="remark">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
          </div>
        </el-dialog>

        <pagination
          v-show="total>0"
          :total="total"
          :page-sizes="[10, 20, 30, 50, 100]"
          :page.sync="queryParams.pageIndex"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>
    </template>
  </BasicLayout>
</template>

<script>
import { listSamples, getSampleUrl, SearchSamples, updateSample, deleteSample } from '@/api/sample/sample'
import { listLabels } from '@/api/sample/label'
import elImageViewer from 'element-ui/packages/image/src/image-viewer.vue'
export default {
  name: 'SampleSearch',
  components: {
    elImageViewer
  },
  props: {
    acceptFileType: {
      type: Array,
      default: function() {
        // 支持预览图片和pdf文件
        return ['image/png', 'image/jpg', 'image/jpeg', 'application/pdf', 'image/webp']
      }
    }
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 非空禁用
      isEmpty: true,
      showHandleUpdateLabel: false,
      // 总条数
      total: 0,
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      isEdit: false,
      showErrMsgs: false,
      // 类型数据字典
      samplesList: [],
      statusOptions: [],
      labelNameOptions: [],
      fileTypeOptions: undefined,
      // 是否只返回object_key
      isOnlyObjectKeyOptions: [
        { label: '是', value: false },
        { label: '否', value: true }
      ],
      // 是否显示标签列表
      showLabelList: false,
      // 标签列表
      labelList: [],
      // 关系表类型
      dowdate: undefined,
      imgUrls: [],
      // 查询参数
      queryParams: {
        pageIndex: 1,
        pageSize: 10,
        only_objectKey: true,
        // file_size: 0,
        // image_width: 0,
        // image_height: 0,
        sample_id: undefined,
        initial_name: undefined,
        label_name: undefined,
        file_type: undefined
      },
      // 是否展示其他信息
      showOtherInfos: false,
      // 表单参数
      form: {
        initial_name: '',
        labels: [],
        remark: ''
      },
      // 表单校验
      rules: { docid: [{ required: true, message: 'id不能为空', trigger: 'blur' }],
        starttime: [{ required: true, message: '(:s)不能为空', trigger: 'blur' }],
        status: [{ required: true, message: ', 0:, 1:, 2:不能为空', trigger: 'blur' }]
      },
      // 文件预览相关
      showViewer: false,
      viewPhotoList: []
    }
  },
  created() {
    this.getLabelList()
    this.getList()
    this.getDicts('sample_filetype').then(response => {
      this.fileTypeOptions = response.data
    })
    // this.getDicts('pdf_exporttype').then(response => {
    //   this.exporttypeOptions = response.data
    // })
  },
  methods: {
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.sample_id)
      this.single = selection.length !== 1
      this.multiple = this.getMultiple(selection.length)
      this.isEmpty = selection.length === 0
    },
    getMultiple(length) {
      return length <= 1
    },
    /** 查询参数列表 */
    getList() {
      this.loading = true
      // listRequests接口传入两个参数：pageIndex 和 pageSize
      // listSamples(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
      const form = new FormData()
      if (this.queryParams.file_type !== undefined && this.queryParams.file_type.length !== 0) {
        for (const file_type of this.queryParams.file_type) {
          if (file_type === 'image') {
            for (const image_type of this.fileTypeOptions) {
              if (image_type.value.startsWith('image/')) {
                form.append('file_type', image_type.value)
              }
            }
          } else {
            form.append('file_type', file_type)
          }
        }
      }
      form.append('only_objectKey', this.queryParams.only_objectKey)
      if (this.queryParams.sample_id !== undefined) {
        form.append('sample_id', this.queryParams.sample_id)
      }
      if (this.queryParams.initial_name !== undefined) {
        form.append('initial_name', this.queryParams.initial_name)
      }
      if (this.queryParams.label_name !== undefined) {
        for (const label_name of this.queryParams.label_name) {
          form.append('label_name', label_name)
        }
      }
      form.append('pageIndex', this.queryParams.pageIndex)
      form.append('pageSize', this.queryParams.pageSize)
      listSamples(form).then(response => {
        this.samplesList = response.data.result_data
        this.total = response.data.result_count
        this.loading = false
      }
      )
    },
    /** 查询标签列表 */
    getLabelList() {
      listLabels().then(response => {
        this.labelNameOptions = response.data.list.labels
        this.showLabelList = true
      }
      )
    },
    /** 复制链接 */
    async copyLink(url) {
      var urlLink
      // 判断是否返回url
      if (this.queryParams.only_objectKey === true) {
        // 获取url
        const form = new FormData()
        form.append('object_key', url)
        // 先默认给一天有效期
        form.append('timeout', 86400)
        await getSampleUrl(form).then(response => {
          urlLink = this.format(response.data.url)
          // console.log('urlLink:', urlLink)
        })
      } else {
        urlLink = url
      }
      var inputNode = document.getElementById('pdfcv_input')
      if (inputNode != null) {
        inputNode.remove()
      }
      inputNode = document.createElement('input')
      inputNode.value = urlLink
      inputNode.id = 'pdfcv_input'
      document.body.appendChild(inputNode)
      inputNode.select()
      document.execCommand('Copy')
      inputNode.className = 'oInput'
      inputNode.style.display = 'none'
      this.$message.success('复制成功')
    },
    /* 在线预览 */
    onPreview(fileType, url) {
      // 清空列表
      this.viewPhotoList = []

      // 判断是否返回url
      if (this.queryParams.only_objectKey === true) {
        // 获取url
        const form = new FormData()
        form.append('object_key', url)
        // 先默认给一天有效期
        form.append('timeout', 86400)
        getSampleUrl(form).then(response => {
          var imgurl = this.format(response.data.url)
          // 判断pdf或是图片
          if (fileType.includes('application/pdf')) {
            window.open(imgurl.replaceAll('"', '').replace('-internal', ''))
          } else {
            this.viewPhotoList.push(imgurl)
            this.showViewer = true
          }
        })
      } else {
        var imgurl = this.format(url)
        // 判断pdf或是图片
        if (fileType.includes('application/pdf')) {
          window.open(imgurl.replaceAll('"', '').replace('-internal', ''))
        } else {
          this.viewPhotoList.push(imgurl)
          this.showViewer = true
        }
      }
      // this.showViewer = true
    },
    // /* 查看源pdf/目标pdf等 */
    // formatUrl(url) {
    //   var starttime = this.dowdate !== '' ? ('/' + this.dowdate) : ''
    //   getCopyLink(this.queryParams, filetype, this.docid, starttime)
    //     .then(response => {
    //       var imgurl = response.data
    //       var s = imgurl === undefined ? '' : imgurl
    //       window.open(s.replaceAll('"', '').replace('-internal', ''))
    //     })
    //     .catch(error => {
    //       console.log('Error:', error)
    //     })
    // },
    // 格式化
    format(imgsUrl) {
      const externalUrl = imgsUrl.replace(/-internal/g, '')
      const cleanUrl = externalUrl.replace(/\[|\]/g, '')
      return cleanUrl
    },
    closeViewer() {
      this.showViewer = false
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        sample_id: undefined,
        initial_name: '',
        labels: [],
        remark: ''
      }
      this.showHandleUpdateLabel = false
      this.resetForm('form')
    },
    /** 是否展示其他信息 */
    showOtherInfosClick() {
      this.showOtherInfos = !this.showOtherInfos
    },
    /** 搜索按钮操作 */
    handleQuery() {
      // this.dowdate = this.queryParams.starttime
      this.queryParams.pageIndex = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.only_objectKey = true
      this.queryParams.label_name = undefined
      this.queryParams.sample_id = undefined
      this.queryParams.initial_name = undefined
      // this.dowdate = undefined
      this.queryParams.file_size = undefined
      this.queryParams.image_height = undefined
      this.queryParams.file_type = undefined
      this.queryParams.image_width = undefined
      // this.dateRange = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 批量修改样张标签 */
    handleUpdateLabel(row) {
      this.reset()
      this.showHandleUpdateLabel = true
      // 获取标签列表
      this.getLabelList()
      this.open = true
      this.title = '批量修改标签'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const form = new FormData()
      form.append('sample_id', this.ids)
      // 获取标签列表
      this.getLabelList()
      SearchSamples(form).then(response => {
        this.form.sample_id = response.data.result_data[0].sample_id
        this.form.initial_name = response.data.result_data[0].initial_name
        this.form.remark = response.data.result_data[0].remark
        if (response.data.result_data[0].labelNames !== null && response.data.result_data[0].labelNames !== undefined) {
          this.form.labels = response.data.result_data[0].labelNames.map(labelName => {
            const option = this.labelNameOptions.find(option => option.LabelName === labelName)
            return option ? option.LabelId : null
          })
        }
        this.open = true
        this.title = '修改样张信息'
        // this.isEdit = true
      })
    },
    /** 提交按钮 */
    submitForm: function() {
      // this.$refs['form'].validate(valid => {
      //   if (valid) {
      const newForm = new FormData()
      for (const sample_id of this.ids) {
        newForm.append('sample_id', sample_id)
      }
      for (const label of this.form.labels) {
        newForm.append('label_id', label)
      }
      newForm.append('remark', this.form.remark)
      newForm.append('initial_name', this.form.initial_name)
      updateSample(newForm).then(response => {
        if (response.code === 200) {
          this.msgSuccess(response.msg)
          this.open = false
          this.getList()
        } else {
          this.msgError(response.msg)
        }
      })
      //   }
      // })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$confirm('是否确认删除编号为"' + this.ids + '"的样张信息?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const newForm = new FormData()
        for (const sample_id of this.ids) {
          newForm.append('sample_id', sample_id)
        }
        return deleteSample(newForm)
      }).then((response) => {
        if (response.code === 200) {
          this.msgSuccess(response.msg)
          this.open = false
          this.getList()
        } else {
          this.msgError(response.msg)
        }
      }).catch(function() {
      })
    }
  }
}
</script>
