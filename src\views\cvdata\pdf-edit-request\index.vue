
<template>
  <BasicLayout>
    <template #wrapper>
      <el-card class="box-card">
        <el-form ref="queryForm" :model="queryParams" :inline="true" label-width="88px">
          <el-form-item label="docid">
            <el-input
              v-model="queryParams.docid"
              placeholder="请输入docid"
              clearable
              size="small"
              style="width: 300px"
            />
          </el-form-item>
          <el-form-item label="origin" prop="origin"><el-input
            v-model="queryParams.origin"
            placeholder="请输入origin"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="thirdclienttype" prop="thirdclienttype"><el-input
            v-model="queryParams.thirdclienttype"
            placeholder="请输入thirdclienttype"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="日期">
            <el-date-picker
              v-model="queryParams.starttime"
              size="small"
              type="date"

              align="right"
              value-format="yyyy-MM-dd"
            /> </el-form-item>
          <el-form-item label="状态" prop="status"><el-select
            v-model="queryParams.status"
            placeholder="PdfEditRequest状态"
            clearable
            size="small"
          >
            <el-option
              v-for="dict in statusOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
          </el-form-item>
          <el-form-item label="model"><el-select
            v-model="queryParams.models"
            placeholder="model"
            clearable
            size="small"
          >
            <el-option
              v-for="dict in modelOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
          </el-form-item>
          <el-form-item label="非文字">
            <el-checkbox
              @change="errmsgClick()"
            />
          </el-form-item>
          <el-form-item label="错误详情">
            <el-checkbox
              @change="errShowMsgClick()"
            />
          </el-form-item>
          <el-form-item label="错误"><el-select
            v-model="queryParams.errorstatus"
            placeholder="错误个数"
            clearable
            size="small"
          >
            <el-option
              v-for="dict in errstatusOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
          </el-form-item>
          <el-form-item label="errmsg" prop="errmsg"><el-input
            v-model="queryParams.errmsg"
            placeholder="请输入异常信息"
            clearable
            size="small"
          />
          </el-form-item>
          <el-form-item>
            <div style="display: flex; align-items: center;">
              <el-button v-permisaction="['cvdata:pdfEditRequest:select']" type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
              <el-button v-permisaction="['cvdata:pdfEditRequest:refresh']" icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              <el-button v-permisaction="['cvdata:pdfEditRequest:err']" icon="el-icon-bottom" size="mini" style="margin-right: 10px;" @click="errurlsExport">查询</el-button>
              <!-- <el-button v-permisaction="['cvdata:pdfEditRequest:downbath']" icon="el-icon-bottom" size="mini" @click="errurlsExport">批量下载</el-button> -->
              <CVdataExport
                :query-params="{ ...queryParams, pageIndex: queryParams.pageIndex, pageSize: queryParams.pageSize }"
                :approver-list="approver_list"
                :approver-function="approver_function"
              />
            </div>
          </el-form-item>
          <!-- <el-form-item label="最新文件">
            <el-checkbox
              v-model="islatest"
            />
          </el-form-item> -->
        </el-form>

        <el-table v-loading="loading" :data="pdfEditRequestList" span="1.36" @selection-change="handleSelectionChange">
          <el-table-column v-if="false" type="selection" width="5" align="center" /><el-table-column
            label="docid"
            align="center"
            prop="docid"
            width="320"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="origin"
            align="center"
            prop="origin"
            width="90"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="third_client_type"
            align="center"
            prop="thirdclienttype"
            width="130"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="日期"
            align="center"
            prop="starttime"
            width="180"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="状态"
            align="center"
            prop="status"
            :formatter="statusFormat"
            width="60"
          >
            <template slot-scope="scope">
              {{ statusFormat(scope.row) }}
            </template>
          </el-table-column><el-table-column
            label="耗时(s)"
            align="center"
            prop="duration"
            width="120"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="页数"
            align="center"
            prop="pagecount"
            width="80"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="Model"
            align="center"
            prop="models"
            width="80"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="错误个数"
            align="center"
            prop="errcount"
            width="80"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            v-if="showErrMsgs"
            label="错误详情"
            align="center"
            prop="pageserr"
            width="500"
            :show-overflow-tooltip="true"
          />
          <!-- :query-params="{ ...queryParams, pageIndex: queryParams.pageIndex, pageSize: queryParams.pageSize }" -->
          <el-table-column label="详情" align="center">
            <template slot-scope="scope">
              <CVdataParameter
                :key="scope.row.docid"
                :docid="scope.row.docid"
                :starttime="queryParams.starttime"
                function="pdfEdit"
              />
            </template>
          </el-table-column>
          <!-- <el-table-column label="url" align="center" prop="imgurl"
                                                 :show-overflow-tooltip="true"/>  -->
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <CVdataOperation
                :approver-list="approver_list"
                :show-input-pdf="scope.row.pdfurl=='true'"
                :show-input-zip="scope.row.zipurl=='true'"
                cvmodel="pdf-edit-request"
                :docid="scope.row.docid"
                :dowdate="dowdate"
                :input-pdf-url="scope.row.pdfurl"
                :input-zip-url="scope.row.zipurl"
                :show-xml="scope.row.xmlurl=='true'&&(scope.row.models=='pdfEdit'||scope.row.models=='picEdit')"
                :show-json="scope.row.xmlurl=='true'&&scope.row.models=='pdf2json'"
                :xml-url="scope.row.xmlurl"
                :show-img="scope.row.imgurl=='true'"
                :show-imgs="scope.row.imgurls=='true'"
              />
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageIndex"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>

    </template>
  </BasicLayout>
</template>

<script>
import { addPdfEditRequest, delPdfEditRequest, getPdfEditRequest, listPdfEditRequest, updatePdfEditRequest, errExportPdfEditRequest } from '@/api/cvdata/pdf-edit-request'

import CVdataOperation from '@/components/Cdata/Operation.vue'
import CVdataExport from '@/components/Cdata/Export.vue'
import CVdataParameter from '@/components/Cdata/Parameter.vue'

export default {
  name: 'PdfEditRequest',
  components: {
    CVdataOperation, CVdataExport, CVdataParameter
  },
  data() {
    return {
      // 下载参数日期
      dowdate: undefined,
      // 下载参数状态
      dowstatus: undefined,
      // 下载docid
      dowdocid: undefined,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      showErrMsgs: false,
      // 总条数
      total: 0,
      islatest: false,
      // 弹出层标题
      title: '',
      // 是否显示弹出层

      // 图片电子
      imgurls: [],

      open: false,
      isEdit: false,
      // 类型数据字典
      typeOptions: [],
      pdfEditRequestList: [],
      statusOptions: [],
      errstatusOptions: [],
      modelOptions: [],
      // 关系表类型
      showPageserr: false,
      // 查询参数
      queryParams: {
        pageIndex: 1,
        pageSize: 10,
        cvmodel: 'pdf-edit-request',
        origin: undefined,
        errorstatus: undefined,
        starttime: undefined,
        status: 'noFinished',
        docid: undefined,
        pageserr: undefined,
        errmsg: undefined,
        models: undefined,
        thirdclienttype: undefined,
        isExport: false
      },
      showViewer: false,
      // viewPhotoList:['http://zhai-datas.ks3-cn-beijing.ksyun.com/chenzhiwei/png/test05_01.png','http://zhai-datas.ks3-cn-beijing.ksyun.com/chenzhiwei/png/test04_01.png'],
      viewPhotoList: [],
      approver_list: [],
      approver_function: 'pdfEdit',
      // 表单参数
      form: {
      },
      // 表单校验
      rules: { starttime: [{ required: true, message: '日期不能为空', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.getList()
    this.getDicts('pdf_edit_status').then(response => {
      this.statusOptions = response.data
    })
    this.getDicts('pdf_edit_errstatus').then(response => {
      this.errstatusOptions = response.data
    })
    this.getDicts('pdf_edit_model').then(response => {
      this.modelOptions = response.data
    })
    this.getDicts('approver_cvdata_list').then((response) => {
      this.approver_list = response.data
    })
  },
  methods: {
    /** 查询参数列表 */
    getList() {
      this.loading = true
      listPdfEditRequest(this.addDateRange(this.queryParams)).then(response => {
        console.log(response)
        this.pdfEditRequestList = response.data.list
        this.total = response.data.count
        this.loading = false
      }
      )
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {

        id: undefined
      }
      this.resetForm('form')
    },
    getImgList: function() {
      this.form[this.fileIndex] = this.$refs['fileChoose'].resultList[0].fullUrl
    },
    fileClose: function() {
      this.fileOpen = false
    },
    statusFormat(row) {
      return this.selectDictLabel(this.statusOptions, row.status)
    },
    errmsgClick() {
      this.showPageserr = !this.showPageserr
      if (this.showPageserr) {
        this.queryParams.pageserr = '1'
      } else {
        this.queryParams.pageserr = ''
      }
    },
    errShowMsgClick() {
      this.showErrMsgs = !this.showErrMsgs
    },
    replaceUrl(row) {
      var s = row.imgurl
      return s.replace('"', '').replace('-internal', '')
    },
    replaceUrl2(imgurl) {
      var s = imgurl
      return s.replace('"', '').replace('-internal', '')
    },

    showphotos(imgurl) {

    },
    closeViewer() {
      this.showViewer = false
    },
    fileDownload3(row) {
      return process.env.VUE_APP_BASE_API + '/api/v1/pdf-edit-request/download/' + row.docid + '/' + this.dowdate + '/' + this.dowstatus + '/' + this.dowdocid
    },
    fileDownload(row, filetype) {
      return process.env.VUE_APP_BASE_API + '/api/v1/pdf-edit-request/download/' + filetype + '/' + row.docid + '/' + this.dowdate
    },
    // 关系
    // 文件
    /** 搜索按钮操作 */
    handleQuery() {
      this.dowdate = this.queryParams.starttime
      this.dowstatus = this.queryParams.status
      this.dowdocid = this.queryParams.docid
      this.pageserr = this.queryParams.pageserr
      this.queryParams.pageIndex = 1
      this.getList()
    },
    downloaBath() {
      this.dowdate = this.queryParams.starttime
      this.dowstatus = this.queryParams.status
      this.dowdocid = this.queryParams.docid
      this.pageserr = this.queryParams.pageserr
      this.queryParams.pageIndex = 1
      this.getList()
    },
    /* 导出错误图片url*/
    errurlsExport() {
      errExportPdfEditRequest().then(res => {
        if (res.code === 200) {
          this.msgSuccess(res.msg)
        } else {
          this.msgError(res.msg)
        }
      })
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.dowstatus = undefined
      this.dowdate = undefined
      this.dowdocid = undefined
      this.queryParams.errorstatus = undefined
      this.queryParams.starttime = undefined
      this.queryParams.pageserr = undefined
      this.queryParams.errmsg = undefined
      this.queryParams.models = undefined
      this.queryParams.thirdclienttype = undefined
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加PdfEditRequest'
      this.isEdit = false
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.docid)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const docid =
                row.docid || this.ids
      getPdfEditRequest(docid).then(response => {
        this.form = response.data
        this.open = true
        this.title = '修改PdfEditRequest'
        this.isEdit = true
      })
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.docid !== undefined) {
            updatePdfEditRequest(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(response.msg)
                this.open = false
                this.getList()
              } else {
                this.msgError(response.msg)
              }
            })
          } else {
            addPdfEditRequest(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(response.msg)
                this.open = false
                this.getList()
              } else {
                this.msgError(response.msg)
              }
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      var Ids = (row.docid && [row.docid]) || this.ids

      this.$confirm('是否确认删除编号为"' + Ids + '"的数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function() {
        return delPdfEditRequest({ 'ids': Ids })
      }).then((response) => {
        if (response.code === 200) {
          this.msgSuccess(response.msg)
          this.open = false
          this.getList()
        } else {
          this.msgError(response.msg)
        }
      }).catch(function() {
      })
    }
  }
}
</script>
