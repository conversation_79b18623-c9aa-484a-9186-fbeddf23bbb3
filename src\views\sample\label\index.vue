
<template>
  <BasicLayout>
    <template #wrapper>
      <el-card class="box-card">
        <el-form ref="queryForm" :model="queryParams" :inline="true" label-width="68px">
          <!-- <el-form-item label="日期">
            <el-date-picker
              v-model="queryParams.starttime"
              size="small"
              type="date"
              align="right"
              value-format="yyyy-MM-dd"
            />
          </el-form-item> -->
          <!-- <el-form-item v-if="showLabelList" label="标签名"><el-select
            v-model="queryParams.label_name"
            placeholder="输入标签名"
            clearable
            size="small"
          >
            <el-option
              v-for="dict in labelNameOptions"
              :key="dict.value"
              :label="dict.LabelName"
              :value="dict.LabelName"
            />
          </el-select>
          </el-form-item> -->
          <el-form-item label="标签名" prop="labelName"><el-input
            v-model="queryParams.label_name"
            placeholder="输入标签名"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
            >搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
            >新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="success"
              icon="el-icon-edit"
              size="mini"
              :disabled="single"
              @click="handleUpdate"
            >修改</el-button>
          </el-col>
          <!-- 接口暂时只支持单个删除，支持批量删除后，改为multiple即可 -->
          <el-col :span="1.5">
            <el-button
              type="danger"
              icon="el-icon-delete"
              size="mini"
              :disabled="single"
              @click="handleDelete"
            >删除</el-button>
          </el-col>
        </el-row>
        <el-table v-loading="loading" :data="labelList" span="11.1" :cell-style="{padding:'5'}" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="1" />
          <el-table-column v-if="false" type="selection" width="5" align="left" /><el-table-column
            label="标签Id"
            align="center"
            prop="LabelId"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="标签名"
            align="center"
            prop="LabelName"
            :show-overflow-tooltip="true"
          />
        </el-table>

        <!-- 修改样张信息对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="500px">
          <el-form ref="form" :model="form" label-width="80px">
            <el-form-item label="标签名" prop="label_name">
              <el-input v-model="form.label_name" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
          </div>
        </el-dialog>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageIndex"
          :limit.sync="queryParams.pageSize"
          @pagination="getLabelList"
        />
      </el-card>
    </template>
  </BasicLayout>
</template>

<script>
import { listLabels, updateLabel, deleteLabel, insertLabel } from '@/api/sample/label'
export default {
  name: 'LabelManagement',
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中labelId数组
      ids: [],
      // 选中labelName数组
      names: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 是否新增标签
      isAdd: false,
      // 是否显示标签列表
      showLabelList: false,
      // 标签列表
      labelList: [],
      // 关系表类型
      dowdate: undefined,
      imgUrls: [],
      // 查询参数
      queryParams: {
        pageIndex: 1,
        pageSize: 10,
        label_name: ''
      },
      // 表单参数
      form: {
        label_name: ''
      }
    }
  },
  created() {
    this.getLabelList()
  },
  methods: {
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.LabelId)
      this.names = selection.map(item => item.LabelName)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 查询标签列表 */
    getLabelList() {
      listLabels(this.queryParams).then(response => {
        // this.labelNameOptions = response.data.labels
        this.labelList = response.data.list.labels
        this.total = response.data.count
        this.queryParams.pageIndex = response.data.pageIndex
        this.queryParams.pageSize = response.data.pageSize
        this.showLabelList = true
        this.loading = false
      }
      )
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        label_name: ''
      }
      this.isAdd = false
      this.resetForm('form')
    },
    // 关系
    // 文件
    /** 搜索按钮操作 */
    handleQuery() {
      // this.queryParams.pageIndex = 0
      // this.queryParams.pageSize = 0
      this.getLabelList(this.queryParams)
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.label_name = ''
      // this.dowdate = undefined
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.isAdd = true
      this.title = '新增标签'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      this.form.label_name = this.names[0]
      this.open = true
      this.title = '修改标签信息'
    },
    /** 提交按钮 */
    submitForm: function() {
      if (this.isAdd === true) {
        const newForm = new FormData()
        newForm.append('label_name', this.form.label_name)
        insertLabel(newForm).then(response => {
          if (response.code === 200) {
            this.msgSuccess(response.msg)
            this.open = false
            this.getLabelList()
          } else {
            this.msgError(response.msg)
          }
        })
      } else {
        const newForm = new FormData()
        newForm.append('label_id', this.ids)
        newForm.append('label_name', this.form.label_name)
        updateLabel(newForm).then(response => {
          if (response.code === 200) {
            this.msgSuccess(response.msg)
            this.open = false
            this.getLabelList()
          } else {
            this.msgError(response.msg)
          }
        })
      }
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$confirm('是否确认删除编号为"' + this.ids + '"的标签信息?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const newForm = new FormData()
        for (const label_id of this.ids) {
          newForm.append('label_id', label_id)
        }
        return deleteLabel(newForm)
      }).then((response) => {
        if (response.code === 200) {
          this.msgSuccess(response.msg)
          this.open = false
          this.getLabelList()
        } else {
          this.msgError(response.msg)
        }
      }).catch(function() {
      })
    }
  }
}
</script>
