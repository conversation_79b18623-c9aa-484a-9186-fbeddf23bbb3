<template>
  <!-- 创建一个卡片式的容器，使用主题内容区域将背景色统一 -->
  <el-card>
    <!-- 标题插槽：显示使用说明 -->
    <div slot="header">
      <CVHint>使用说明：（分割万物）选择一张图片上传，分割结果会在右侧显示</CVHint>
    </div>

    <!-- 主体内容 -->
    <div>
      <div class="result-row">
        <el-row>
          <el-col v-if="containerHeight && containerWidth" :md="12" :style="{height: containerHeight, width: containerWidth}">
            <span class="image-label">Original Image:</span>
            <vue-cropper
              ref="cropper"
              :img="options.img"
              :info="true"
              :can-scale="false"
              :view-mode-options="{ aspectRatio: containerWidth / containerHeight }"
              :auto-crop="options.autoCrop"
              :auto-crop-width="containerWidth"
              :auto-crop-height="containerHeight"
              :fixed-box="options.fixedBox"
              :can-move="false"
            />

          </el-col>
          <el-col :md="12" :style="{height: containerHeight, width: containerWidth}">
            <span class="image-label" style="margin-right: 20px;">Segmentation Result:</span>
            <el-image v-if="masks_url" :src="masks_url" />
            <span v-else>正在加载...</span>
          </el-col>
        </el-row>
      </div>
      <br>

      <!-- action="placeholder" -->
      <br>
      <div class="result-row">
        <el-upload action="#" :http-request="submitUpload" :show-file-list="false" :before-upload="beforeUpload">
          <el-button type="primary" size="small" style="width: 100px;">
            选择图片
            <i class="el-icon-upload el-icon--right" />
          </el-button>
        </el-upload>
        <el-row v-if="showResult">
          <el-col :span="12">
            <el-button type="warning" size="small" @click="copyLink('origin')">复制原图链接</el-button>
          </el-col>
          <el-col :span="12">
            <el-button type="success" size="small" @click="copyLink('result')">复制结果链接</el-button>
          </el-col>
        </el-row>
      </div>
      <br>

      <br>
      <div class="result-row">
        <CVThumbnail :images="base64ImageData" :show-thumbnail="showThumbnail" />
      </div>
      <br>

    </div>
    <br>

    <!-- 底部插槽：未使用，后续若是扩展可以考虑翻页等功能 -->
    <div slot="footer">
      footer
    </div>
  </el-card>
</template>

<script>
import CVHint from './components/CVHint'
import { VueCropper } from 'vue-cropper'
import axios from 'axios'
import { getHeaders } from '@/utils/get-header'

import CVThumbnail from './components/CVThumbnail'

export default {
  components: { CVHint, VueCropper, CVThumbnail },
  props: {
    // eslint-disable-next-line vue/require-default-prop
    user: { type: Object }
  },
  data() {
    return {
      // 是否显示弹出层
      open: true,
      options: {
        img: 'http://zhai-datas.ks3-cn-beijing.ksyuncs.com/layout/people/wangluoyan/segmentation/%E9%80%89%E6%8B%A9%E4%B8%80%E5%BC%A0%E5%9B%BE%E7%89%87.png',
        autoCrop: false, // 是否默认生成截图框
        fixedBox: false // 固定截图框大小 不允许改变
      },
      // 图片的缩放比例
      scale: 1,
      scaleFlag: false,
      // 图片展示宽度
      imageWidth: 600,
      url: 'http://zhai-datas.ks3-cn-beijing.ksyuncs.com/layout/people/wangluoyan/segmentation/%E9%80%89%E6%8B%A9%E4%B8%80%E5%BC%A0%E5%9B%BE%E7%89%87.png',
      containerHeight: '',
      containerWidth: '',
      uploadurl: {
        value: '//pcv-test.wps.cn',
        default: '//pcv-test.wps.cn'
      },
      segmentationurl: {
        value: '//pcv-test-scan.wps.cn',
        default: '//pcv-test-scan.wps.cn'
      },
      // 图片分割结果
      masks_url: 'http://zhai-datas.ks3-cn-beijing.ksyuncs.com/layout/people/wangluoyan/segmentation/%E9%80%89%E6%8B%A9%E4%B8%80%E5%BC%A0%E5%9B%BE%E7%89%87.png',
      // 显示结果
      showResult: false,
      // 存储图像的base64编码数据
      base64ImageData: [],
      // 显示缩略图
      showThumbnail: false
    }
  },
  mounted() {
    this.getImageSize()
  },
  methods: {
    copyLink(type) {
      var inputNode = document.getElementById('pdfcv_input')
      if (inputNode != null) {
        inputNode.remove()
      }
      inputNode = document.createElement('input')
      if (type === 'origin') {
        inputNode.value = this.url
      } else {
        inputNode.value = this.masks_url
      }
      // inputNode.value = this.masks_url
      inputNode.id = 'pdfcv_input'
      document.body.appendChild(inputNode)
      inputNode.select()
      document.execCommand('Copy')
      inputNode.className = 'oInput'
      inputNode.style.display = 'none'
      this.$message.success('复制成功')
    },
    getImageSize() {
      var image = new Image()
      // image.src = this.url
      image.src = this.options.img

      image.onload = () => {
        var imageWidth = image.width
        var imageHeight = image.height

        if (imageWidth > 660) {
          this.scaleFlag = true
          var originalAspectRatio = imageWidth / imageHeight
          // 四舍五入保留一位小数
          this.scale = parseFloat((650 / imageWidth).toFixed(1))
          imageWidth = imageWidth * this.scale
          imageHeight = imageWidth / originalAspectRatio
        }

        this.containerHeight = imageHeight + 'px'
        this.containerWidth = imageWidth + 'px'
      }
    },
    // 上传预处理
    beforeUpload(file) {
      // 复位
      this.resetResult()
      if (file.type.indexOf('image/') === -1) {
        this.msgError('文件格式错误，请上传图片类型,如：JPG，PNG后缀的文件。')
      } else {
        const reader = new FileReader()
        reader.readAsDataURL(file)
        reader.onload = () => {
          this.options.img = reader.result
          this.getImageSize()
        }
      }
    },
    // 重置
    resetResult() {
      this.showResult = false
      this.showThumbnail = false
      this.masks_url = ''
    },
    // 上传到服务器
    async submitUpload(param) {
      this.fileName = param.file.name
      const form = new FormData()
      form.append('file', param.file)
      form.append('origin', 'pdf-cv-demo')
      const axios_config_upload = {
        method: 'post',
        headers: { 'Cache-Control': 'no-cache', ...getHeaders() },
        url: this.uploadurl.value + '/layout/scheduler/samplecollect/upload',
        data: form,
        responseType: 'json',
        processData: false,
        contentType: false
      }
      console.log('等待上传...')
      // 上传
      await axios(axios_config_upload).then(res => {
        this.url = res.data.data.url
      })
      // 请求
      const s_time = new Date().getTime()
      const status = await this.segAnything()
      const e_time = new Date().getTime()
      // 状态和处理用时
      this.statistic = {
        status,
        duration: this.result.duration || (e_time - s_time) / 1000
      }
      return Promise.resolve()
    },
    // 获取分割结果
    segAnything() {
      // 对传入的url进行处理
      // let urlString = url.toString()
      const urlString = this.url.toString()
      // urlString = urlString.replace(/-internal/g, '')
      var data_dict = {}
      data_dict = {
        'image_url': urlString,
        'internal': true
      }
      return axios({
        method: 'post',
        headers: { 'Cache-Control': 'no-cache', 'Content-Type': 'application/json', ...getHeaders() },
        url: this.segmentationurl.value + '/matting/sam/seg-anything',
        data: data_dict,
        responseType: 'json',
        processData: false,
        contentType: false,
        timeout: 80000 // 设置超时时间为80秒
      }).then(res => {
        if (res.data.code !== 200 || res.data.data === undefined || res.data.data.masks_url === undefined || res.data.data.result === undefined) {
          return Promise.reject('分割图片失败!')
        }

        // 处理完成
        console.log('分割图片完成: ' + JSON.stringify(res.data.data.masks_url))
        this.masks_url = res.data.data.masks_url

        this.base64ImageData = res.data.data.result.map(item => {
          return {
            src: 'data:image/jpeg;base64,' + item.seg,
            isBase64: true
          }
        })

        // 显示结果
        this.showResult = true
        // 显示缩略图
        this.showThumbnail = true
      }).catch((error) => {
        this.$message({
          showClose: true,
          message: '分割图片失败! ' + error,
          center: true,
          duration: 4000,
          type: 'error'
        })
        return Promise.resolve(0)
      })
    },
    resetPosition() {
      // 重置原图位置
      this.ImgX = 0
      this.ImgY = 0
    }
  }
}
</script>
<style lang="scss" scoped>
@import "~@/styles/mixin.scss";

.vue-cropper-preview {
position: relative;
width: 180px;
height: 180px;
border-radius: 50%;
box-shadow: 0 0 4px #ccc;
overflow: hidden;
}

.result-row {
  display: flex;
  justify-content: center;
  align-items: center;
}

.image-label {
  margin-top: 10px;
  font-size: 16px;
  background-color: #333;
  color: #fff;
  padding: 5px 10px;
}

</style>
