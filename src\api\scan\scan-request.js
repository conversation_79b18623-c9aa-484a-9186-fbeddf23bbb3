import request from '@/utils/request'

// 查询ScanRequest列表
export function getScanRequestList(query) {
  return request({
    url: '/api/v1/scan-request',
    method: 'get',
    params: query
  })
}

// 获取链接
export function getScanCopyLink(scan_url, jobid) {
  return request({
    method: 'get',
    headers: { 'Cache-Control': 'no-cache' },
    url: '/api/v1/scan-request/copy/' + scan_url + '/' + jobid
  })
}
