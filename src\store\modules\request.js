const state = {
  httpRequestList: []
}

const mutations = {
  addHttpRequest({ httpRequestList }, payload) {
    if (payload === 0) {
      // 强行中断时才向下执行
      httpRequestList.forEach(element => {
        element('interrupt') // Promise.reject('')
      })
      httpRequestList = []
    } else {
      httpRequestList.push(payload)
    }
  }
}

const actions = {
  async removeHttpRequest({ commit }) {
    commit('addHttpRequest', 0)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
