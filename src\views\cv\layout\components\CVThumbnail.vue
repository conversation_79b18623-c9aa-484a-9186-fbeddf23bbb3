<template>
  <div v-if="showThumbnail">
    <span class="image-label" style="margin-right: 20px;">所有元素的缩略图如下：(点击打开大图)</span>
    <br>
    <div class="image-viewer">
      <div class="thumbnails">
        <div v-for="(image, index) in images" :key="index" class="thumbnail">
          <img :src="image.isBase64 ? image.src : require(`@/assets/${image.src}`)" alt="Thumbnail Image" @click="openModal(index)">
          <span>{{ index + 1 }}.jpg</span>
        </div>
      </div>

      <div v-if="showModal" class="modal">
        <div class="modal-content">
          <span class="close" @click="closeModal">&times;</span>
          <img :src="images[currentImage].src" :alt="images[currentImage].alt" :style="getImageStyle">
        </div>
      </div>

    </div>
  </div>
</template>

<script>
export default {
  props: {
    showThumbnail: {
      type: Boolean,
      require: true
    },
    images: {
      type: Array,
      required: true
    }
  },
  data() {
    return {
      showModal: false,
      currentImage: 0
    }
  },
  computed: {
    getImageStyle() {
      // 固定宽度值
      const width = '600px'
      const image = this.images[this.currentImage]
      const height = `${(image.height / image.width) * parseInt(width)}px`
      return { width, height }
    }
  },
  methods: {
    changeImage(index) {
      this.currentImage = index
    },
    openModal(index) {
      this.currentImage = index
      this.showModal = true
    },
    closeModal() {
      this.showModal = false
    }
  }
}
</script>

<style scoped>
.image-viewer {
  display: flex;
  flex-direction: column;
}

.thumbnails {
  display: flex;
  flex-wrap: wrap;
  margin-top: 10px;
}

.thumbnail {
  width: 100px;
  height: 100px;
  margin-right: 10px;
  /* 不同行之间的行距 */
  margin-bottom: 25px;
  /* 添加边框样式 */
  border: 1px solid #ccc;
  /* 居中显示 */
  text-align: center;
}

.thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
}

.modal {
  display: block;
  position: fixed;
  z-index: 9999;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
}

.modal-content {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

.close {
  position: absolute;
  top: 20px;
  right: 30px;
  color: #fff;
  font-size: 30px;
  font-weight: bold;
  cursor: pointer;
}

.image-label {
  margin-top: 10px;
  font-size: 16px;
  background-color: #333;
  color: #fff;
  padding: 5px 10px;
}

</style>
