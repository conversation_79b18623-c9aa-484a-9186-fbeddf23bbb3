
<template>
  <BasicLayout>
    <template #wrapper>
      <el-card class="box-card">
        <el-form
          ref="queryForm"
          :model="queryParams"
          :inline="true"
          label-width="68px"
          :rules="rules"
        >
          <el-form-item label="创建时间" prop="dateRange">
            <el-date-picker
              v-model="dateRange"
              size="small"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              align="right"
              value-format="yyyyMMdd"
            />
          </el-form-item>
          <el-form-item label="筛选类型" prop="category">
            <el-select v-model="category" placeholder="请选择类别" @change="handleTypeSelectionChange">
              <el-option label="服务" value="service" />
              <el-option label="路由" value="router" />
            </el-select>
          </el-form-item>
          <el-form-item v-if="category ==='service'" label="名称" prop="service"><el-select
            v-model="queryParams.service"
            placeholder="请选择名称"
            size="small"
          >
            <el-option
              v-for="dict in serviceTypeOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
          </el-form-item>
          <el-form-item v-if="category ==='router'" label="路由" prop="router">
            <!-- <el-input
              v-model="queryParams.router"
              placeholder="请输入路由名称"
              clearable
              size="small"
            /> -->
            <el-select
              v-model="queryParams.router"
              placeholder="请选择名称"
              size="small"
            >
              <el-option
                v-for="dict in routerTypeOptions"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button
              v-permisaction="['scan:requestsDayStatistics:select']"
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
            >搜索</el-button>
            <el-button
              v-permisaction="['scan:requestsDayStatistics:refresh']"
              icon="el-icon-refresh"
              size="mini"
              @click="resetQuery"
            >重置</el-button>
          </el-form-item>
        </el-form>
        <el-row>
          <el-col>
            <MyLineChart
              :x-axis-datas="xAxisDatas"
              :task-datas="taskDatas"
              :success-rate="successRate"
              :max-duration="maxDuration"
              :avg-duration="avgDuration"
            />
          </el-col>
        </el-row>
        <el-table
          v-loading="loading"
          :data="requestsDayStatisticsList"
        >
          <el-table-column
            v-if="false"
            type="selection"
            width="5"
            align="center"
          /><el-table-column
            label="日期"
            align="center"
            prop="day"
            sortable
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <a style="color: blue" @click="gotoScanRequest(scope.row)">{{ scope.row.day }}</a>
            </template>
          </el-table-column>
          <el-table-column
            label="任务总数"
            align="center"
            prop="request_total"
            sortable
            :show-overflow-tooltip="true"
          /><el-table-column
            label="成功任务数"
            align="center"
            prop="request_success"
            sortable
            :show-overflow-tooltip="true"
          /><el-table-column
            label="失败任务数"
            align="center"
            prop="failedCount"
            sortable
            :show-overflow-tooltip="true"
          /><el-table-column
            label="成功率"
            align="center"
            prop="successRate"
            sortable
            :show-overflow-tooltip="true"
          /><el-table-column
            label="失败率"
            align="center"
            prop="failedRate"
            sortable
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="平均耗时(s)"
            align="center"
            prop="requestTimeAvgS"
            sortable
            :show-overflow-tooltip="true"
          /><el-table-column
            label="最小耗时(s)"
            align="center"
            prop="requestTimeMinS"
            sortable
            :show-overflow-tooltip="true"
          /><el-table-column
            label="最大耗时(s)"
            align="center"
            prop="requestTimeMaxS"
            sortable
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="服务名称"
            align="center"
            prop="service"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            v-if="isShowRouter"
            label="路由名称"
            align="center"
            prop="router"
            :show-overflow-tooltip="true"
          />
          <el-table-column v-if="isShowDetail" label="操作" width="150" align="center" class-name="small-padding fixed-width">
            <template v-if="isShowDetail" slot-scope="scope">
              <ScanStatisticsOperation
                :day="scope.row.day"
                :service="scope.row.service"
              />
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageIndex"
          :limit.sync="queryParams.pageSize"
          @pagination="handlePagination"
        />
      </el-card>
    </template>
  </BasicLayout>
</template>

<script>
import { getStatisticsList } from '@/api/scan/statistics'
import ScanStatisticsOperation from '@/components/ScanStatisticsOperation/index.vue'
import MyLineChart from './linechart'

export default {
  name: 'ScanRequestsDayStatistics',
  components: { MyLineChart, ScanStatisticsOperation },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      isShowRouter: false,
      isShowDetail: false,
      // 总条数
      total: 0,
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      isEdit: false,
      category: 'service',
      // 类型数据字典
      typeOptions: [],
      requestsDayStatisticsList: [],
      xAxisDatas: [],
      taskDatas: [],
      serviceTypeOptions: [],
      routerTypeOptions: [],
      avgDuration: [],
      successRate: [],
      maxDuration: [],
      // 查询参数
      queryParams: {
        dataType: '',
        router: '',
        pageIndex: 1,
        pageSize: 10,
        service: '',
        startTime: '',
        endTime: '',
        date: undefined
      },
      // 表单参数
      form: {},
      dateRange: [],
      // 表单校验
      rules: {
        router: [{ required: true, message: '请输入内容', trigger: 'blur' }],
        service: [{ required: true, message: '请输入内容', trigger: 'blur' }]
      },
      allData: undefined,
      start: 10,
      end: 20
    }
  },
  created() {
    this.getDicts('scan_router_type').then(response => {
      this.routerTypeOptions = response.data
    })
    this.getDicts('scan_service_type').then(response => {
      this.serviceTypeOptions = response.data
      if (this.serviceTypeOptions.length > 0) {
        this.queryParams.service = this.serviceTypeOptions[0].value
      } else {
        this.queryParams.service = 'all'
      }
      this.getList()
    })
  },
  methods: {
    // 计算分页数据范围，用于表显示
    handlePagination() {
      // 计算起始索引
      this.start = (this.queryParams.pageIndex - 1) * this.queryParams.pageSize
      // 计算结束索引
      this.end = this.start + this.queryParams.pageSize
      // 如果结束索引超过总记录数，则将结束索引设置为最后一条记录的索引
      if (this.end > this.total) {
        this.end = this.total
      }
      // 截取出需要展示的数据
      this.requestsDayStatisticsList = this.allData.slice(this.start, this.end)
    },
    /** 查询参数列表 */
    getList() {
      this.xAxisDatas = []
      this.taskDatas = []
      // this.failCountDatas = []

      this.avgDuration = []
      this.maxDuration = []
      this.successRate = []
      this.loading = true
      var d = new Date()
      if (this.dateRange === null || this.dateRange.length === 0) {
        this.dateRange = []
        var sd = new Date(d.getTime() - 14 * 24 * 60 * 60 * 1000)

        var smonth = sd.getMonth() < 9 ? '0' + (sd.getMonth() + 1).toString() : (sd.getMonth() + 1).toString()
        var sday = sd.getDate() < 9 ? '0' + (sd.getDate() + 1).toString() : (sd.getDate() + 1).toString()

        var edmonth = d.getMonth() < 9 ? '0' + (d.getMonth() + 1).toString() : (d.getMonth() + 1).toString()
        var edday = d.getDate() < 9 ? '0' + (d.getDate() + 1).toString() : (d.getDate() + 1).toString()

        this.dateRange.push(sd.getFullYear() + smonth + sday)
        this.dateRange.push(d.getFullYear() + edmonth + edday)
      }
      this.queryParams.startTime = this.dateRange[0]
      this.queryParams.endTime = this.dateRange[1]
      getStatisticsList(this.queryParams).then((response) => {
        this.loading = false

        // 后端返回的全部数据
        this.allData = response.data.list
        // 返回的数据数量
        this.total = response.data.count

        if (this.total === 0) {
          this.requestsDayStatisticsList = []
          return
        }

        for (var i = response.data.list.length - 1; i >= 0; i--) {
          // 图
          this.xAxisDatas.push(response.data.list[i].day)
          this.taskDatas.push(response.data.list[i].request_total)
          // 表
          var successRate = this.calSuccessRate(response.data.list[i])
          var failedRate = this.calFailedRate(response.data.list[i])
          var failedCount = this.calFailedCount(response.data.list[i])
          response.data.list[i].successRate = successRate
          response.data.list[i].failedRate = failedRate
          response.data.list[i].failedCount = failedCount

          var requestTimeAvgS = response.data.list[i].request_time_avg / 1000
          var requestTimeMaxS = response.data.list[i].request_time_max / 1000
          var requestTimeMinS = response.data.list[i].request_time_min / 1000
          if (response.data.list[i].request_total === 0) {
            requestTimeMinS = 0
          }
          response.data.list[i].requestTimeAvgS = requestTimeAvgS
          response.data.list[i].requestTimeMaxS = requestTimeMaxS
          response.data.list[i].requestTimeMinS = requestTimeMinS
          this.successRate.push(successRate)
          this.avgDuration.push(requestTimeAvgS)
          this.maxDuration.push(requestTimeMaxS)
        }

        // 表，默认显示10条数据
        this.requestsDayStatisticsList = response.data.list.slice(0, 10)
        // this.requestsDayStatisticsList = response.data.list
        if (this.category === 'router') {
          this.isShowRouter = true
          this.isShowDetail = false
        } else {
          this.isShowRouter = false
          this.isShowDetail = true
        }
      })
    },
    // 表单重置
    reset() {
      this.form = {}
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.$refs.queryForm.validate(valid => {
        if (!valid) {
          return
        } else {
          this.queryParams.pageIndex = 1
          this.getList()
        }
      })
    },
    stringToDate(dateStr, separator) {
      if (!separator) {
        separator = '-'
      }
      var dateArr = dateStr.split(separator)
      var year = parseInt(dateArr[0])
      var month
      if (dateArr[1].indexOf('0') === 0) {
        month = parseInt(dateArr[1].substring(1))
      } else {
        month = parseInt(dateArr[1])
      }
      var day = parseInt(dateArr[2])
      var date = new Date(year, month - 1, day)
      return date
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.category = 'service'
      this.queryParams.dataType = ''
      this.queryParams.startTime = ''
      this.queryParams.endTime = ''
      this.queryParams.service = this.serviceTypeOptions[0].value
      this.dateRange = []
      this.handleQuery()
      this.$refs.queryForm.clearValidate('router')
      this.$refs.queryForm.clearValidate('service')
    }, calFailedCount(row) {
      return row.request_total - row.request_success
    },
    calSuccessRate(row) {
      if (row.request_success === 0 || row.request_total === 0) {
        return 0
      }
      return this.toFixedWithoutZero((row.request_success / row.request_total * 100), 2)
    },
    calFailedRate(row) {
      if (row.request_total === 0) {
        return 0
      }
      return this.toFixedWithoutZero(((row.request_total - row.request_success) / row.request_total) * 100, 2)
    }, toFixedWithoutZero(num, fixed) {
      const result = num.toFixed(fixed)
      if (result.endsWith('0')) {
        return parseFloat(result).toString()
      }
      return result
    },
    isExistRouter() {
      return this.queryParams.dataType !== '1'
    },
    handleTypeSelectionChange(selection) {
      if (selection !== 'router') {
        this.$refs.queryForm.clearValidate('router')
        this.queryParams.service = this.serviceTypeOptions[0].value
        this.queryParams.router = ''
        this.queryParams.dataType = '1'
      } else {
        // 选择路由
        this.$refs.queryForm.clearValidate('service')
        this.queryParams.service = ''
        this.queryParams.dataType = '0'
      }
    },
    gotoScanRequest(row) {
      const st = row.day.toString()
      var srv = row.service
      if (srv === 'all') {
        srv = ''
      }
      this.$router.push({ path: '/scan/scan-request', query: { startTime: st, router: row.router, service: srv }})
    }
  }
}
</script>
