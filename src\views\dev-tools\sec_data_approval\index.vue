
<template>
  <BasicLayout>
    <template #wrapper>
      <el-card class="box-card">
        <el-form ref="queryForm" :model="queryParams" :inline="true" label-width="68px">
          <el-form-item label="用途描述" prop="purposes"><el-input v-model="queryParams.purposes" placeholder="请输入用途"
              clearable size="small" @keyup.enter.native="handleQuery" />
          </el-form-item>
          <el-form-item label="文件地址" prop="path"><el-input v-model="queryParams.path" placeholder="请输入文件地址" clearable
              size="small" @keyup.enter.native="handleQuery" />
          </el-form-item>
          <el-form-item label="状态" prop="status"><el-input v-model="queryParams.status" clearable size="small"
              @keyup.enter.native="handleQuery" />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button v-permisaction="['upload2ks3:task:add']" type="primary" icon="el-icon-plus" size="mini"
              @click="handleAdd">新增
            </el-button>
          </el-col>

          <el-col :span="1.5">
            <el-button v-permisaction="['upload2ks3:task:edit']" type="success" icon="el-icon-edit" size="mini"
              :disabled="single" @click="handleUpdate">修改
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-permisaction="['upload2ks3:task:remove']" type="danger" icon="el-icon-delete" size="mini"
              :disabled="multiple" @click="handleDelete">删除
            </el-button>
          </el-col>
        </el-row>

        <el-table v-loading="loading" :data="taskList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="申请人" align="center" prop="executor" :show-overflow-tooltip="true" />
          <el-table-column label="申请类型" align="center" prop="application" :show-overflow-tooltip="true">
            <template slot-scope="scope">
              {{ getTaskTypes(scope.row.application) }}
            </template>
          </el-table-column>
          <el-table-column label="是否加密" align="center" prop="is_encrypt" :show-overflow-tooltip="true" v-if="false">
            <template slot-scope="scope">
              {{ getIsEncryptTaskTypes(scope.row.is_encrypt) }}
            </template>
          </el-table-column>
          <el-table-column label="申请用途" align="center" prop="purposes" :show-overflow-tooltip="true" />
          <el-table-column label="源文件地址" align="center" prop="path" :show-overflow-tooltip="true"/>
          <!-- <el-table-column label="文件大小" align="center" prop="file_size" :show-overflow-tooltip="true"/> -->
          <el-table-column label="状态" align="center" prop="status" :show-overflow-tooltip="true">
            <template slot-scope="scope">
              {{ getStatusText(scope.row.status) }}
            </template>
          </el-table-column>
          <el-table-column label="ks3地址" align="center" prop="url" :show-overflow-tooltip="true" v-if="false" />
          <el-table-column label="默认存储" align="center" prop="default_storage" :show-overflow-tooltip="true" v-if="false">
            <template slot-scope="scope">
              {{ getDefaultStorageStatusText(scope.row.default_storage) }}
            </template>
          </el-table-column>
          <el-table-column label="密钥" align="center" prop="aes_key" :show-overflow-tooltip="true" v-if="false" />
          <el-table-column label="备注" align="center" prop="remark" :show-overflow-tooltip="true" :v-if="false" />
          <el-table-column label="审批人" align="center" prop="approver" :show-overflow-tooltip="true">
            <template slot-scope="scope">
              {{ getApprover(scope.row.approver) }}
            </template>
          </el-table-column>
          <el-table-column label="更新时间" align="center" prop="exectime" :show-overflow-tooltip="true">
          </el-table-column>
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button size="mini" type="text" icon="el-icon-search" @click="() => showDetails(scope.row)">详情
              </el-button>
              <el-popconfirm class="delete-popconfirm" title="确认要修改吗?" confirm-button-text="修改"
                @onConfirm="handleUpdate(scope.row)">
                <el-button slot="reference" v-permisaction="['upload2ks3:task:edit']" size="mini" type="text"
                  icon="el-icon-edit">修改
                </el-button>
              </el-popconfirm>
              <el-popconfirm class="delete-popconfirm" title="确认要删除吗?" confirm-button-text="删除"
                @onConfirm="handleDelete(scope.row)">
                <el-button slot="reference" v-permisaction="['upload2ks3:task:remove']" size="mini" type="text"
                  icon="el-icon-delete">删除
                </el-button>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageIndex"
          :limit.sync="queryParams.pageSize" @pagination="getList" />
        <!-- 添加或修改对话框 -->
        <el-dialog :title="purposes" :visible.sync="open" width="500px">
          <el-form ref="form" :model="form" :rules="rules" label-width="80px">
            <el-form-item label="申请类型" prop="application">
              <div>
                <el-radio v-model="form.application" :label="option1">下载模型</el-radio>
                <el-radio v-model="form.application" :label="option2">下载数据</el-radio>
              </div>
            </el-form-item>
            <el-form-item label="是否加密" prop="is_encrypt">
              <div>
                <el-radio v-model="form.is_encrypt" :label="is_encrypt_option1">否</el-radio>
                <el-radio v-model="form.is_encrypt" :label="is_encrypt_option2">是</el-radio>
              </div>
            </el-form-item>
            <el-form-item label="用途描述" prop="purposes">
              <el-input v-model="form.purposes" placeholder="用途" type="textarea" />
            </el-form-item>
            <el-form-item label="文件地址" prop="path">
              <el-input v-model="form.path" placeholder="支持/data/share目录下的文件绝对路径" />
            </el-form-item>
            <el-form-item label="自选存储" prop="default_storage">
              <div>
                <el-radio v-model="form.default_storage" :label="default_storage_option1">否</el-radio>
                <el-radio v-model="form.default_storage" :label="default_storage_option2">是</el-radio>
              </div>
            </el-form-item>
            <el-form-item label="审批人" prop="approver">
              <div>
                <el-radio v-for="option in approver_list" :key="option.value" v-model="form.approver"
                  :label="option.value">
                  {{ option.label }}
                </el-radio>
              </div>
            </el-form-item>
            <el-form-item label="存储位置" prop="specify_storage" v-if="form.default_storage === default_storage_option2">
              <el-input v-model="form.specify_storage" placeholder="可指定仅上传权限的ks3桶位置" />
            </el-form-item>
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" placeholder="请备注申请下载的模型/数据大小；选择加密数据下载后进行数据解码;自选存储可选择指定ks3位置" type="textarea" />
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
          </div>
        </el-dialog>
        <el-dialog :visible="dialogVisible" title="任务详情" width="50%" @close="dialogVisible = false">
          <el-table :data="detailedTaskData" style="width: 100%">
            <el-table-column prop="key" label="Key" align="center" width="100"></el-table-column>
            <el-table-column prop="value" label="Value" align="center" width="800"></el-table-column>
          </el-table>
        </el-dialog>
      </el-card>
    </template>
  </BasicLayout>
</template>

<script>
import {
  addUpload2Ks3,
  delUpload2Ks3,
  getUpload2Ks3,
  listUpload2Ks3,
  updateUpload2Ks3
} from '@/api/tools/upload2ks3'

export default {
  name: 'Upload2Ks3',
  components: {},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 弹出层标题
      purposes: '',
      // 是否显示弹出层
      open: false,
      isEdit: false,
      // 类型数据字典
      typeOptions: [],
      taskList: [],

      // 查询参数
      queryParams: {
        pageIndex: 1,
        pageSize: 10,
        purposes: undefined,
        path: undefined,
        status: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        application: [{ required: true, message: '申请类型不能为空', trigger: 'blur' }],
        is_encrypt: [{ required: true, message: '是否加密不能为空', trigger: 'blur' }],
        default_storage: [{ required: true, message: '存储位置不能为空', trigger: 'blur' }],
        purposes: [{ required: true, message: '用途不能为空', trigger: 'blur' }],
        path: [{ required: true, message: '文件地址不能为空', trigger: 'blur' }],
        approver: [{ required: true, message: '选择一个审批人', trigger: 'blur' }],
        remark: [{ required: true, message: '请备注下载的模型/文件大小', trigger: 'blur' }],
      },
      application: '',
      // 0 下载模型 1 上传模型  2 下载数据 3 下载模型
      option1: 0,
      option2: 2,
      is_encrypt: '',
      is_encrypt_option1: 0,
      is_encrypt_option2: 1,
      default_storage: '',
      default_storage_option1: 0,
      default_storage_option2: 1,
      dialogVisible: false,
      detailedTaskData: [], // 包含所有列的详细信息
      approver_list: [],

    }
  },
  created() {
    this.getList()
    this.getDicts('approver_list').then((response) => {
      console.log(response.data)
      this.approver_list = response.data
    })
  },
  methods: {
    getStatusText(status) {
      switch (status) {
        case 0:
          return '审批中';
        case 1:
          return '审批通过';
        case 2:
          return '审批不通过';
        case 3:
          return '任务开始';
        case 4:
          return '正在执行';
        case 5:
          return '任务完成';
        case 6:
          return '已发通知';
        default:
          return '任务失败';
      }
    },
    getTaskTypes(task) {
      switch (task) {
        case 0:
          return '模型下载(base_sec)';
        case 1:
          return '模型上传(base_sec)';
        case 2:
          return '数据下载(base_sec)';
        case 3:
          return '模型下载(base_model_list)';
        default:
          return '模型上传(base_url)';
      }
    },
    getIsEncryptTaskTypes(task) {
      switch (task) {
        case 1:
          return '是';
        default:
          return '否';
      }
    },
    getDefaultStorageStatusText(task) {
      switch (task) {
        case 1:
          return '是';
        default:
          return '否';
      }
    },
    getApprover(approver) {
      const foundApprover = this.approver_list.find(item => item.value === approver);
      return foundApprover ? foundApprover.label : '无';
    },

    setCellStyle() {
      return 'white-space: pre-wrap;'
    },
    convertLongtime(time) {
      if (time === 0) { return '' }
      var date = new Date(time * 1000)
      var DD = String(date.getDate()).padStart(2, '0') // 获取日
      var MM = String(date.getMonth() + 1).padStart(2, '0') // 获取月份，1 月为 0
      var yyyy = date.getFullYear() // 获取年
      var hh = String(date.getHours()).padStart(2, '0') // 获取当前小时数(0-23)
      var mm = String(date.getMinutes()).padStart(2, '0') // 获取当前分钟数(0-59)
      var ss = String(date.getSeconds()).padStart(2, '0') // 获取当前秒数(0-59)
      var today = yyyy + '-' + MM + '-' + DD + ' ' + hh + ':' + mm + ':' + ss
      return today
    },
    /** 查询参数列表 */
    getList() {
      this.loading = true
      listUpload2Ks3(this.addDateRange(this.queryParams, this.dateRange)).then(
        (response) => {
          this.taskList = response.data.list
          this.total = response.data.count
          this.loading = false
        }
      )
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        purposes: undefined,
        path: undefined,
        config: undefined,
        remark: undefined,
        application: undefined,
        approver: undefined,
        is_encrypt: 0,
        default_storage: 0,
        specify_storage: undefined,
      }
      this.resetForm('form')
    },
    // 文件
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageIndex = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.purposes = '添加申请'
      this.isEdit = false
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    showDetails(row) {
      let dataArray = Object.keys(row).map(key => ({ key, value: row[key] }));
      this.detailedTaskData = dataArray; // 设置弹窗中的详细信息数据
      this.dialogVisible = true; // 显示弹窗
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getUpload2Ks3(id).then((response) => {
        this.form = response.data
        this.open = true
        this.purposes = '修改申请'
        this.isEdit = true
        this.form.exectime = this.form.exectime * 1000
      })
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          if (this.form.id !== undefined) {
            updateUpload2Ks3(this.form).then((response) => {
              if (response.code === 200) {
                this.msgSuccess(response.msg)
                this.open = false
                this.getList()
              } else {
                this.msgError(response.msg)
              }
            })
          } else {
            addUpload2Ks3(this.form).then((response) => {
              if (response.code === 200) {
                this.msgSuccess(response.msg)
                this.open = false
                this.getList()
              } else {
                this.msgError(response.msg)
              }
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      var Ids = (row.id && [row.id]) || this.ids

      this.$confirm('是否确认删除编号为"' + Ids + '"的数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(function () {
          return delUpload2Ks3({ ids: Ids })
        })
        .then((response) => {
          if (response.code === 200) {
            this.msgSuccess(response.msg)
            this.open = false
            this.getList()
          } else {
            this.msgError(response.msg)
          }
        })
        .catch(function () { })
    }
  }
}
</script>
