<template>
  <div>
    <!-- <el-button type="success" size="small" @click="openNewPage()">查看json结果</el-button> -->
    <!-- <json-viewer :value="data" :theme="theme" :expand-depth="1000" /> -->
    <!-- expand-depth设置为一个很大的数值以全部展开 -->
    <vue-markdown :source="data" />
  </div>
</template>

<script>
import VueMarkdown from 'vue-markdown'
export default {
  name: 'CVShowJson',
  components: { VueMarkdown },
  // props: {
  //   // 预留一个父组件传递来的值，在父组件中可以使用CVShowJson组件的方式进行显示<CVShowJson :json-data="kdcJsonData" />
  //   jsonData: {
  //     type: Object,
  //     default: function() {
  //       return {}
  //     }
  //   }
  // },
  data() {
    return {
      data: {},
      theme: 'dark'
    }
  },
  mounted() {
    const parm = this.$route.params.data
    this.data = JSON.parse(parm)
  }
}
</script>
