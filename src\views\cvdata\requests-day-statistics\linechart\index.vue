<template>
  <div :class="className" :style="{height:height,width:width}" />

</template>

<script>
import echarts from 'echarts'
import resize from './mixins/resize'

require('echarts/theme/macarons') // echarts theme

export default {
  name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '350px'
    },
    autoResize: {
      type: Boolean,
      default: true
    },
    // chartData: {
    //   type: Object
    // },
    xAxisDatas: {
      type: Array,
      default: () => []
    },
    taskDatas: {
      type: Array,
      default: () => []
    },
    failCount: {
      type: Array,
      default: () => []
    },
    avgDuration: {
      type: Array,
      default: () => []
    },
    avgPageDatas: {
      type: Array,
      default: () => []
    },
    totalPages: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      chart: null,
      opinionData: ['2', '6', '1', '3'],
      actd: ['5', '2', '7', '6']
    }
  },
  watch: {
    taskDatas: {
      deep: true,
      handler(val) {
        this.setOptions(val)
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    // if (!this.chart) {
    //   return
    // }
    // this.chart.dispose()
    // this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons')
      this.setOptions(this.chartData)
    },
    setOptions({ expectedData, actualData } = {}) {
      this.chart.setOption({
        xAxis: {
          data: this.xAxisDatas,
          boundaryGap: false,
          axisTick: {
            show: false
          },
          axisLabel: {
            rotate: -90
          }
        },
        grid: {
          left: 10,
          right: 10,
          bottom: 20,
          top: 30,
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          },
          padding: [5, 10]
        },
        yAxis: {
          type: 'value',
          axisTick: {
            show: false
          },
          max: function(value) {
            return null
          },
          min: function(value) {
            // console.log(value)
            var difnum = value.max - value.min
            if (difnum < 1 && difnum > 0.1) {
              return Math.floor(value.min)
            }
            if (difnum < 0.1 && difnum > 0.01) {
              return value.min - 0.1
            }
            if (difnum < 0.01 && difnum > 0.001) {
              return value.min - 0.01
            }
            // 判断误差是否比较小
            if (Math.abs(1 - value.min / value.max) < 0.1) {
              return Math.floor(value.min * 0.98)
            } else {
              return null
            }
          }
        },
        legend: {
          data: ['任务总数', '成功率', '平均耗时(s)', '平均页数', '总页数']
        },
        series: [{
          name: '任务总数', itemStyle: {
            normal: {
              color: '#FF005A',
              lineStyle: {
                color: '#FF005A',
                width: 2
              }
            }
          },
          smooth: true,
          type: 'line',
          data: this.taskDatas,
          animationDuration: 2800,
          animationEasing: 'cubicInOut'
        },
        {
          name: '成功率',
          smooth: true,
          type: 'line',
          itemStyle: {
            normal: {
              color: '#3888fa',
              lineStyle: {
                color: '#3888fa',
                width: 2
              },
              areaStyle: {
                color: '#f3f8ff'
              }
            }
          },
          data: this.failCount,
          animationDuration: 2800,
          animationEasing: 'quadraticOut'
        },
        {
          name: '平均耗时(s)',
          smooth: true,
          type: 'line',
          itemStyle: {
            normal: {
              color: '#FFD700',
              lineStyle: {
                color: '#FFD700',
                width: 2
              },
              areaStyle: {
                color: '#f3f8ff'
              }
            }
          },
          data: this.avgDuration,
          animationDuration: 2800,
          animationEasing: 'quadraticOut'
        },
        {
          name: '平均页数',
          smooth: true,
          type: 'line',
          itemStyle: {
            normal: {
              color: '#FF4500',
              lineStyle: {
                color: '#FF4500',
                width: 2
              },
              areaStyle: {
                color: '#f3f8ff'
              }
            }
          },
          data: this.avgPageDatas,
          animationDuration: 2800,
          animationEasing: 'quadraticOut'
        },
        {
          name: '总页数',
          smooth: true,
          type: 'line',
          itemStyle: {
            normal: {
              color: '#00ff00',
              lineStyle: {
                color: '#00ff00',
                width: 2
              },
              areaStyle: {
                color: '#00ff8a'
              }
            }
          },
          data: this.totalPages,
          animationDuration: 2800,
          animationEasing: 'quadraticOut'
        }]
      })
    }
  }
}
</script>
