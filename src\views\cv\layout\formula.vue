<template>
  <!-- 创建一个卡片式的容器，使用主题内容区域将背景色统一 -->
  <el-card>
    <!-- 标题插槽：显示使用说明 -->
    <div slot="header">
      <CVHint>使用说明：（公式识别）选择一张图片，系统会对图片进行处理，转换成功后会在页面展示结果</CVHint>
    </div>

    <!-- 主体内容 -->
    <div class="card-content">
      <div>
        <div class="container">
          <el-row type="flex" justify="center" :gutter="20">

            <el-col :span="8" class="grid-content">
              <div class="upload-container">
                <div class="params-container" style="width: 100% !important">

                  <div class="params">
                    <span class="param-label" style="width: 25% !important">版本 :</span>
                    <el-select
                      v-model="startPage.version"
                      class="param-input"
                      style="width: 75% !important"
                      placeholder="版本"
                      size="small"
                      @change="selectVersion"
                    >
                      <el-option
                        v-for="dict in versions"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                        @click.native="handleSelect(dict)"
                      />
                    </el-select>
                  </div>
                  <!-- <div class="params">
                    <span
                      class="param-label"
                      style="width: 25% !important"
                    >url of png :</span>
                    <el-input
                      v-model="fileUrl"
                      class="param-input"
                      style="width: 75% !important"
                      type="text"
                    />
                  </div> -->
                  <div class="params">
                    <span
                      class="param-label"
                      style="width: 25% !important"
                    >输出 :</span>
                    <el-select
                      v-model="startPage.exporttype"
                      class="param-input"
                      style="width: 75% !important"
                      placeholder="导出类型"
                      size="small"
                      @change="selectExporttype"
                    >
                      <el-option
                        v-for="dict in exporttypeOptions"
                        :key="dict"

                        :value="dict"
                      />
                    </el-select>
                  </div>
                  <br><div class="params">
                    <!-- :disabled="true" -->
                    <el-button
                      type="warning"
                      size="mini"
                      @click="handleSetting"
                    >高级设置</el-button>
                    <el-tooltip content="切换输入方式" placement="right-end">
                      <el-select v-model.number="choiceInput" placeholder="默认上传文件">
                        <el-option
                          v-for="dict in choiceInputOptions"
                          :key="dict.value"
                          :label="dict.label"
                          :value="dict.value"
                        />
                      </el-select>
                    </el-tooltip>
                    <!-- <el-button
                      type="primary"
                      size="mini"
                      :disabled="startPage.exporttype==='picedit'"
                      @click="convertclick"
                    >转换</el-button> -->
                  </div>

                  <hr>
                  <div v-if="choiceInput=='inputUrl'">
                    <br><span style="color:blue"> {{ "输入图片链接：" }}</span><br>
                    <div class="params">
                      <span
                        class="param-label"
                        style="width: 25% !important"
                      >url of png :</span>
                      <el-input
                        v-model="fileUrl"
                        class="param-input"
                        style="width: 75% !important"
                        type="text"
                      />
                    </div>
                    <el-button
                      type="primary"
                      size="mini"
                      :disabled="startPage.exporttype==='picedit'"
                      @click="convertclick"
                    >开始转换</el-button>
                  </div>

                  <!-- 转换或点击上传前高级设置对话框 -->
                  <el-dialog :title="title" :visible.sync="open" width="600px">
                    <el-form ref="form" :model="form" :rules="rules" label-width="120px" inline-message>

                      <el-row>
                        <el-col :span="24">
                          <el-tooltip content="选择调用的公式模型" placement="left-end">
                            <template slot="default">
                              <el-form-item label="choice_model:" prop="choiceModel">
                                <el-select v-model.number="form.choiceModel" :disabled="true" placeholder="请选择">
                                  <el-option
                                    v-for="dict in choiceModelOptions"
                                    :key="dict.value"
                                    :label="dict.label"
                                    :value="dict.value"
                                  />
                                </el-select>
                              </el-form-item>
                            </template>
                          </el-tooltip>
                        </el-col>
                        <el-col :span="24">
                          <el-tooltip content="选择是否需要绘制公式框/文本框" placement="left-end">
                            <template slot="default">
                              <el-form-item label="Latex/Text:" prop="choiceRectangles">
                                <el-select v-model.number="form.choiceRectangles" placeholder="请选择">
                                  <el-option
                                    v-for="dict in choiceRectanglesOptions"
                                    :key="dict.value"
                                    :label="dict.label"
                                    :value="dict.value"
                                  />
                                </el-select>
                              </el-form-item>
                            </template>
                          </el-tooltip>
                        </el-col>
                        <el-col :span="24">
                          <el-tooltip content="是否开启公式" placement="left-end">
                            <template slot="default">
                              <el-form-item label="get_formula:">
                                <el-select v-model="form.get_formula" placeholder="请选择" @change="$forceUpdate()">
                                  <el-option
                                    v-for="dict in isFormulaOptions"
                                    :key="dict.value"
                                    :label="dict.label"
                                    :value="Number(dict.value)"
                                  />
                                </el-select>
                                <span style="color: #0073ed; font-size: 12px;">只有【开发版】支持该参数</span>
                              </el-form-item>
                            </template>
                          </el-tooltip>
                        </el-col>

                      </el-row>
                    </el-form>

                    <div slot="footer" class="dialog-footer">
                      <el-button class="reset-button" type="warning" @click="reset">重 置</el-button>
                      <div class="right-buttons">
                        <el-button class="confirm-button" type="primary" @click="submitForm">确 定</el-button>
                        <el-button class="cancel-button" @click="cancel">取 消</el-button>
                      </div>
                    </div>
                  </el-dialog>
                </div>

                <!-- <div class="params-container">
                  <br><div v-show="fileName !== ''" class="params">
                    <span style="color:blue" class="param-label">{{ '转化结果' }} :</span>
                  </div>
                  <div v-show="fileName !== ''" class="params">
                    <span class="param-label">{{ fileName }}</span>
                  </div>
                  <div v-show="statistic.status !== -1" class="params">
                    <span class="param-label">处理用时:</span>
                    <span class="param-input">
                      {{ formattedDuration }}
                    </span>
                  </div>
                  <div v-show="statistic.status !== -1" class="time-params">
                    <div v-for="(time, index) in resultTime" :key="index">
                      <span class="param-label">{{ resultTime[index].comment }}</span>
                      <span class="param-input">
                        {{ resultTime[index].value + 's' }}<br>
                      </span>
                    </div>
                  </div>
                  <div v-show="statistic.status !== -1" class="params">
                    <span v-show="statistic.status === 1" class="param-label" style="color:blue;">转化成功!</span>
                    <span v-show="statistic.status === 0" class="param-label" style="color: #ff0000;">转化失败!</span>
                  </div>

                </div> -->

                <div v-if="choiceInput=='uploadFile'">
                  <br><span style="color:blue"> {{ "上传图片：" }}</span>
                  <el-upload
                    v-loading="loading"
                    element-loading-text="文件处理中"
                    class="uploader"
                    list-type="text"
                    :accept="'image/*'"
                    :multiple="false"
                    :show-file-list="false"
                    :file-list="fileList"
                    :http-request="submitUpload"
                    :on-success="onSuccess"
                    action="placeholder"
                    drag
                  >
                    <i class="el-icon-upload" />
                    <div class="el-upload__text">
                      将文件拖到此处，或
                      <el-button type="text">点击上传</el-button>
                    </div>
                  </el-upload>

                  <el-progress
                    v-show="percentage !== 0 && percentage !== 100"
                    :text-inside="true"
                    :stroke-width="14"
                    :percentage="percentage"
                  />
                </div>

                <div class="params-container">
                  <br><div v-show="fileName !== ''" class="params">
                    <span style="color:blue" class="param-label">{{ '转化结果' }} :</span>
                  </div>
                  <div v-show="fileName !== ''" class="params">
                    <span class="param-label">{{ fileName }}</span>
                  </div>
                  <div v-show="statistic.status !== -1" class="params">
                    <span class="param-label">处理用时:</span>
                    <span class="param-input">
                      {{ formattedDuration }}
                    </span>
                  </div>
                  <div v-show="statistic.status !== -1" class="time-params">
                    <div v-for="(time, index) in resultTime" :key="index">
                      <span class="param-label">{{ resultTime[index].comment }}</span>
                      <span class="param-input">
                        {{ resultTime[index].value + 's' }}<br>
                      </span>
                    </div>
                  </div>
                  <div v-show="statistic.status !== -1" class="params">
                    <span v-show="statistic.status === 1" class="param-label" style="color:blue;">转化成功!</span>
                    <span v-show="statistic.status === 0" class="param-label" style="color: #ff0000;">转化失败!</span>
                  </div>
                </div>

              </div>
              <ul>
                <li v-for="(errMsg, index) in result.errMsgs" :key="index">
                  {{ errMsg }}
                </li>
              </ul>
            </el-col>
            <el-col
              :span="5"
              class="grid-content"
            >
              <CVDemo :thumbnail="true" :loading="loading" :data="urlFiles[0]" @click="tableextractDemo(0,$event)">示例:</CVDemo>
            </el-col>
          </el-row>
        </div>

        <div v-show="showResult && showFormula" class="result-button">
          <el-row>
            <el-col :span="8">
              <el-button type="primary" size="small" @click="choiceFormulaType(choiceFormulaOptions[0].value)">全部公式</el-button>
            </el-col>
            <el-col :span="8">
              <el-button type="success" size="small" @click="choiceFormulaType(choiceFormulaOptions[1].value)">行内公式</el-button>
            </el-col>
            <el-col :span="8">
              <el-button type="warning" size="small" @click="choiceFormulaType(choiceFormulaOptions[2].value)">独立公式</el-button>
            </el-col>
          </el-row>
        </div>
        <br>

        <!-- 公式渲染结果展示 -->
        <div class="container">
          <div v-show="showResult" class="result">
            <el-row class="formula-row">
              <el-col>
                {{ '公式识别结果为:' }}
                <div v-for="(item, index) in originData" :key="`${item}-${index}`">
                  <div v-katex>
                    <br><div :id="'formula-' + index">
                      {{ '公式' + (index + 1) + ' 为:' }}
                      {{ `$$${originData[index]}$$` }}
                    </div>
                    <div v-if="originData[index]">
                      {{ '公式' + (index + 1) + ' 原文为:' }}
                      {{ originData[index] }}
                    </div>
                    <!-- <div v-if="originImg[index]"> -->
                    <div v-if="originData[index]">
                      <div>
                        <el-button
                          v-if="item"
                          type="text"
                          size="medium"
                          @click="copyLink(originData[index])"
                        >复制公式原文</el-button>
                      </div>
                    </div>
                  </div>
                  <hr> <!-- 添加分割线 -->
                </div>
                <!-- {{ '公式上下文结果为:' }}
                <div v-for="(item, index) in originTextData" :key="item">
                  <div v-if="item[index]">
                    {{ '公式上下文' + (index + 1) + ' 为:' }}
                    {{ originTextData[index] }}
                  </div>
                  <hr>
                </div> -->
              </el-col>
            </el-row>
            <el-row class="result-row" style="width: 750px;">
              <el-col>

                <div class="canvas-container" style="width: 750px;">
                  <canvas ref="canvas" :width="imageWidth" :height="imageHeight" />
                  <img :src="originUrl" @load="setImageSize">
                </div>
                <!-- <div v-if="drawImageUrl"> -->
                <div>
                  <el-button size="small" type="primary" @click="zoomIn">放大</el-button>
                  <el-button size="small" type="primary" @click="zoomOut">缩小</el-button>
                  <!-- <el-button size="small" type="info" @click="openDialog">查看图片</el-button> -->
                </div>

              </el-col>
            </el-row>

            <el-dialog :visible="showDialog" @close="closeDialog">
              <div class="canvas-container">
                <canvas ref="canvas" :width="imageWidth" :height="imageHeight" />
                <img :src="originUrl" @load="setImageSize">
              </div>
            </el-dialog>

          </div>
        </div>
      </div>
    </div>

    <!-- 底部插槽：未使用，后续若是扩展可以考虑翻页等功能 -->
    <div slot="footer">
      footer
    </div>
  </el-card>
</template>

<script>
import axios from 'axios'
import CVHint from './components/CVHint'
import CVDemo from './components/CVDemo'

import { isValidFile } from '@/api/cv/utils'
import { fetchUrl2Ks } from '@/api/cv/request-ks3'
import { getHeaders } from '@/utils/get-header'

import { axios_wait_doc } from '@/api/cv/request'
import { sleep } from '@/api/cv/utils'

export default {
  name: 'Formula',
  components: { CVHint, CVDemo },
  props: {
    maxFileNum: {
      validator(value) {
        if (value <= 0) {
          return 1
        }
        return value
      },
      default: 1
    },
    fileType: {
      type: Array,
      default: function() {
        return ['.png', '.jpg', '.jpeg', '.pdf', '.zip']
      }
    }
  },
  data() {
    return {
      // 输入方式字典
      choiceInputOptions: [
        { label: '上传图片(默认)', value: 'uploadFile' },
        { label: '输入图片链接', value: 'inputUrl' }
      ],
      choiceInput: 'uploadFile',
      // 公式模型字典
      choiceModelOptions: [
        { label: 'formula_small(默认)', value: 'formula_small' },
        { label: 'formula', value: 'formula' }
      ],
      // 绘制公式框/文本框字典
      choiceRectanglesOptions: [
        { label: '只绘制公式框(默认)', value: 'Latex' },
        { label: '只绘制文本框', value: 'Text' },
        { label: '公式框+文本框', value: 'LatexAndText' }
      ],
      // 选择显示行间公式/所有公式
      choiceFormulaOptions: [
        { label: '显示所有公式(默认)', value: 'allFormula' },
        { label: '只显示行内公式', value: 'inlineFormula' },
        { label: '只显示独立公式', value: 'displayedFormula' }
      ],
      // 是否开启公式
      isFormulaOptions: [
        { label: '是', value: '1' },
        { label: '否', value: '0' }
      ],
      // 耗时情况
      resultTime: [],
      fileName: '',
      // 画框后的图片url
      drawImageUrl: '',
      dialogVisible: false, // 弹窗的显示与隐藏
      // 控制公式识别结果的显示
      showResult: false,
      showFormula: true,
      // 存储公式图片
      originImg: [],
      // 存储公式
      originData: [],
      originAllData: [],
      // 存储转换后的公式
      formulas: [],
      // 弹出层标题（用户导入）
      title: '',
      // 是否显示弹出层（用户导入）
      open: false,
      // 表单参数
      form: {
        choiceModel: 'formula_small',
        choiceRectangles: 'Latex',
        get_formula: 1,
        // choiceFormula: 'allFormula',
        originWidth: 10
      },
      // 参数
      pdfDebugFlags: 0,
      choiceModel: 'formula_small',
      choiceRectangles: 'Latex',
      get_formula: 1,
      originWidth: 10,
      // 表单校验
      rules: {
        // 定义规则对象
        // trigger: 'blur' 表示在失去焦点时触发验证
        originHeight: [{ type: 'number', message: 'originHeight必须为int类型', trigger: ['blur', 'change'] }],
        originWidth: [{ type: 'number', message: 'originWidth必须为int类型', trigger: ['blur', 'change'] }]
      },
      loading: false,
      url: {
        value: '//pcv-test.wps.cn',
        default: '//pcv-test.wps.cn',
        gray: '//pcv-test-gray.wps.cn',
        intDebug: '//pcv-int-debug.wps.cn/zhumoextend_gray'
      },
      fileObj: {
        name: '',
        size: 0
      },
      versionremark: '',
      remark: '',
      showNewVersion: false,
      versions: [],
      //   exporttypeOptions: ['html', 'img'],
      exporttypeOptions: ['html'],
      startPage: {
        exporttype: 'html',
        version: '',
        xlsxicon: '',
        splitsheet: '一个表格一个sheet',
        pagetext: '否',
        pagetextoption: '所有文本一个单元格'
      },
      fileUrl: '',
      fileList: [],
      fileData: '',
      // 进度百分比
      percentage: 0,
      result: {
        duration: 0,
        zipUrl: '',
        xlsxUrl: '',
        errMsgs: []
      },
      statistic: {
        status: -1,
        duration: 0
      },
      status: -1,
      originalImages: [],
      htmlsInZip: [],
      urlFiles: [
        [
          {
            url: 'http://zhai-datas.ks3-cn-beijing.ksyuncs.com/layout/people/wangluoyan/formula/formula01.jpeg',
            name: '示例一.jpeg'
          },
          {
            url: 'http://zhai-datas.ks3-cn-beijing.ksyun.com/layout/people/wangluoyan/formula/formula02.jpeg',
            name: '示例二.jpeg'
          },
          {
            url: 'http://zhai-datas.ks3-cn-beijing.ksyun.com/layout/people/wangluoyan/formula/formula03.jpeg',
            name: '示例三.jpeg'
          },
          {
            url: 'http://zhai-datas.ks3-cn-beijing.ksyun.com/layout/people/wangluoyan/formula/formula04.jpeg',
            name: '示例四.jpeg'
          },
          {
            url: 'http://zhai-datas.ks3-cn-beijing.ksyun.com/layout/people/wangluoyan/formula/formula05.jpeg',
            name: '示例五.jpeg'
          }
        ]
      ],
      // 原图url
      originUrl: '',
      backupUrl: '',
      // 原始宽高值
      originimageHeight: 0,
      originimageWidth: 0,
      // 图片的缩放比例
      scale: 1,
      originScale: 1,
      // 图片展示宽度
      imageWidth: 750,
      imageHeight: 0,
      // 公式框
      rectanglesLatex: [
        { x: 193, y: 229, width: 232, height: 32 }
      ],
      // 独立公式框
      displayedRectanglesLatex: [
        { x: 193, y: 229, width: 232, height: 32 }
      ],
      // 行内公式框
      inlineRectanglesLatex: [
        { x: 193, y: 229, width: 232, height: 32 }
      ],
      // 文本框
      rectanglesText: [
        { x: 10, y: 10, width: 100, height: 50 }
      ],
      choiceFormula: 'allFormula',
      // 保存公式框初始值
      originalRectanglesLatex: [],
      originalRectanglesText: [],
      originalInlineRectanglesLatex: [],
      originalDisplayedRectanglesLatex: [],
      // // 矫正图片返回结果的标志
      // correctionImg: false,
      showDialog: false, // 控制弹窗显示/隐藏的数据属性
      // 公式框序号字体大小
      fontSize: 11,
      // 获取layout返回的矫正后图片url
      getExtend: 1
    }
  },
  computed: {
    // 根据公式长度确定组件长度
    contentSize() {
      // 假设每个字符的宽度为 10px
      const charWidth = 5
      // 计算公式内容的最大长度
      const maxLength = this.formulas.reduce((max, item) => {
        const length = item.latex.length
        return length > max ? length : max
      }, 0)
      //   const formulasLength = this.formulas.val.length
      // 根据公式长度计算组件区域的大小
      return maxLength * charWidth
    },
    formattedDuration() {
      return this.statistic.duration.toFixed(3) + 's'
    }
  },
  // 通过watch监听输入值的变化，并将maxConNumPage/pdfDebugFlags由 string类型转为 int类型
  watch: {
    'form.pdfDebugFlags'(val) {
      this.pdfDebugFlagsInt = parseInt(val)
    }
  },
  created() {
    this.getDicts('pdf_convert_version').then(response => {
      this.versions = response.data

      if (this.versions.length > 0) {
        // 公式单独转一下线上版的host
        // if (this.versions[0].label === '线上版') {
        //   this.url.value = this.url.intDebug
        // } else {
        //   this.url.value = this.versions[2].value
        // }
        this.url.value = this.versions[2].value
        this.startPage.version = this.versions[2].label
        this.remark = this.versions[2].remark
      }
    })
  },
  methods: {
    progressPass(percentage) {
      this.percentage = 100
    },
    updateImgSize() {
      this.imageHeight = (this.scale * this.originimageHeight).toFixed(2)

      // 得到更新后的坐标值
      // const updateRectangles = (rectangles, originalRectangles) => {
      //   rectangles.forEach((rectangle, index) => {
      //     rectangle.x = originalRectangles[index].x * this.scale
      //     rectangle.y = originalRectangles[index].y * this.scale
      //     rectangle.width = originalRectangles[index].width * this.scale
      //     rectangle.height = originalRectangles[index].height * this.scale
      //   })
      // }
      const updateRectangles = (rectangles, originalRectangles) => {
        rectangles.forEach((rectangle, index) => {
          rectangle.x1 = originalRectangles[index].x1 * this.scale
          rectangle.y1 = originalRectangles[index].y1 * this.scale
          rectangle.x2 = originalRectangles[index].x2 * this.scale
          rectangle.y2 = originalRectangles[index].y2 * this.scale
          rectangle.x3 = originalRectangles[index].x3 * this.scale
          rectangle.y3 = originalRectangles[index].y3 * this.scale
          rectangle.x4 = originalRectangles[index].x4 * this.scale
          rectangle.y4 = originalRectangles[index].y4 * this.scale
        })
      }

      if (this.choiceRectangles.includes('Latex')) {
        updateRectangles(this.rectanglesLatex, this.originalRectanglesLatex)
        updateRectangles(this.displayedRectanglesLatex, this.originalDisplayedRectanglesLatex)
        updateRectangles(this.inlineRectanglesLatex, this.originalInlineRectanglesLatex)
      }

      if (this.choiceRectangles.includes('Text')) {
        updateRectangles(this.rectanglesText, this.originalRectanglesText)
      }

      // 重新绘制
      if (this.choiceFormula === 'allFormula') {
        this.drawRectangles(this.backupUrl, this.rectanglesLatex, this.fontSize)
      } else {
        this.drawRectangles(this.backupUrl, this.choiceFormula, this.fontSize)
      }

      // 关闭原始图片
      this.originUrl = ''
    },
    setImageSize(event) {
      const image = event.target
      // 存储原始图像高度宽度值
      this.originimageWidth = image.width
      this.originimageHeight = image.height
      // 计算初始scale
      this.scale = (this.imageWidth / this.originimageWidth).toFixed(4)
      this.imageHeight = (this.scale * this.originimageHeight).toFixed(2)

      this.drawRectangles(this.originUrl, this.rectanglesLatex, this.fontSize)
      // // 关闭原始图片
      this.backupUrl = this.originUrl
      this.originUrl = ''
    },
    drawRectangles(url, rectanglesLatex, fontSize) {
      const canvas = this.$refs.canvas
      const ctx = canvas.getContext('2d')

      const image = new Image()
      image.src = url

      image.onload = () => {
        ctx.drawImage(image, 0, 0, this.imageWidth, this.imageHeight)

        ctx.lineWidth = 1
        // 绘制公式框
        if (this.choiceRectangles.includes('Latex')) {
          ctx.strokeStyle = 'red'
          rectanglesLatex.forEach((rect, index) => {
            ctx.beginPath()
            ctx.moveTo(rect.x1, rect.y1)
            ctx.lineTo(rect.x2, rect.y2)
            ctx.lineTo(rect.x3, rect.y3)
            ctx.lineTo(rect.x4, rect.y4)
            ctx.closePath()
            ctx.stroke()
            // ctx.strokeRect(rect.x, rect.y, rect.width, rect.height)

            // 设置文本字体字号
            ctx.font = fontSize.toString() + 'px Arial'

            // 绘制文本
            var text = (index + 1).toString()
            // 获取文本字体的宽度/高度
            var textWidth = ctx.measureText(text).width
            var textHeight = parseInt(ctx.font)

            // 设置白底
            ctx.fillStyle = 'white'
            // 使用第一个点的坐标来绘制文本背景
            ctx.fillRect(rect.x1 - 2, rect.y1 - textHeight + 2, textWidth + 4, textHeight)

            // 设置文本颜色
            ctx.fillStyle = 'rgba(0, 0, 255)'
            // 使用第一个点的坐标来绘制文本
            ctx.fillText(text, rect.x1, rect.y1)
          })
        }
        // 绘制文本框
        if (this.choiceRectangles.includes('Text')) {
          ctx.strokeStyle = 'blue'
          this.rectanglesText.forEach(rect => {
            ctx.beginPath()
            ctx.moveTo(rect.x1, rect.y1)
            ctx.lineTo(rect.x2, rect.y2)
            ctx.lineTo(rect.x3, rect.y3)
            ctx.lineTo(rect.x4, rect.y4)
            ctx.closePath()
            ctx.stroke()
          })
        }
        console.log('绘制完成')
      }
    },
    fileChange(file, fileList) {
      this.fileList = fileList.slice(-this.maxFileNum)
    },
    onSuccess(response, file, fileList) {
      // hook
      this.fileChange(file, fileList)
    },
    openDialog() {
      this.dialogVisible = true // 打开弹窗
    },
    closeDialog() {
      this.dialogVisible = false // 关闭弹窗
    },
    /** 放大按钮 */
    zoomIn() {
      // 添加限制，避免过大
      if (this.imageWidth < 1000) {
        this.imageWidth = this.imageWidth + 100
        this.scale = (this.imageWidth / this.originimageWidth).toFixed(4)
      }
      // 放大字号
      if (this.fontSize < 15) {
        this.fontSize += 1
      }

      // 更新宽高值,并重新绘制
      this.updateImgSize()
    },
    /** 缩小按钮 */
    zoomOut() {
      // 添加限制，避免过小
      if (this.imageWidth > 10) {
        this.imageWidth = this.imageWidth - 50
        this.scale = (this.imageWidth / this.originimageWidth).toFixed(4)
      }
      // 缩小字号
      if (this.fontSize > 8) {
        this.fontSize -= 1
      }
      // 更新宽高值,并重新绘制
      this.updateImgSize()
    },
    /** 复制链接按钮 */
    copyLink(url) {
      var inputNode = document.getElementById('pdfcv_input')
      if (inputNode != null) {
        inputNode.remove()
      }
      inputNode = document.createElement('input')
      inputNode.value = url
      inputNode.id = 'pdfcv_input'
      document.body.appendChild(inputNode)
      inputNode.select()
      document.execCommand('Copy')
      inputNode.className = 'oInput'
      inputNode.style.display = 'none'
      this.$message.success('复制成功')
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.choiceModel = this.form.choiceModel
          this.choiceRectangles = this.form.choiceRectangles
          this.get_formula = this.form.get_formula
          this.originWidth = this.form.originWidth
          this.open = false
        }
      })
    },
    // 取消按钮
    cancel() {
      this.form.choiceModel = this.choiceModel
      this.form.choiceRectangles = this.choiceRectangles
      this.form.get_formula = this.get_formula
      this.form.originWidth = this.originWidth
      this.open = false
    },
    /** 重置按钮 */
    // 表单重置
    reset() {
      this.form = {
        choiceModel: 'formula_small',
        choiceRectangles: 'Latex',
        get_formula: 1,
        originWidth: 10
      }
      this.resetForm('form')
    },
    /** 高级设置按钮操作 */
    handleSetting() {
      this.open = true
      this.title = '高级设置'
      this.form.password = ''
    },
    // 获取版本
    handleSelect(item) {
      this.remark = item.remark
    },
    // 选择版本
    selectVersion(value) {
      this.versionremark = ''
      this.url.value = value
      this.resetResult()
    },
    // 选择导出类型
    selectExporttype(value) {
      if (value === 'html') {
        this.showNewVersion = false
        this.startPage.pagetextoption = '所有文本一个单元格'
        this.startPage.splitsheet = '一个表格一个sheet'
      } else {
        this.showNewVersion = true
        this.versionremark = ''
      }
    },
    resetResult() {
      this.result = {
        duration: 0,
        zipUrl: '',
        errMsgs: [] }
      this.statistic = {
        status: -1,
        duration: 0
      }
      this.status = -1
      this.originalImages = []
      this.htmlsInZip = []
      this.resultTime = []
      this.drawImageUrl = ''
      // this.correctionImg = false
      this.showResult = false
      this.showFormula = true
      // 按钮重置
      this.choiceFormula = 'allFormula'
      // 字体大小
      this.fontSize = 11
    },
    // ks3上传前的文件类型检查
    beforeUpload(file) {
      // 清理旧结果
      this.fileObj = { name: '', size: 0 }
      this.resetResult()

      const { name } = file
      var ext = '.' + name.trim().split('.').pop()
      ext = ext.toLowerCase()
      if (!isValidFile(ext, this.fileType)) {
        this.$message({
          showClose: true,
          message: '请选择png/jpg/jpeg/pdf文件/zip文件!',
          center: true,
          duration: 4000,
          type: 'error'
        })
        return false
      } else {
        this.fileObj = file
        return true
      }
    },
    /** 转换按钮操作 */
    async convertclick() {
      this.resetResult()
      this.fileName = ''
      console.log(this.fileUrl)
      // 检查url是否有效
      this.checkImageLinkAvailability(this.fileUrl)
        .then(async() => {
          // 执行链接成功后的逻辑
          console.log('url匹配通过')

          this.loading = true
          // 检查this.fileUrl字符串中是否包含ksyun.com子字符串
          if (this.fileUrl.lastIndexOf('ksyun.com') > 0) {
            var urls = []
            // 将this.fileUrl的值添加到一个名为urls的数组中
            urls.push(this.fileUrl)
            const s_time = new Date().getTime()
            // 请求
            // const status = await this.formulaextract(urls)
            // create请求
            const create_docID = await this.formulaCreate(urls)
            if (create_docID !== undefined) {
              // 等待1s
              await sleep(1000)
              // query请求
              await this.waitDocStatus(create_docID)
            }
            // 结束加载
            this.loading = false
            const e_time = new Date().getTime()
            this.statistic = {
              status: this.status,
              duration: this.result.duration || (e_time - s_time) / 1000
            }
            return Promise.resolve()
          } else {
            this.fetchUrl2Ks3(this.fileUrl)
          }
          this.loading = false
        })
        .catch(() => {
          // 执行链接加载失败后的逻辑
          this.$message({
            showClose: true,
            message: '请输入正确文件地址url ',
            center: true,
            duration: 4000,
            type: 'error'
          })
        })
    },
    beforeCreate(url) {
      // 保存绘制原图
      this.originUrl = url.replace(/-internal/g, '')
      // 清空之前的缩放比例
      this.scale = 1
      // 清空之前的原始坐标值
      this.originalRectanglesLatex = []
      this.originalRectanglesText = []
      this.originalInlineRectanglesLatex = []
      this.originalDisplayedRectanglesLatex = []
      this.inlineRectanglesLatex = []
      this.displayedRectanglesLatex = []
      // 清空之前的矩形坐标
      this.rectanglesLatex = []
      this.rectanglesText = []
      // 重置初始宽度值
      this.imageWidth = 750
      // 重置渲染的公式
      this.formulas = []
      // 清空公式原文
      this.originData = []
      this.originAllData = []
      this.originDisplayedFormula = []
      this.originInlineFormula = []
    },
    // 转换按钮：若url不包含ksyun.com
    async fetchUrl2Ks3(sourceurl) {
      console.log('url不包含ksyun.com: ', sourceurl)
      fetchUrl2Ks(this, sourceurl).then(async(res) => {
        if (res.data.message === 'ok') {
          const ksurl = res.data.url
          console.log(ksurl)
          var urls = []
          urls.push(ksurl)
          const s_time = new Date().getTime()
          // const status = await this.formulaextract(urls)
          // 请求前的操作
          this.beforeCreate(urls)
          // create请求
          const create_docID = await this.formulaCreate(urls)
          if (create_docID !== undefined) {
            // 等待1s
            await sleep(1000)
            // query请求
            await this.waitDocStatus(create_docID)
          }
          // 结束加载
          this.loading = false
          const e_time = new Date().getTime()
          this.statistic = {
            status: this.status,
            duration: this.result.duration || (e_time - s_time) / 1000
          }
          return Promise.resolve()
        }
      }).catch((error) => {
        this.$message({
          showClose: true,
          message: '文件处理失败! ' + error,
          center: true,
          duration: 4000,
          type: 'error'
        })
        return Promise.resolve(0)
      })
    },
    async waitDocStatus(docID) {
      await axios_wait_doc({
        method: 'get',
        headers: { 'Cache-Control': 'no-cache', ...getHeaders() },
        url:
          // this.url.gray + '/layout/scheduler/pic2latex/query?docID=' + docID,
          this.url.value + '/layout/scheduler/pic2latex/query?docID=' + docID,
        // this.url.gray + '/layout/scheduler/pic2latex/query?docID=' + docID + '&getExtend=' + this.getExtend,
        handleProgress: this.progressPass()
      }).then((res) => {
        if (res === undefined) {
          console.log('undefined res')
          this.status = -1
          return Promise.resolve(-1)
        }
        // 多页中有一项错误，不能算作整体状态码，需要去内部看错误信息
        if (res.data.status === 4) {
          console.log('错误信息:', res.data.pages_err[0].msg)
          this.$message({
            showClose: true,
            message: '文件处理失败! ' + res.data.pages_err[0].msg,
            center: true,
            duration: 4000,
            type: 'info'
          })
          this.status = 0
          return Promise.resolve(0)
        }

        // 处理完成得到string类型的公式
        console.log('处理完成，响应码为: ' + JSON.stringify(res.code))

        // 没有检测到公式
        if (res.data.success[0][0] === undefined) {
          this.$message({
            showClose: true,
            message: '没有检测到公式',
            center: true,
            duration: 4000,
            type: 'info'
          })
          return Promise.resolve(1)
        }

        // 走原图逻辑，不拿矫正后的图片，算法会返回原图坐标框ori_box
        // 判断是否返回矫正后的图片
        // if (res.data.origin_change_images[0] !== '') {
        //   this.backupUrl = (res.data.origin_change_images[0]).replace(/-internal/g, '')
        // }

        // 服务耗时
        this.resultTime.push(
          {
            value: res.data.duration / 1000,
            comment: '接口返回的耗时：'
          })

        // 公式渲染
        var result_latex = []
        var result_inlineLatex = []
        var result_displayedLatex = []
        // var result_text = []
        // block有多个
        var blocks = res.data.success[0]
        for (const b in blocks) {
          // sentences可能有多个
          var block = blocks[b]
          block.sentences.forEach(item => {
            // 判断是否为公式4/9, 手写为5
            if (item.type === 4 || item.type === 9 || item.type === 5) {
              // 获取公式内容, 优先获取latex字段
              if (item.latex !== undefined) {
                result_latex.push(item.latex)
              } else {
                result_latex.push(item.text)
              }
              // 获取公式框坐标
              const rectangle = this.getRectangle(item.points)
              // 保存原始矩形框的坐标值
              this.originalRectanglesLatex.push(rectangle)
              // 根据缩放比例调整矩形框的坐标值
              const scaledRectangle = this.getScaledRectangle(item.points)
              this.rectanglesLatex.push(scaledRectangle)
              // 获取行内公式
              if (item.type === 9) {
                // 获取公式内容, 优先获取latex字段
                if (item.latex !== undefined) {
                  result_inlineLatex.push(item.latex)
                } else {
                  result_inlineLatex.push(item.text)
                }
                this.originalInlineRectanglesLatex.push(rectangle)
                this.inlineRectanglesLatex.push(scaledRectangle)
              } else if (item.type === 4) {
                // 获取独立公式
                if (item.latex !== undefined) {
                  result_displayedLatex.push(item.latex)
                } else {
                  result_displayedLatex.push(item.text)
                }
                this.originalDisplayedRectanglesLatex.push(rectangle)
                this.displayedRectanglesLatex.push(scaledRectangle)
              }
            } else if (item.type === 0) {
              // 获取文本内容
              // result_text.push(item.ocr)
              // 获取文本框坐标
              const rectangle = this.getRectangle(item.points)
              // 保存原始矩形框的坐标值
              this.originalRectanglesText.push(rectangle)
              // 根据缩放比例调整矩形框的坐标值
              const scaledRectangle = this.getScaledRectangle(item.points)
              this.rectanglesText.push(scaledRectangle)
            }
            // type = 1为手写类，暂不显示
          })
        }

        // 公式原文
        this.originData = result_latex
        this.originAllData = result_latex
        this.originDisplayedFormula = result_displayedLatex
        this.originInlineFormula = result_inlineLatex

        // 设置块级公式居中
        this.formulas = result_latex.map(item => {
          return {
            'latex': `$$${item}$$`
          }
        })
        // 临时方案：重新触发绘制公式框
        this.zoomOut()
        // // 绘制框
        // this.drawRectangles(this.backupUrl)

        // 显示结果
        this.showResult = true
        // 只显示文本框则无需显示公式选择按钮
        if (this.choiceRectangles === 'Text') {
          this.showFormula = false
        }
        this.status = 1

        return Promise.resolve(1)
      })
    },
    // 根据缩放比例调整矩形框的坐标值
    getScaledRectangle(points) {
      // const scaledRectangle = {
      //   x: rectangle.x * this.scale,
      //   y: rectangle.y * this.scale,
      //   width: rectangle.width * this.scale,
      //   height: rectangle.height * this.scale
      // }
      const scaledRectangle = {
        x1: points[0][0] * this.scale,
        y1: points[0][1] * this.scale,
        x2: points[1][0] * this.scale,
        y2: points[1][1] * this.scale,
        x3: points[2][0] * this.scale,
        y3: points[2][1] * this.scale,
        x4: points[3][0] * this.scale,
        y4: points[3][1] * this.scale
      }
      return scaledRectangle
    },
    // 获取矩形框坐标
    getRectangle(points) {
      // const rectangle = {
      //   x: item.points[0][0],
      //   y: item.points[0][1],
      //   width: item.points[1][0] - item.points[0][0],
      //   height: item.points[2][1] - item.points[0][1]
      // }
      const rectangle = {
        x1: points[0][0],
        y1: points[0][1],
        x2: points[1][0],
        y2: points[1][1],
        x3: points[2][0],
        y3: points[2][1],
        x4: points[3][0],
        y4: points[3][1]
      }
      return rectangle
    },
    choiceFormulaType(choice) {
      if (choice === 'displayedFormula') {
        this.choiceFormula = this.displayedRectanglesLatex
        this.showData(this.originDisplayedFormula, this.displayedRectanglesLatex)
      } else if (choice === 'inlineFormula') {
        this.choiceFormula = this.inlineRectanglesLatex
        this.showData(this.originInlineFormula, this.inlineRectanglesLatex)
      } else {
        this.choiceFormula = this.rectanglesLatex
        this.showData(this.originAllData, this.rectanglesLatex)
      }
    },
    showData(result_latex, rectanglesLatex) {
      // 设置公式原文
      this.originData = result_latex
      console.log('[showData] this.originData:', this.originData)
      // 设置块级公式居中
      this.formulas = []
      this.formulas = result_latex.map(item => {
        return {
          'latex': `$$${item}$$`
        }
      })
      // console.log('[showData] this.formulas:', this.formulas)
      // 绘制框
      this.drawRectangles(this.backupUrl, rectanglesLatex, this.fontSize)
    },
    // 使用Image对象来检查链接是否可以加载为图片,利用浏览器的图片预加载机制
    checkImageLinkAvailability(url) {
      return new Promise((resolve, reject) => {
        if (url.trim() === '') {
          reject()
        }
        const img = new Image()
        img.onload = function() {
          resolve()
        }
        img.onerror = function() {
          reject()
        }
        img.src = url
      })
    },
    // 清空上传列表
    clearUpload() {
      this.$refs.upload.clearFiles()
      this.fileList = []
    },
    // 上传到服务器
    async submitUpload(param) {
      this.resetResult()
      this.fileList.push(param.file)
      // 只取列表中的最后一个值
      this.fileList = this.fileList.slice(-this.maxFileNum)
      // this.showResult = false
      this.loading = true
      var urls = []
      const s_time = new Date().getTime()
      for (var element of this.fileList) {
        this.fileName = element.name
        const form = new FormData()
        form.append('file', element)
        form.append('origin', 'pdf-cv-demo')
        const axios_config_upload = {
          method: 'post',
          headers: { 'Cache-Control': 'no-cache', ...getHeaders() },
          url: this.url.value + '/layout/scheduler/samplecollect/upload',
          data: form,
          responseType: 'json',
          processData: false,
          contentType: false
        }
        // 上传
        await axios(axios_config_upload).then(res => {
          const url = res.data.data.url
          this.beforeCreate(url)
          urls.push(url)
        })
      }
      // 请求
      // const status = await this.formulaextract(urls)
      // create请求
      const create_docID = await this.formulaCreate(urls)
      if (create_docID !== undefined) {
        // 等待1s
        await sleep(1000)
        // query请求
        await this.waitDocStatus(create_docID)
      }
      this.loading = false
      // return Promise.resolve()
      const e_time = new Date().getTime()
      this.statistic = {
        status: this.status,
        duration: this.result.duration || (e_time - s_time) / 1000
      }
      return Promise.resolve()
    },
    // 相应的请求体设置
    formulaCreate(urls) {
      // 对传入的url进行处理
      // let urlString = urls[0].toString()
      // urlString = urlString.replace(/-internal/g, '')
      const urlString = urls[0].toString()
      var data_dict = {}
      data_dict = {
        'imgs': [{
          'index': 0,
          'url': urlString,
          'imgType': 'png',
          'dpi': 144
        }],
        'params': {
          'get_coord': true,
          'get_paragraph': true,
          'get_formula': this.form.get_formula
        }
      }
      return new Promise((resolve, reject) => {
        axios({
          method: 'post',
          headers: { 'Cache-Control': 'no-cache', 'Content-Type': 'application/json', ...getHeaders() },
          // 公式服务
          // url: this.url.gray + '/layout/scheduler/pic2latex/create',
          url: this.url.value + '/layout/scheduler/pic2latex/create',
          data: data_dict,
          responseType: 'json',
          processData: false,
          contentType: false,
          timeout: 80000 // 设置超时时间为80秒
        }).then(res => {
          if (res.data.code !== 200 || res.data.data === undefined || res.data.data.docID === undefined) {
            return Promise.reject('转化请求失败!')
          }
          // 处理完成得到string类型的公式
          console.log('create完成: ' + JSON.stringify(res.data.data.docID))
          resolve(res.data.data.docID)
        }).catch((error) => {
          this.$message({
            showClose: true,
            message: '文件处理失败! ' + error,
            center: true,
            duration: 4000,
            type: 'error'
          })
          // return Promise.resolve(0)
          reject(error)
        })
      })
    },
    // 示例图片
    async tableextractDemo(arrayid, id) {
      // this.showResult = false
      this.resetResult()
      // 开始加载
      this.loading = true

      const file = this.urlFiles[parseInt(arrayid)][parseInt(id)]

      this.fileObj = { name: file.name, size: file.size }
      this.fileName = file.name

      const s_time = new Date().getTime()
      console.log('file.url: ', [file.url])
      // const status = await this.formulaextract([file.url])
      this.beforeCreate(file.url)
      // create请求
      const create_docID = await this.formulaCreate([file.url])
      if (create_docID !== undefined) {
        // 等待1s
        await sleep(1000)
        // query请求
        await this.waitDocStatus(create_docID)
      }
      // 结束加载
      this.loading = false
      const e_time = new Date().getTime()
      this.statistic = {
        status: this.status,
        duration: this.result.duration || (e_time - s_time) / 1000
      }
      return Promise.resolve()
    },
    handleRemove(file, fileList) {
      this.fileList = fileList
    },
    // 将用户选择的文件添加到上传列表
    tableExtractUpload2(param) {
      this.fileList.push(param.file)
    },
    // 相应的请求体设置
    formulaextract(urls) {
      // 对传入的url进行处理
      let urlString = urls[0].toString()
      urlString = urlString.replace(/-internal/g, '')
      var data_dict = {}
      data_dict = {
        'img_url': urlString,
        'choice_model': this.choiceModel
      }
      return axios({
        method: 'post',
        headers: { 'Cache-Control': 'no-cache', 'Content-Type': 'application/json', ...getHeaders() },
        // 公式服务
        url: this.url.value + '/layout/formula/formula',
        data: data_dict,
        responseType: 'json',
        processData: false,
        contentType: false,
        timeout: 80000 // 设置超时时间为80秒
      }).then(res => {
        if (res.data.code !== 200 || res.data.data === undefined || res.data.data.result === undefined) {
          return Promise.reject('转化请求失败!')
        }
        // 公式框为空
        if (res.data.data.warn !== undefined) {
          return Promise.reject('图片没有检测到公式!')
        }

        // 处理完成得到string类型的公式
        console.log('文件处理完成: ' + JSON.stringify(res.data.data))

        var result_latex = []
        var tempUrl
        // 公式
        for (let i = 0; i < res.data.data.result.length; i++) {
          tempUrl = res.data.data.result[i].latex
          result_latex.push(tempUrl)
        }
        // 检测框图
        if (res.data.data.draw_image_url !== undefined) {
          this.drawImageUrl = res.data.data.draw_image_url
          this.drawImageUrl = this.drawImageUrl.replace(/-internal/g, '')
        }
        // 服务耗时
        if (res.data.data.t_total !== undefined) {
          var properties = [
            { comment: '服务耗时:', name: 't_total' },
            { comment: 'classifier:', name: 't_classifier' },
            { comment: 'layout:', name: 't_layout' },
            { comment: 'formula:', name: 't_formula' },
            { comment: 'predict:', name: 't_formula_predict' },
            { comment: 'ocr:', name: 't_ocr' }
          ]

          properties.forEach(property => {
            if (res.data.data[property.name] !== undefined) {
              this.resultTime.push({
                value: res.data.data[property.name].toFixed(3),
                comment: property.comment
              })
            }
          })
        }

        this.originData = result_latex
        // 设置块级公式居中
        this.formulas = result_latex.map(item => {
          return {
            'latex': `$$${item}$$`
          }
        })
        // 显示结果
        this.showResult = true
      }).catch((error) => {
        this.$message({
          showClose: true,
          message: '文件处理失败! ' + error,
          center: true,
          duration: 4000,
          type: 'error'
        })
        return Promise.resolve(0)
      })
    }
  }
}
</script>

<style lang="scss">
// scoped style donot apply to v-html
// choose global style or deep selector
.extracted-table-container{
  overflow-x: auto;
  width: 100%;
  padding-top: 2%;
  padding-bottom: 2%;
  border-bottom: 1px solid #d9d9d9;
  table{
    // colspan-and-table-layoutfixed-break-the-table-style
    table-layout: auto;
    width: fit-content;
    margin: auto;
  }
}
</style>
<style lang="scss" scoped>
@import "~@/styles/mixin.scss";

// 设置滚动条
.formula-row {
  justify-content: center;
  // width: 100%; /* 设置父元素的宽度为100% */
  width: 40%; /* 设置父元素的宽度为100% */
  height: 1000px; /* 设置父元素的固定高度 */
  overflow: auto; /* 使用滚动条展示超过高度的内容 */
}
// 设置公式识别结果展示的样式
.result {
  display: flex;
  justify-content: center;
  // align-items: center;
  align-items: flex-start;
  //height: 100vh; /* 设置高度，使容器占据整个视口 */
}
.result-row {
  justify-content: center;
}

.result-button {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 对话框底部按钮区域 */
.dialog-footer {
  display: flex;
  justify-content: space-between;
}
.reset-button {
  order: 1;
}
.right-buttons {
  order: 2;
}
.container {
  padding-bottom: 2%;
  padding-left: 8%;
  padding-right: 8%;
}
.el-row {
  margin-bottom: 20px;
  &:last-child {
    margin-bottom: 0;
  }
}
.el-col {
  border-radius: 8px;
  border: 1px dashed #d9d9d9;
  background-color: #ffffff;
  color: #666666;
  padding: 1%;
}
.grid-content {
  border-radius: 8px;
  vertical-align: middle;
}

.upload-container {
  width: 100%;
  padding: 1%;
  position: relative;
  @include clearfix;
  .params-container {
    width: 50%;
    display: inline-block;
    padding-left: 1%;
    padding-right: 1%;
  }
  .params {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }
  .time-params {
    align-items: center;
    justify-content: space-between;
  }
  .param-label {
    width: 80%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-bottom: 2%;
  }
  .param-input {
    width: 50%;
    text-align: right;
    margin-bottom: 2%;
  }
}

.download-doc-btn{
    text-align: center;
    width: 100%;
    height: 4vh;
    font-size: 2vh;
    line-height: 4vh;
    color: #fff;
    background-color: #338ef0;
    border: none;
    border-radius: 4px;
}

.download-doc-btn a{
    text-decoration: none;
    color: #fff;
}

.canvas-container {
  position: relative;
  // width: 100%; /* 设置父元素的宽度为100% */
  width: 600px;
  height: 1000px; /* 设置父元素的固定高度 */
  overflow: auto; /* 使用滚动条展示超过高度的内容 */
}

.canvas-container canvas {
  position: absolute;
}

</style>
