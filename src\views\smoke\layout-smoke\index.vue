
<template>
  <BasicLayout>
    <template #wrapper>
      <el-card class="box-card">
        <el-form
          ref="queryForm"
          :model="queryParams"
          :inline="true"
          label-width="68px"
        >
          <el-form-item
            label="任务"
            prop="title"
          ><el-input
            v-model="queryParams.title"
            placeholder="请输入任务"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item
            label="脚本"
            prop="cmd"
          ><el-input
            v-model="queryParams.cmd"
            placeholder="请输入脚本"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item
            label="状态"
            prop="status"
          ><el-input
            v-model="queryParams.status"
            placeholder="请输入状态"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
            >搜索</el-button>
            <el-button
              icon="el-icon-refresh"
              size="mini"
              @click="resetQuery"
            >重置</el-button>
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              v-permisaction="['smoke:task:add']"
              type="primary"
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
            >新增
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              v-permisaction="['smoke:task:edit']"
              type="success"
              icon="el-icon-edit"
              size="mini"
              :disabled="single"
              @click="handleUpdate"
            >修改
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              v-permisaction="['smoke:task:remove']"
              type="danger"
              icon="el-icon-delete"
              size="mini"
              :disabled="multiple"
              @click="handleDelete"
            >删除
            </el-button>
          </el-col>
        </el-row>

        <el-table
          v-loading="loading"
          :data="taskList"
          @selection-change="handleSelectionChange"
        >
          <el-table-column
            type="selection"
            width="55"
            align="center"
          /><el-table-column
            label="任务"
            align="center"
            prop="title"
            :show-overflow-tooltip="true"
          /><el-table-column
            v-if="false"
            label="脚本"
            align="center"
            prop="cmd"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="脚本描述"
            align="center"
            prop="cmd2"
            :show-overflow-tooltip="true"
          ><template slot-scope="scope">
            <span v-html="converCmd(scope.row.cmd) " />
          </template>
          </el-table-column>
          <el-table-column
            v-if="false"
            label="参数配置"
            align="center"
            prop="config"
            :show-overflow-tooltip="true"
          />
          <el-table-column

            label="参数描述"
            align="center"
            prop="config2"
            :show-overflow-tooltip="true"
          ><template slot-scope="scope">
            <span v-html="converConfig(scope.row.config) " />
          </template>
          </el-table-column>

          <el-table-column label="状态" width="80" sortable="custom">
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.status"
                :active-value="1"
                :inactive-value="0"
                @change="handleStatusChange(scope.row)"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="备注"
            align="center"
            prop="remark"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="执行者"
            align="center"
            prop="executor"
            :show-overflow-tooltip="true"
          ><template slot-scope="scope">
            <span v-html="converNotice(scope.row.executor) " />
          </template>
          </el-table-column>
          <el-table-column
            label="执行时间"
            align="center"
            prop="exectime"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span>{{ convertLongtime(scope.row.exectime) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-popconfirm
                class="delete-popconfirm"
                title="确认要修改吗?"
                confirm-button-text="修改"
                @onConfirm="handleUpdate(scope.row)"
              >
                <el-button
                  slot="reference"
                  v-permisaction="['smoke:task:edit']"
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                >修改
                </el-button>
              </el-popconfirm>
              <el-popconfirm
                class="delete-popconfirm"
                title="确认要删除吗?"
                confirm-button-text="删除"
                @onConfirm="handleDelete(scope.row)"
              >
                <el-button
                  slot="reference"
                  v-permisaction="['smoke:task:remove']"
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                >删除
                </el-button>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageIndex"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />

        <!-- 添加或修改对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="500px">
          <el-form ref="form" :model="form" :rules="rules" label-width="80px">
            <el-form-item label="任务" prop="title">
              <el-input v-model="form.title" placeholder="任务" />
            </el-form-item>
            <el-form-item label="脚本" prop="cmd">
              <el-input v-model="form.cmd" placeholder="脚本" :disabled="configEdit" />
            </el-form-item>
            <el-form-item label="脚本名称" prop="cmd2">
              <el-select v-model="cmd2" placeholder="" clearable size="small" filterable="">
                <el-option

                  v-for="dict in cmdOptions"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select><span style="color:red">*支持模糊搜索</span>
            </el-form-item>
            <el-form-item label="执行时间">
              <el-date-picker
                v-model="form.exectime"
                size="small"
                type="datetime"

                align="right"
                value-format="timestamp"
              /> </el-form-item>
            <el-form-item label="配置" prop="config">
              <el-input v-model="form.config" placeholder="" type="textarea" :disabled="configEdit" rows="12" />

            </el-form-item>
            <el-form-item label="配置名称" prop="config2">
              <el-select v-model="config2" placeholder="配置名称" clearable size="small" multiple filterable="">
                <el-option

                  v-for="dict in configOptions"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select><span style="color:red">*支持模糊搜索</span>
            </el-form-item>

            <!-- <el-form-item label="执行者" prop="config">
              <el-input v-model="form.executor" placeholder="执行者" />
            </el-form-item> -->

            <el-form-item label="执行者" prop="executor">
              <el-select v-model="form.executor" placeholder="" clearable size="small">
                <el-option

                  v-for="dict in noticeOptions"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" placeholder="备注" type="textarea" />
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
          </div>
        </el-dialog>
      </el-card>
    </template>
  </BasicLayout>
</template>

<script>
import {
  addLayoutSmoke,
  delLayoutSmoke,
  getLayoutSmoke,
  listLayoutSmoke,
  updateLayoutSmokeStatus,
  updateLayoutSmoke
} from '@/api/smoke/layout-smoke'

export default {
  name: 'LayoutSmoke',
  components: {},
  data() {
    return {
      sOptions: [
        { label: '选项1', value: '1' }, { label: '选项2', value: '2' }
      ],
      configOptions: [],
      cmdOptions: [],
      config2: [],
      cmd2: [],
      // :[],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      configEdit: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      isEdit: false,
      // 类型数据字典
      typeOptions: [],
      noticeOptions: [],
      taskList: [],

      // 关系表类型

      // 查询参数
      queryParams: {
        pageIndex: 1,
        pageSize: 10,
        title: undefined,
        cmd: undefined,
        status: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        title: [{ required: true, message: '任务不能为空', trigger: 'blur' }],
        status: [{ required: true, message: '状态不能为空', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.getDicts('pdf_smoke_config').then(response => {
      this.configOptions = response.data
    })
    this.getDicts('pdf_smoke_notice').then(response => {
      this.noticeOptions = response.data
    })
    this.getList()

    this.getDicts('pdf_smoke_cmd').then(response => {
      this.cmdOptions = response.data
    })
  },
  methods: {
    setCellStyle() {
      return 'white-space: pre-wrap;'
    },
    converCmd(item) {
      for (var i in this.cmdOptions) {
        if (this.cmdOptions[i].value === item) {
          return this.cmdOptions[i].label
        }
      }
    },
    converNotice(item) {
      for (var i in this.noticeOptions) {
        if (this.noticeOptions[i].value === item) {
          return this.noticeOptions[i].label
        }
      }
    },
    converConfig(item) {
      var arrItem = item.split(';')
      var result = []
      for (var i in this.configOptions) {
        for (var k in arrItem) {
          if (this.configOptions[i].value === arrItem[k]) {
            result.push(this.configOptions[i].label)
            // this.taskList[2].config2=this.configOptions[i].label
          }
        }
      }
      return result.join('</br>')
    },
    convertLongtime(time) {
      if (time === 0) { return '' }
      var date = new Date(time * 1000)
      var DD = String(date.getDate()).padStart(2, '0') // 获取日
      var MM = String(date.getMonth() + 1).padStart(2, '0') // 获取月份，1 月为 0
      var yyyy = date.getFullYear() // 获取年
      var hh = String(date.getHours()).padStart(2, '0') // 获取当前小时数(0-23)
      var mm = String(date.getMinutes()).padStart(2, '0') // 获取当前分钟数(0-59)
      var ss = String(date.getSeconds()).padStart(2, '0') // 获取当前秒数(0-59)
      var today = yyyy + '-' + MM + '-' + DD + ' ' + hh + ':' + mm + ':' + ss
      return today
    },
    /** 查询参数列表 */
    getList() {
      this.loading = true
      listLayoutSmoke(this.addDateRange(this.queryParams, this.dateRange)).then(
        (response) => {
          this.taskList = response.data.list
          this.total = response.data.count
          this.loading = false
        }
      )
    },
    handleStatusChange(row) {
      const text = row.status === 1 ? '启用' : '停用'
      this.$confirm('确认要"' + text + '""' + row.title + '"冒烟任务吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function() {
        return updateLayoutSmokeStatus(row)
      }).then(() => {
        this.msgSuccess(text + '成功')
      }).catch(function() {
        row.status = row.status === 1 ? 0 : 1
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        title: undefined,
        cmd: undefined,
        config: undefined,
        remark: undefined,
        status: undefined
      }
      this.cmd2 = undefined
      this.config2 = undefined
      this.resetForm('form')
    },
    getImgList: function() {
      this.form[this.fileIndex] =
        this.$refs['fileChoose'].resultList[0].fullUrl
    },
    fileClose: function() {
      this.fileOpen = false
    },
    // 关系
    // 文件
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageIndex = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加LayoutSmoke'
      this.isEdit = false
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getLayoutSmoke(id).then((response) => {
        this.form = response.data
        // this.form.id= response.data.id
        this.config2 = response.data.config.split(';')
        this.cmd2 = response.data.cmd
        this.form.config = this.form.config.replaceAll(';', '\n')
        this.open = true
        this.title = '修改LayoutSmoke'
        this.isEdit = true
        this.form.exectime = this.form.exectime * 1000
      })
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          if (this.form.id !== undefined) {
            this.form.config = this.config2.join(';')
            this.form.cmd = this.cmd2
            updateLayoutSmoke(this.form).then((response) => {
              if (response.code === 200) {
                this.msgSuccess(response.msg)
                this.open = false
                this.getList()
              } else {
                this.msgError(response.msg)
              }
            })
          } else {
            this.form.config = this.config2.join(';')
            this.form.cmd = this.cmd2
            addLayoutSmoke(this.form).then((response) => {
              if (response.code === 200) {
                this.msgSuccess(response.msg)
                this.open = false
                this.getList()
              } else {
                this.msgError(response.msg)
              }
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      var Ids = (row.id && [row.id]) || this.ids

      this.$confirm('是否确认删除编号为"' + Ids + '"的数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(function() {
          return delLayoutSmoke({ ids: Ids })
        })
        .then((response) => {
          if (response.code === 200) {
            this.msgSuccess(response.msg)
            this.open = false
            this.getList()
          } else {
            this.msgError(response.msg)
          }
        })
        .catch(function() {})
    }
  }
}
</script>
