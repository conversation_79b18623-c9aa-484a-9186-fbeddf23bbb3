<template>
  <!-- 创建一个卡片式的容器，使用主题内容区域将背景色统一 -->
  <el-card>
    <!-- 标题插槽：显示使用说明 -->
    <!-- <div slot="header">
      <CVHint>使用说明：（表格提取）选择一张或多张图片或一个pdf文件，系统会对文件进行处理，输出格式html会在页面展示结果，若输出格式xlsx则会输出对应文档</CVHint>
    </div> -->

    <!-- 主体内容 -->
    <div class="card-content">
      <div>
        <div class="container">
          <el-row type="flex" justify="center" :gutter="20">
            <!-- 将不同版本的提示语挪为单独一列 -->
            <el-col :span="4" class="grid-content">
              <div class="params-container" style="width: 100% !important">
                <div class="params">
                  <span style="color:blue"> {{ "使用手册：" }}</span>
                </div><br>
                <div class="params">
                  <span v-show="remark === ''" style="color:darkblue"> {{ "支持上传图片/PDF文件。" }}</span><br>
                </div>
              </div>
            </el-col>

            <el-col :span="8" class="grid-content">
              <span style="color:blue"> {{ "参数设置：" }}</span>
              <div class="upload-container">

                <div class="params-container" style="width: 100% !important">

                  <div class="params">
                    <el-button
                      type="warning"
                      size="mini"
                      @click="handleSetting"
                    >表单设置</el-button>
                    <!-- <el-tooltip content="切换输入方式" placement="right-end">
                      <el-select v-model.number="choiceInput" placeholder="默认上传文件">
                        <el-option
                          v-for="dict in choiceInputOptions"
                          :key="dict.value"
                          :label="dict.label"
                          :value="dict.value"
                        />
                      </el-select>
                    </el-tooltip> -->
                  </div>

                  <hr>
                  <!-- 转换或点击上传前高级设置对话框 -->
                  <el-dialog :title="title" :visible.sync="open" width="600px">
                    <el-form ref="form" :model="form" :rules="rules" label-width="120px" inline-message>

                      <el-row>
                        <!-- <el-col :span="24">
                          <el-form-item label="文件类型:" prop="SelectFileType">
                            <div>
                              <el-radio-group v-model="form.SelectFileType" placeholder="请选择">
                                <el-radio v-for="dict in SelectFileTypeOptions" :key="dict.value" :label="dict.value">
                                  {{ dict.label }}
                                </el-radio>
                              </el-radio-group>
                            </div>
                          </el-form-item>
                        </el-col> -->

                        <el-col :span="16">
                          <!-- <el-form-item label="选择标签类别:" prop="label">
                            <el-select v-model.number="form.label" placeholder="请选择">
                              <el-option
                                v-for="dict in labelList"
                                :key="dict.value"
                                :label="dict.LabelName"
                                :value="dict.LabelId"
                              />
                            </el-select>
                          </el-form-item> -->
                          <el-form-item label="选择标签类别:" prop="label">
                            <el-select v-model="form.labels" placeholder="请选择" multiple @change="$forceUpdate()">
                              <el-option
                                v-for="dict in labelList"
                                :key="dict.value"
                                :label="dict.LabelName"
                                :value="dict.LabelId"
                              />
                            </el-select>
                          </el-form-item>
                        </el-col>
                        <el-col :span="8">
                          <el-button
                            size="medium"
                            type="text"
                            @click="handleAddLabel()"
                          >新增标签</el-button>
                        </el-col>

                        <el-col :span="24">
                          <el-form-item label="备注:" prop="remark">
                            <el-input v-model="form.remark" placeholder="填写需要补充的信息" type="textarea" />
                          </el-form-item>
                        </el-col>
                      </el-row>
                    </el-form>

                    <div slot="footer" class="dialog-footer">
                      <el-button class="reset-button" type="warning" @click="reset">重 置</el-button>
                      <div class="right-buttons">
                        <el-button class="confirm-button" type="primary" @click="submitForm">确 定</el-button>
                        <el-button class="cancel-button" @click="cancel">取 消</el-button>
                      </div>
                    </div>
                  </el-dialog>

                  <!-- 新建标签表单 -->
                  <el-dialog :title="titleLabel" :visible.sync="openLabel" width="600px">
                    <el-form ref="formLabel" :model="formLabel" label-width="120px" inline-message>
                      <el-row>
                        <el-col :span="24">
                          <el-form-item label="标签名:" prop="label_name">
                            <el-input v-model="formLabel.label_name" placeholder="填写需要创建的标签名" type="textarea" />
                          </el-form-item>
                        </el-col>
                      </el-row>
                    </el-form>

                    <div slot="footer" class="dialog-footer">
                      <el-button class="reset-button" type="warning" @click="resetLabel">重 置</el-button>
                      <div class="right-buttons">
                        <el-button class="confirm-button" type="primary" @click="addLabel">创 建</el-button>
                      </div>
                    </div>
                  </el-dialog>
                </div>

                <div v-if="choiceInput=='uploadFile'">
                  <br><span style="color:blue"> {{ "上传图片/pdf文件：" }}</span>
                  <!-- 支持指定文件类型 -->
                  <!-- :accept="fileType.join(',')" -->
                  <el-upload
                    ref="upload"
                    v-loading="loading"
                    element-loading-text="文件处理中"
                    class="uploader"
                    list-type="text"
                    :accept="'*'"
                    :multiple="true"
                    :show-file-list="true"
                    :file-list="fileList"
                    :http-request="tableExtractUpload2"
                    :on-remove="handleRemove"
                    :on-change="handleChange"
                    :auto-upload="true"
                    action="placeholder"
                    drag
                  >

                    <i class="el-icon-upload" />
                    <div class="el-upload__text">
                      将文件拖到此处，或
                      <el-button type="text">点击上传</el-button>
                    </div>

                  </el-upload>
                  <br><div class="params">
                    <!-- <el-button size="small" type="primary" @click="submitUpload">开始上传</el-button> -->
                    <el-button size="small" type="primary" @click="prompt">开始上传</el-button>
                    <el-button size="small" type="success" @click="clearUpload">清空上传列表</el-button>
                  </div>
                </div>
              </div>

            </el-col>

            <el-col :span="12" class="grid-content">
              <div class="params-container" style="width: 100% !important">
                <div class="params">
                  <span style="color:blue"> {{ "已上传图片/PDF文件列表：" }}</span>
                  <el-table v-show="showResult" :data="resultList">
                    <el-table-column prop="count" label="" width="50" />
                    <el-table-column prop="sampleId" label="样张ID" width="80" />
                    <el-table-column prop="name" label="文件名" width="200" />
                    <el-table-column prop="value" label="objectKey / 错误信息" width="400" />
                  </el-table>
                  <span v-show="showFailedList" style="color:blue"> {{ "文件类型不支持上传的文件：" }}</span>
                  <el-table v-show="showFailedList" :data="failedList">
                    <el-table-column prop="name" label="文件名" width="200" />
                    <el-table-column prop="reason" label="原因" width="300" />
                  </el-table>
                  <span v-show="showRepeatedList" style="color:green"> {{ "已存在的文件：" }}</span>
                  <el-table v-show="showRepeatedList" ref="repeatedTable" v-loading="loading" :data="repeatedList" @selection-change="handleSelectionChange">
                    <el-table-column type="selection" width="55" align="center" />
                    <el-table-column prop="sampleId" label="样张ID" width="100" />
                    <el-table-column prop="name" label="文件名" width="200" />
                    <el-table-column prop="infos" label="msg" width="300" />
                  </el-table>
                </div><br>
              </div>
            </el-col>

            <!-- 修改样张信息对话框 -->
            <el-dialog :title="title" :visible.sync="openUpdateLabel" width="500px">
              <el-form ref="form" :model="form" :rules="rules" label-width="80px">
                <el-form-item label="标签类别:" prop="label">
                  <!-- <el-select v-model="form.labels" placeholder="请选择" multiple @change="$forceUpdate()"> -->
                  <el-select v-model="form.labels" placeholder="请选择" @change="$forceUpdate()">
                    <el-option
                      v-for="dict in labelNameOptions"
                      :key="dict.value"
                      :label="dict.LabelName"
                      :value="dict.LabelId"
                    />
                  </el-select>
                </el-form-item>
              </el-form>
              <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitLabel">确 定</el-button>
                <el-button @click="cancelLabel">取 消</el-button>
              </div>
            </el-dialog>

            <!-- 提示信息 -->
            <el-dialog title="提示" :visible.sync="openPrompt" width="500px">
              <el-form ref="form" :model="form" :rules="rules" label-width="80px">
                <el-form-item>
                  <span style="font-weight: bold; font-size: 16px;">请确保文件名做好清晰命名！</span><br>
                  <span style="font-weight: bold; font-size: 16px;">请确保用户敏感信息已去除！</span>
                </el-form-item>
              </el-form>
              <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="pass">确 定</el-button>
                <el-button @click="fail">取 消</el-button>
              </div>
            </el-dialog>

          </el-row>
        </div>

      </div>
    </div>

    <!-- 底部插槽：未使用，后续若是扩展可以考虑翻页等功能 -->
    <div slot="footer">
      Card Footer
    </div>
  </el-card>
</template>

<script>
import axios from 'axios'
// import CVHint from '@/views/cv/layout/components/CVHint'
// import CVDemo from '@/views/cv/layout/components/CVDemo'

import { urlRegex, formatfileSize } from '@/api/cv/utils'
import { getHeaders } from '@/utils/get-header'
// import { insertLabel } from '@/api/sample/label'
import { getToken } from '@/utils/auth'
import { updateSample } from '@/api/sample/sample'

export default {
  name: 'SampleUpload',
  //   components: { CVHint, CVDemo },
  props: {
    maxFileNum: {
      validator(value) {
        if (value <= 0) {
          return 1
        }
        return value
      },
      default: 1
    },
    fileType: {
      type: Array,
      default: function() {
        return ['.png', '.jpg', '.jpeg', '.pdf', '.zip']
      }
    }
  },
  data() {
    return {
      // 选择文件类型
      SelectFileTypeOptions: [
        { label: 'PDF', value: 'pdf' },
        { label: '图片', value: 'img' }
      ],
      // 输入方式字典
      choiceInputOptions: [
        { label: '上传图片/pdf文件(默认)', value: 'uploadFile' },
        { label: '输入图片链接', value: 'inputUrl' }
      ],
      choiceInput: 'uploadFile',
      // 弹出层标题（用户导入）
      title: '',
      // 是否显示弹出层（用户导入）
      open: false,
      // 表单参数
      form: {
        selectFileType: '',
        // label: '',
        labels: [],
        remark: ''
      },
      // 新建标签表单
      formLabel: {
        label_name: ''
      },
      titleLabel: '',
      openLabel: false,
      // 参数
      selectFileType: '',
      // label: '',
      labels: [],
      remark: '',
      image_width: 0,
      image_height: 0,
      // 表单校验
      rules: {
        // 定义规则对象
        // trigger: 'blur' 表示在失去焦点时触发验证
        originHeight: [{ type: 'number', message: 'originHeight必须为int类型', trigger: ['blur', 'change'] }],
        originWidth: [{ type: 'number', message: 'originWidth必须为int类型', trigger: ['blur', 'change'] }]
      },
      loading: false,
      url: {
        value: '//pcv-test.wps.cn',
        // value: '//localhost:8000',
        default: '//pcv-test.wps.cn'
      },
      fileUrl: '',
      // 上传列表
      fileList: [],
      // 支持获取宽度和高度的图片类型
      supportedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/jpg', 'image/webp'],
      // 存储图片宽高值
      imgInfos: [],
      showResult: false,
      resultList: [],
      // 存储不支持上传的文件
      failedList: [],
      showFailedList: false,
      // 存储已存在的文件
      repeatedList: [],
      showRepeatedList: false,
      // 选中数组
      ids: [],
      openUpdateLabel: false,
      showHandleUpdateLabel: false,
      labelNameOptions: [],
      // 上传文件总数
      // resultCount: 0,
      // 标签列表
      labelList: [],
      fileObj: {
        name: '',
        size: 0
      },
      versionremark: '',
      showNewVersion: false,
      versions: [],
      exporttypeOptions: ['html', 'xlsx'],
      // 是否打开提示框
      openPrompt: false
    }
  },
  computed: {
    formattedDuration() {
      return this.statistic.duration.toFixed(3) + 's'
    },
    fileName() {
      const { name } = this.fileObj
      return name.trim()
    },
    fileSize() {
      const { size } = this.fileObj
      return formatfileSize(size)
    }
  },
  // 通过watch监听输入值的变化，并将maxConNumPage/pdfDebugFlags由 string类型转为 int类型
  watch: {
    'form.pdfDebugFlags'(val) {
      this.pdfDebugFlagsInt = parseInt(val)
    }
  },
  created() {
    // this.getDicts('pdf_convert_version').then(response => {
    //   this.versions = response.data
    //   if (this.versions.length > 0) {
    //     this.url.value = this.versions[0].value
    //     this.startPage.version = this.versions[0].label
    //     this.remark = this.versions[0].remark
    //   }
    // })
  },
  methods: {
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.sampleId)
      this.handleUpdateLabel()
    },
    /** 批量修改样张标签 */
    handleUpdateLabel(row) {
      this.reset()
      this.showHandleUpdateLabel = true
      // 获取标签列表
      this.getLabelList()
      this.openUpdateLabel = true
      this.title = '批量修改标签'
    },
    /** 提交标签按钮 */
    submitLabel: function() {
      const newForm = new FormData()
      for (const sample_id of this.ids) {
        newForm.append('sample_id', sample_id)
      }
      // 多选框
      // for (const label of this.form.labels) {
      //   newForm.append('label_id', label)
      // }
      // 单选框
      newForm.append('label_id', this.form.labels)
      newForm.append('update_type', 'upload')
      updateSample(newForm).then(response => {
        if (response.code === 200) {
          this.msgSuccess(response.msg)
          this.$refs.repeatedTable.clearSelection()
          this.openUpdateLabel = false
          // this.getList()
        } else {
          this.msgError(response.msg)
          this.$refs.repeatedTable.clearSelection()
          this.openUpdateLabel = false
        }
      })
    },
    cancelLabel() {
      this.ids = []
      this.$refs.repeatedTable.clearSelection()
      this.form.labels = this.labels
      this.openUpdateLabel = false
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          // this.pdfDebugFlags = this.form.pdfDebugFlags
          this.selectFileType = this.form.selectFileType
          this.labels = this.form.labels
          this.remark = this.form.remark
          this.open = false
        }
      })
    },
    // 取消按钮
    cancel() {
      this.form.selectFileType = this.selectFileType
      this.form.labels = this.labels
      this.form.remark = this.remark
      this.open = false
      // this.reset()
    },
    /** 重置按钮 */
    // 表单重置
    reset() {
      this.form = {
        selectFileType: '',
        // label: '',
        label: [],
        remark: ''
      }
      this.resetForm('form')
    },
    // 新增标签表单重置
    resetLabel() {
      this.form = {
        label_name: ''
      }
      this.resetForm('formLabel')
    },
    /** 高级设置按钮操作 */
    handleSetting() {
      // this.reset()
      // 获取标签列表
      this.getLabelList()

      this.open = true
      this.title = '表单设置'
      this.form.password = ''
    },
    /** 获取标签列表 */
    async getLabelList() {
      // form.append('image_width', this.image_width)
      const axios_config_label = {
        method: 'get',
        headers: { 'Cache-Control': 'no-cache', ...getHeaders() },
        // url: this.url.value + '/api/api/v1/sample/upload',
        url: '/api/api/v1/sample/label',
        // data: form,
        responseType: 'json',
        processData: false,
        contentType: false
      }

      await axios(axios_config_label).then(res => {
        if (res.data.code !== 200) {
          this.message('error', '获取标签列表失败! ', res.data.msg)
        } else {
          this.labelList = res.data.data.list.labels
          this.labelNameOptions = res.data.data.list.labels
        }
      })
    },
    handleAddLabel() {
      this.openLabel = true
      this.titleLabel = '新增标签'
    },
    /** 新增标签 */
    async addLabel() {
      const form = new FormData()
      form.append('label_name', this.formLabel.label_name)
      const axios_config_insertlabel = {
        method: 'post',
        headers: { 'Cache-Control': 'no-cache', ...getHeaders(), 'Authorization': 'Bearer ' + getToken() },
        // url: this.url.value + '/api/api/v1/sample/upload',
        url: '/api/api/v1/sample/label',
        data: form,
        responseType: 'json',
        processData: false,
        contentType: false
      }
      await axios(axios_config_insertlabel).then(res => {
        if (res.data.code !== 200) {
          this.message('warning', '创建标签失败! ', res.data.msg)
        } else {
          this.openLabel = false
          this.message('success', '创建标签 ' + res.data.data.LabelName + ' 成功! 标签ID为: ', res.data.data.LabelId)
          // 刷新标签列表
          this.getLabelList()
        }
      })
      // insertLabel(this.formLabel.label_name).then(response => {
      //   console.log('response.data.LabelId:', response.data.LabelId)
      //   console.log('response.data.LabelName:', response.data.LabelName)
      //   // this.showLabelList = true
      // })
    },
    urlChange(value) {
      // url 检测
      if (urlRegex.test(value)) {
        this.url.value = value
      } else {
        this.url.value = this.url.default
      }
    },
    handleSelect(item) {
      this.remark = item.remark
    },
    fileChange(file, fileList) {
      this.fileList = fileList.slice(-this.maxFileNum)
    },
    onSuccess(response, file, fileList) {
      // hook
      this.fileChange(file, fileList)
    },
    resetResult() {
      this.resultList = []
      this.failedList = []
      this.repeatedList = []
      this.ids = []
      this.showResult = false
      this.showFailedList = false
      this.showRepeatedList = false
      this.open = false
      this.openLabel = false
      this.currentCount = 0
      // this.resultCount = 0
    },
    clearUpload() {
      this.$refs.upload.clearFiles()
      this.fileList = []
    },
    isImageOrPdfFileType(fileType) {
      const imageOrPdfRegex = /^image\/|^application\/pdf$/
      return imageOrPdfRegex.test(fileType)
    },
    isMaxFileSize(fileSize) {
      return fileSize > 104857600
    },
    buildParam(index, element) {
      // console.log('fileName:', element.name)
      // console.log('fileType:', element.type)
      const form = new FormData()
      form.append('file', element)
      // form.append('', this.form.selectFileType)
      if (this.form.labels !== undefined) {
        for (const label of this.form.labels) {
          form.append('label_id', label)
        }
      }
      // form.append('label_id', this.form.labels)
      form.append('remark', this.form.remark)
      form.append('initial_name', element.name)
      if (element.type.startsWith('image/')) {
        form.append('image_height', this.imgInfos[index].height)
        form.append('image_width', this.imgInfos[index].width)
      }
      return form
    },
    message(type, msg, dataMsg) {
      this.$message({
        showClose: true,
        message: msg + dataMsg,
        center: true,
        duration: 4000,
        type: type
      })
    },
    /** 上传样张前的提示 */
    prompt() {
      if (this.fileList.length > 0) {
        this.openPrompt = true
      } else {
        this.message('warning', '上传列表为空! ', '请先添加文件')
      }
    },
    pass() {
      this.openPrompt = false
      this.submitUpload()
    },
    fail() {
      this.openPrompt = false
    },
    /** 开始上传 */
    async submitUpload() {
      // const s_time = new Date().getTime()
      this.resetResult()
      this.loading = true
      var urls = []
      var counts = 0
      const totalCount = this.fileList.length
      // this.resultCount = this.fileList.length
      // for (var element of this.fileList) {
      for (const [index, element] of this.fileList.entries()) {
        // if (!this.isImageOrPdfFileType(element.type)) {
        //   this.message('error', element.name + ' 上传失败，不支持上传' + element.type + '类型的文件:', '！')
        //   this.failedList.push({ name: element.name, reason: '不支持上传' + element.type + '类型的文件。' })
        //   this.showFailedList = true
        //   continue
        // }
        if (this.isMaxFileSize(element.size)) {
          this.message('error', element.name + ' 上传失败，不支持上传超过100MB 的文件！该文件大小为：' + (element.size / 1048576).toFixed(2), 'MB。')
          this.failedList.push({ name: element.name, reason: '不支持上传超过100MB 的文件！该文件大小为：' + (element.size / 1048576).toFixed(2) + 'MB。' })
          this.showFailedList = true
          continue
        }
        const form = this.buildParam(index, element)
        const axios_config_upload = {
          method: 'post',
          headers: { 'Cache-Control': 'no-cache', ...getHeaders(), 'Authorization': 'Bearer ' + getToken() },
          // url: this.url.value + '/api/api/v1/sample/upload',
          url: '/api/api/v1/sample/upload',
          data: form,
          responseType: 'json',
          processData: false,
          contentType: false
          // config.headers['Authorization'] = 'Bearer ' + getToken()
        }

        await axios(axios_config_upload).then(res => {
          var url = ''
          var id = ''
          if (res.data.code !== 200 || res.data.data.url === '') {
            url = res.data.msg
            if (res.data.code === 500 && res.data.msg.includes('样张已存在')) {
              const regex = /样张ID为(\d+)/
              const match = res.data.msg.match(regex)
              if (match) {
                this.repeatedList.push({ name: element.name, sampleId: match[1], infos: res.data.msg })
                this.showRepeatedList = true
              }
            }
          } else {
            url = res.data.data.url
            id = res.data.data.sample_id
          }
          counts += 1
          this.resultList.push({
            name: element.name,
            // value: url.replace('http://zhai-datas.ks3-cn-beijing.ksyun.com/', ''),
            value: url.replace('http://zhai-datas.ks3-cn-beijing-internal.ksyun.com/', ''),
            sampleId: id,
            count: counts + '/' + totalCount })
          this.showResult = true
          urls.push(url)
        }).catch(error => {
          this.resultList.push({
            name: element.name,
            value: '请求超时或出现错误，错误信息：' + error.message,
            count: counts + '/' + totalCount })
          if (axios.isCancel(error)) {
            // 请求被取消
            this.msgError('Request canceled', error.message)
          } else if (error.code === 'ECONNABORTED') {
            // 请求超时
            this.msgError('Request timeout', error.message)
          } else {
            this.msgError(error)
          }
        })
      }

      this.fileList = []
      this.imgInfos = []
      this.loading = false
      // console.log('上传结果：', urls)

      return Promise.resolve()
    },
    handleRemove(file, fileList) {
      this.fileList = fileList
    },
    /** 钩子函数：获取图片宽高值 */
    handleChange(file, fileList) {
      console.log('fileType:', file.raw.type)
      // if (file.raw.type.startsWith('image/')) {
      if (this.supportedTypes.includes(file.raw.type)) {
        // console.log('Image size: ' + file.size + ' bytes')
        // 获取图片的宽高值
        const img = new Image()
        img.src = URL.createObjectURL(file.raw)
        img.onload = () => {
          this.imgInfos.push({
            height: img.height,
            width: img.width
          })
        }
      } else {
        // 占位
        this.imgInfos.push({
          height: 0,
          width: 0
        })
      }
    },
    tableExtractUpload2(param) {
      this.fileList.push(param.file)
    },
    // 相应的请求体设置
    tableextract(urls) {
    }
  }
}
</script>

<style lang="scss">
// scoped style donot apply to v-html
// choose global style or deep selector
.extracted-table-container{
  overflow-x: auto;
  width: 100%;
  padding-top: 2%;
  padding-bottom: 2%;
  border-bottom: 1px solid #d9d9d9;
  table{
    // colspan-and-table-layoutfixed-break-the-table-style
    table-layout: auto;
    width: fit-content;
    margin: auto;
  }
}
</style>
<style lang="scss" scoped>
@import "~@/styles/mixin.scss";

/* 对话框底部按钮区域 */
.dialog-footer {
  display: flex;
  justify-content: space-between;
}
.reset-button {
  order: 1;
}
.right-buttons {
  order: 2;
}
.container {
  padding-bottom: 2%;
  padding-left: 8%;
  padding-right: 8%;
}
.el-row {
  margin-bottom: 20px;
  &:last-child {
    margin-bottom: 0;
  }
}
.el-col {
  border-radius: 8px;
  border: 1px dashed #d9d9d9;
  background-color: #ffffff;
  color: #666666;
  padding: 1%;
}
.grid-content {
  border-radius: 8px;
  vertical-align: middle;
}

.upload-container {
  width: 100%;
  padding: 1%;
  position: relative;
  @include clearfix;
  .params-container {
    width: 50%;
    display: inline-block;
    padding-left: 1%;
    padding-right: 1%;
  }
  .params {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }
  .param-label {
    width: 50%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-bottom: 2%;
  }
  .param-input {
    width: 50%;
    text-align: right;
    margin-bottom: 2%;
  }
}

</style>
