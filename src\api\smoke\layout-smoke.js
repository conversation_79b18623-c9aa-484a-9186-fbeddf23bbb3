import request from '@/utils/request'

// 查询LayoutSmoke列表
export function listLayoutSmoke(query) {
  return request({
    url: '/api/v1/layout-smoke',
    method: 'get',
    params: query
  })
}

// 查询LayoutSmoke详细
export function getLayoutSmoke(id) {
  return request({
    url: '/api/v1/layout-smoke/' + id,
    method: 'get'
  })
}

// 新增LayoutSmoke
export function addLayoutSmoke(data) {
  return request({
    url: '/api/v1/layout-smoke',
    method: 'post',
    data: data
  })
}

// 修改LayoutSmoke
export function updateLayoutSmoke(data) {
  return request({
    url: '/api/v1/layout-smoke/' + data.id,
    method: 'put',
    data: data
  })
}

export function updateLayoutSmokeStatus(data) {
  return request({
    url: '/api/v1/layout-smoke/status/' + data.id,
    method: 'put',
    data: data
  })
}

// 删除LayoutSmoke
export function delLayoutSmoke(data) {
  return request({
    url: '/api/v1/layout-smoke',
    method: 'delete',
    data: data
  })
}

