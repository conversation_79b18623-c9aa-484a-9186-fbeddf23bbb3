import request from '@/utils/request'

// 查询PdfEditRequest列表
export function listPdfEditRequest(query) {
  return request({
    url: '/api/v1/request',
    method: 'get',
    params: query
  })
}

// 导出失败url
export function errExportPdfEditRequest() {
  return request({
    headers: { 'Cache-Control': 'no-cache' },
    url: '/api/v1/pdf-edit-request/errexport',
    method: 'get'
  })
}

// 查询PdfEditRequest详细
export function getPdfEditRequest(docid) {
  return request({
    url: '/api/v1/pdf-edit-request/' + docid,
    method: 'get'
  })
}

// 新增PdfEditRequest
export function addPdfEditRequest(data) {
  return request({
    url: '/api/v1/pdf-edit-request',
    method: 'post',
    data: data
  })
}

// 修改PdfEditRequest
export function updatePdfEditRequest(data) {
  return request({
    url: '/api/v1/pdf-edit-request/' + data.docid,
    method: 'put',
    data: data
  })
}

// 删除PdfEditRequest
export function delPdfEditRequest(data) {
  return request({
    url: '/api/v1/pdf-edit-request',
    method: 'delete',
    data: data
  })
}

// 下载pdf
export function downloadPdfEditRequest(data) {
  return request({
    url: '/api/v1/pdf-edit-request/download/3/6418a0c5-3368-419c-86aa-5d6399e034bb',
    method: 'get',
    responseType: 'blob'
    // data: data
  })
}

