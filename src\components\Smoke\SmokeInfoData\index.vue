<template>
  <div>
    <div>
      <!-- v-permisaction="['cvdata:All:viewparameter']" -->
      <el-button icon="el-icon-view" size="mini" @click="handleView">查看</el-button>
    </div>
    <el-dialog :visible.sync="viewOpen" :title="title" class="json-viewer-container" width="500px">
      <el-button icon="el-icon-check" size="mini" @click="handleCopy">复制</el-button>
      <json-viewer :value="jsonData" theme="dark" :expand-depth="300" />
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import axios from 'axios'
import { string } from 'clipboard'
export default {
  name: 'SmokeInfoData',
  components: {
  },
  props: {
    id: {
      type: string,
      default: ''
    },
    function: {
      type: string,
      default: ''
    }
  },
  data() {
    return {
      sOptions: [
        { label: 'json', value: 'json' },
        { label: 'list', value: 'list' }
      ],
      // 存储入参
      jsonData: {},
      // 开启参数详情弹窗
      viewOpen: false,
      // 弹出层标题
      title: '详情'
    }
  },
  created() {
  },
  methods: {
    /** 根据id查询详细信息 */
    handleSelectId() {
      let uri = ''
      const params = {
        'id': this.id
      }
      if (this.function === 'task') {
        uri = 'http://************:8001/data-processing/smoke/tasks'
      } else {
        uri = 'http://************:8001/data-processing/smoke/templates'
      }
      axios.get(uri, { params }).then((response) => {
        this.reset()
        if (response.data.code === 200) {
          this.jsonData = response.data.data
          this.viewOpen = true
        } else {
          this.message('error', '查询失败', response.msg)
        }
      }).catch((error) => {
        this.message('error', '查询失败', error)
      })
    },
    /** 查看入参 */
    handleView() {
      this.handleSelectId()
    },
    /** 复制入参 */
    handleCopy() {
      this.copyData(this.jsonData)
    },
    // 取消按钮
    cancel() {
      this.viewOpen = false
      this.reset()
    },
    // 重置
    reset() {
      this.jsonData = {}
    },
    copyData(data) {
      var data_value = JSON.stringify(data)
      var textArea = document.createElement('textarea')
      textArea.value = data_value
      document.body.appendChild(textArea)
      textArea.select()
      textArea.focus()
      document.execCommand('copy')
      document.body.removeChild(textArea)
      this.$message.success('复制成功')
    },
    message(type, msg, dataMsg) {
      this.$message({
        showClose: true,
        message: msg + dataMsg,
        center: true,
        duration: 4000,
        type: type
      })
    }
  }
}
</script>

  <style>
  .json-viewer-container {
      text-align: left;
  }
  </style>
