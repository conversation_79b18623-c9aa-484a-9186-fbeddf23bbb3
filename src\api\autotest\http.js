/* eslint-disable no-unused-vars */
import axios from 'axios'
import store from '@/store'
import { getToken } from '@/utils/auth'
import { Message, MessageBox } from 'element-ui'
import service from '@/utils/request'

export const axios_transform = axios.create({
  baseURL: process.env.VUE_APP_AUTOTEST_BASE_API,
  timeout: 10000
})

// http request 拦截器
axios_transform.interceptors.request.use(
  config => {
    if (store.getters.token) {
      // let each request carry token
      // ['X-Token'] is a custom headers key
      // please modify it according to the actual situation
      config.headers['Authorization'] = 'Bearer ' + getToken()
      config.headers['Content-Type'] = 'application/json'
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// http response 拦截器
axios_transform.interceptors.response.use(
  async response => {
    const status = response.data.status
    if (response.status !== 200) {
      // Notification.error({
      //   title: response.data.msg
      // })
      Message({
        message: response.data.msg,
        type: 'error'
      })
      return Promise.reject('error')
    } else {
      return response.data
    }
  },
  error => {
    if (error.message === 'Network Error') {
      Message({
        message: '服务器连接异常，请检查服务器！',
        type: 'error',
        duration: 5 * 1000
      })
      return
    }
    console.log('err' + error) // for debug

    Message({
      message: error.message,
      type: 'error',
      duration: 5 * 1000
    })

    return Promise.reject(error)
  }
)

function autotest_get(url, params = {}, responseType = 'json') {
  return new Promise((resolve, reject) => {
    axios_transform({
      method: 'get',
      url: url,
      params: params,
      responseType: responseType
    }).then((data) => {
      resolve(data)
    }).catch(err => {
      reject(err)
    })
  })
}

function autotest_post(url, data = {}, contentType = 'application/json') {
  return new Promise((resolve, reject) => {
    axios_transform({
      headers: {
        'Content-Type': contentType
      },
      method: 'post',
      url,
      data
    }).then((data) => {
      resolve(data)
    }).catch(err => {
      reject(err)
    })
  })
}

function autotest_delete(url, data = {}, contentType = 'application/json') {
  return new Promise((resolve, reject) => {
    axios_transform({
      headers: {
        'Content-Type': contentType
      },
      method: 'delete',
      url,
      data
    }).then((data) => {
      resolve(data)
    }).catch(err => {
      reject(err)
    })
  })
}

export { autotest_get, autotest_post, autotest_delete }

