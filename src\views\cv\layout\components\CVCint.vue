<template>
  <div class="cv-cint">
    <span v-html="formattedContent" />
  </div>
</template>

<script>
export default {
  name: 'CVCint',
  props: {
    hintContent: {
      type: Array,
      required: true
    }
  },
  data() {
    return {
      content: []
    }
  },
  computed: {
    formattedContent() {
      return this.hintContent.join('<br>')
    }
  },
  mounted() {
    this.content = this.hintContent
  }
}
</script>

<style scoped>

.cv-cint {
  text-align: left;
  color: rgb(14, 163, 201);
  font-size: 1.8vh;
  background-color: rgb(240, 254, 255);
  line-height: 5vh;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.cv-hint span {
  line-height: 1.2;
}

</style>
