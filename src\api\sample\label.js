import request from '@/utils/request'

// 查询标签列表
export function listLabels(query) {
  return request({
    url: '/api/v1/sample/label',
    method: 'get',
    params: query
  })
}

// 新增标签
export function insertLabel(form) {
  console.log('form:', form)
  return request({
    url: '/api/v1/sample/label',
    method: 'post',
    data: form
  })
}

// 修改标签信息
export function updateLabel(form) {
  return request({
    url: '/api/v1/sample/label',
    method: 'put',
    data: form
  })
}

// 删除标签信息
export function deleteLabel(form) {
  return request({
    url: '/api/v1/sample/label',
    method: 'delete',
    data: form
  })
}
