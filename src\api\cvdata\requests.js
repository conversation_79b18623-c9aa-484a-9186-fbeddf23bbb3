import request from '@/utils/request'

// 查询Requests列表
export function listRequests(query) {
  return request({
    url: '/api/v1/request',
    method: 'get',
    params: query
  })
}

// 查询Requests详细
export function getRequests(docid) {
  return request({
    url: '/api/v1/requests/' + docid,
    method: 'get'
  })
}

// 新增Requests
export function addRequests(data) {
  return request({
    url: '/api/v1/requests',
    method: 'post',
    data: data
  })
}

// 修改Requests
export function updateRequests(data) {
  return request({
    url: '/api/v1/requests/' + data.docid,
    method: 'put',
    data: data
  })
}

// 删除Requests
export function delRequests(data) {
  return request({
    url: '/api/v1/requests',
    method: 'delete',
    data: data
  })
}
