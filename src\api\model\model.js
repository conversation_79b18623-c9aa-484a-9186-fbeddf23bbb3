import request from '@/utils/request'



// 查询模型详细
export function getModel(modelId) {
  return request({
    url: '/api/v1/model/' + modelId,
    method: 'get'
  })
}
// 新增模型
export function addModel(data) {
  return request({
    url: '/api/v1/model',
    method: 'post',
    data: data
  })
}

// 修改模型
export function updateModel(data) {
  return request({
    url: '/api/v1/model',
    method: 'put',
    data: data
  })
}
// 修改模型
export function updateModelArchive(data) {
  return request({
    url: '/api/v1/model/archive',
    method: 'put',
    data: data,
    timeout: 15000, // 为这个特定的请求设置不同的超时时间
  })
}

// 删除模型
export function delModel(data) {
  return request({
    url: '/api/v1/model',
    method: 'delete',
    data: data
  })
}

// 下载模型
export function downloadModel(data) {
  return request({
    url: '/api/v1/model',
    method: 'post',
    data: data
  })
}

export function getModelList(query) {
  return request({
    url: '/api/v1/model',
    method: 'get',
    params: query
  })
}
export function getBase() {
  return request({
    url: '/api/v1/model/baseFloder',
    method: 'get',
  })
}