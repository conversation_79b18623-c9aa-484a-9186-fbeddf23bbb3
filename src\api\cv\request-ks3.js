import axios from 'axios'
import { getToken } from '@/utils/auth'
import store from '@/store'
import { MessageBox, Message } from 'element-ui'
export const axios_cv_ks = axios.create({})
import CryptoJS from 'crypto-js'
import Zlib from 'zlib'
import { getHeaders } from '@/utils/get-header'

// http request 拦截器
axios_cv_ks.interceptors.request.use(
  config => {
    // do something before request is sent
    if (store.getters.token) {
      // let each request carry token
      // ['X-Token'] is a custom headers key
      // please modify it according to the actual situation
      config.headers['Authorization'] = 'Bearer ' + getToken()
      config.headers['Content-Type'] = 'application/json'
    }
    return config
  },
  err => {
    return Promise.reject(err)
  }
)

// http response 拦截器
// axios_cv_ks.interceptors.response.use(
//   async response => {
//     return response
//   },
//   error => {
//     console.log('err' + error) // for debug
//     return Promise.reject(error)
//   }
// )

// response interceptor
axios_cv_ks.interceptors.response.use(
  /**
     * If you want to get http information such as headers or status
     * Please return  response => response
    */

  /**
     * Determine the request status by custom code
     * Here is just an example
     * You can also judge the status by HTTP Status Code
     */
  async response => {
    var code = response.data.code
    if (code === undefined) {
      code = response.status
    }
    if (code === 401) {
      if (response.data.msg !== undefined && response.data.msg === 'Token is expired') {
        // refresh_token
        const result = await store.dispatch('user/refreshToken'
        ).then(() => {
          console.log('refresh_token success')
          return axios_cv_ks(response.config)
        }).then(retryRes => {
          return retryRes
        }).catch(error => {
          console.log(error)
          return undefined
        })
        console.log('retry result,', result)
        if (result !== undefined) {
          return result
        }
      }
      store.dispatch('user/resetToken')
      if (location.href.indexOf('login') !== -1) {
        location.reload() // 为了重新实例化vue-router对象 避免bug
      } else {
        MessageBox.confirm(
          '登录状态已过期，您可以继续留在该页面，或者重新登录',
          '系统提示',
          {
            confirmButtonText: '重新登录',
            cancelButtonText: '取消',
            type: 'warning'
          }
        ).then(() => {
          location.reload() // 为了重新实例化vue-router对象 避免bug
        })
      }
    } else if (code === 6401) {
      const result = await store.dispatch('user/refreshToken'
      ).then(() => {
        console.log('refresh_token success')
        return axios_cv_ks(response.config)
      }).then(retryRes => {
        console.log('重试结果', retryRes)
        return retryRes
      }).catch(error => {
        console.log(error)
        return undefined
      })
      console.log('retry result,', result)
      if (result !== undefined) {
        return result
      }
      MessageBox.confirm(
        '登录状态已过期，您可以继续留在该页面，或者重新登录',
        '系统提示',
        {
          confirmButtonText: '重新登录',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {
        location.reload() // 为了重新实例化vue-router对象 避免bug
      })
      return false
    } else if (code === 400 || code === 403) {
      Message({
        message: response.data.msg,
        type: 'error',
        duration: 5 * 1000
      })
    } else if (code !== 200) {
      // Notification.error({
      //   title: response.data.msg
      // })
      Message({
        message: response.data.msg,
        type: 'error'
      })
      return Promise.reject('error')
    } else {
      return response
    }
  },
  error => {
    if (error.message === 'Network Error') {
      Message({
        message: '服务器连接异常，请检查服务器！',
        type: 'error',
        duration: 5 * 1000
      })
      return
    }
    console.log('err' + error) // for debug

    Message({
      message: error.message,
      type: 'error',
      duration: 5 * 1000
    })

    return Promise.reject(error)
  }
)

export function pdf2WordCreateUrl(transferurl, imageurl, startPage, endPage, exporttype, uri, maxConNumPage, imgType, justToImg, pdfDebugFlags, dpi, switchMix, getFileMeta, isScan, include_elements, get_formula, isMdTableHtml, isTocCls, isPdf) {
  // 设置入参
  var obj
  if (exporttype === 'pdf2md') {
    if (include_elements.includes('all')) {
      include_elements = ['all']
    }
    obj = {
      'pageNumBegin': startPage,
      'pageNumEnd': endPage,
      'include_elements': include_elements,
      'get_formula': get_formula,
      'isMdTableHtml': isMdTableHtml,
      'isTocCls': isTocCls
    }
    if (isPdf !== undefined) {
      obj.imgUrls = [imageurl]
    } else {
      obj.url = imageurl
    }
  } else {
    obj = {
      'PageNumBegin': startPage,
      'PageNumEnd': endPage,
      'justConvertToImg': justToImg,
      'url': imageurl,
      'maxConNumPage': maxConNumPage,
      'imgType': imgType,
      'pdfDebugFlags': pdfDebugFlags,
      'dpi': dpi,
      'switchMix': switchMix,
      'getFileMeta': getFileMeta,
      'isScan': isScan
    }
  }
  const json = JSON.stringify(obj)

  const axios_config_get_docID = {
    method: 'post',
    headers: { 'Cache-Control': 'no-cache', ...getHeaders() },
    url: transferurl + '/layout/scheduler/' + exporttype + uri,
    data: json,
    responseType: 'json',
    processData: false, // 必须
    contentType: false
  }

  return new Promise((resolve, reject) => {
    axios(axios_config_get_docID).then((res_create_url_data) => {
      resolve(res_create_url_data)
    }).catch(err => {
      reject(err)
    })
  })
}
export function outlineExtractCreateUrl(transferurl,url,uri,exporttype,isLLM) {
  // 设置入参
  var obj
    obj = {
      'url': url,
      'isLLM': isLLM
    }
  const json = JSON.stringify(obj)

  const axios_config_get_docID = {
    method: 'post',
    headers: { 'Cache-Control': 'no-cache', ...getHeaders() },
    url: transferurl + '/layout/scheduler/' + exporttype + uri,
    data: json,
    responseType: 'json',
    processData: false, // 必须
    contentType: false
  }

  return new Promise((resolve, reject) => {
    axios(axios_config_get_docID).then((res_create_url_data) => {
      resolve(res_create_url_data)
    }).catch(err => {
      reject(err)
    })
  })
}
// 转换markdown
export function kdc2md(transferurl, doc_data) {
  // console.log('[kdc2md] doc_data:', doc_data)
  const json = JSON.stringify(doc_data)
  console.log('transferurl:', transferurl)

  const axios_get_result_url = {
    method: 'post',
    headers: { 'Cache-Control': 'no-cache', 'Content-Type': 'application/json', ...getHeaders() },
    // url: transferurl + '/layout/vie/kdc2md',
    // 暂时只有开发环境有该接口
    // url: '//pcv-test-gray.wps.cn/layout/vie/kdc2md',
    // url: '//pcv-test.wps.cn/codelab/kdc2md',
    url: '//pcv-test-gray.wps.cn/layout/kdc-server/kdc2md',
    data: json,
    responseType: 'json',
    // responseType: 'text',
    processData: false // 必须
  }

  return new Promise((resolve, reject) => {
    axios(axios_get_result_url).then((res_data) => {
      // console.log('[kdc2md] res_data:', res_data)
      resolve(res_data)
    }).catch(err => {
      reject(err)
    })
  })
}

// 一键清晰plus
export function optPlus(transferurl, imageurl, use_sr, use_quad, use_hw, use_wm) {
  const obj = {
    'image_url': imageurl,
    'use_sr': use_sr,
    'use_quad': use_quad,
    'use_hw': use_hw,
    'use_wm': use_wm
  }
  console.log('[optPlus] obj:', obj)
  const json = JSON.stringify(obj)

  const axios_get_result_url = {
    method: 'post',
    headers: { 'Cache-Control': 'no-cache', 'Content-Type': 'application/json', ...getHeaders() },
    // url: transferurl + '/scan/v1/opt/opt-plus',
    url: transferurl + '/opt/opt-plus',
    data: json,
    responseType: 'json',
    processData: false // 必须
  }

  return new Promise((resolve, reject) => {
    axios(axios_get_result_url).then((optPlus_result_url) => {
      // console.log('[optPlus] optPlus_result_url:', optPlus_result_url)
      resolve(optPlus_result_url)
    }).catch(err => {
      reject(err)
    })
  })
}

// 图片转pdf.word
export function pic2WordPdfCreateUrl(transferurl, imageurl, startPage, endPage, exporttype, uri, dpi, pdfDebugFlags, switchMix, getFileMeta, isScan) {
  // var urls = []
  // urls.push(imageurl)
  let obj
  switch (exporttype) {
    case 'pdf2txt':
    case 'picedit':
      obj = {
        'imgs': imageurl.map((url, index) => ({ 'url': url, 'index': index, 'dpi': dpi })),
        params: { get_coord: true, get_paragraph: true }}
      break
    default:
      obj = {
        'PageNumBegin': startPage,
        'PageNumEnd': endPage,
        'justConvertToImg': false,
        // 'imgUrls': [imageurl],
        'imgUrls': imageurl,
        'pdfDebugFlags': pdfDebugFlags,
        'switchMix': switchMix,
        'getFileMeta': getFileMeta,
        'isScan': isScan,
        'dpi': dpi }
  }
  console.log('[pic2WordPdfCreateUrl] obj:', obj)
  const json = JSON.stringify(obj)

  const tmp_exporttype = exporttype === 'pdf2txt' ? 'pic2txt' : exporttype

  const axios_config_get_docID = {
    method: 'post',
    headers: { 'Cache-Control': 'no-cache', ...getHeaders() },
    url: transferurl + '/layout/scheduler/' + tmp_exporttype + uri,
    data: json,
    responseType: 'json',
    processData: false, // 必须
    contentType: false
  }

  return new Promise((resolve, reject) => {
    axios(axios_config_get_docID).then((res_create_url_data) => {
      resolve(res_create_url_data)
    }).catch(err => {
      reject(err)
    })
  })
}
function getUrlNameMD5(_this, url) {
  const userId = _this.$store.state.user.userId
  const deptId = _this.$store.state.user.deptId
  const useremail = _this.$store.state.user.email

  const i = url.lastIndexOf('/')
  const e = url.lastIndexOf('?')

  var name = ''
  if (e > i) name = url.substring(i + 1, e)
  else name = url.substring(i + 1)

  var filearr = name.split('.')
  var mddata = filearr[0] + userId + useremail + deptId

  return CryptoJS.MD5(mddata).toString() + '.' + filearr[1]
}

export function picEditCreateForm(transferurl, file, dpi) {
  const form = new FormData()
  form.append('imgs', file)
  form.append('dpi', dpi)
  form.append('imgType', 'png')
  console.log(form)

  const axios_config_get_docID = {
    method: 'post',
    headers: { 'Cache-Control': 'no-cache', ...getHeaders() },
    url: transferurl + '/layout/scheduler/picedit/upload',
    data: form,
    responseType: 'json',
    processData: false, // 必须
    contentType: false
  }

  return new Promise((resolve, reject) => {
    axios(axios_config_get_docID).then((res_create_url_data) => {
      resolve(res_create_url_data)
    }).catch(err => {
      reject(err)
    })
  })
}
function escapeXml(str) {
  return str.replaceAll('&', '&amp;').replaceAll('<', '&lt;').replaceAll('>', '&gt;').replaceAll('"', '&quot;').replaceAll('\'', '&apos;')
}
// 图片转pdf.word
export function convertPicEditXml2pptx(transferurl, xml) {
  var imgUrls = []
  var pdfwriterXml = []

  pdfwriterXml.push('<document>')
  pdfwriterXml.push('<filename></filename>')
  var xmlDoc = (new DOMParser()).parseFromString(xml, 'text/xml')
  var document = xmlDoc.getElementsByTagName('document')[0]
  pdfwriterXml.push('<pagecount>' + document.getElementsByTagName('pagecount')[0].textContent + '</pagecount>')
  pdfwriterXml.push('<pages>')
  var pages = document.getElementsByTagName('pages')[0].getElementsByTagName('page')
  var dpi = 144.0
  for (let i = 0; i < pages.length; i++) {
    var pageWidth = pages[i].getElementsByTagName('width')[0].textContent
    var pageHeight = pages[i].getElementsByTagName('height')[0].textContent
    pdfwriterXml.push('<page>')
    pdfwriterXml.push('<index>' + pages[i].getElementsByTagName('index')[0].textContent + '</index>')
    pdfwriterXml.push('<size>')
    pdfwriterXml.push('<height>' + pageHeight / dpi * 72 + '</height>')
    pdfwriterXml.push('<width>' + pageWidth / dpi * 72 + '</width>')
    pdfwriterXml.push('</size>')
    var imgUrlObj = pages[i].getElementsByTagName('originChangeImage')[0]
    if (imgUrlObj === undefined) {
      imgUrls.push('')
      pdfwriterXml.push('</page>')
      continue
    }
    var imgUrl = pages[i].getElementsByTagName('originChangeImage')[0].textContent

    imgUrls.push(imgUrl)
    // 构造Text
    var texts = pages[i].getElementsByTagName('text')
    for (let j = 0; j < texts.length; j++) {
      pdfwriterXml.push('<text>')
      pdfwriterXml.push('<z_index>0</z_index>')
      pdfwriterXml.push('<bndbox>')
      var pxbox = texts[j].getElementsByTagName('pxbox')[0].children
      for (let k = 0; k < pxbox.length; k += 2) {
        pdfwriterXml.push('<' + pxbox[k].tagName + '>' + (pxbox[k].textContent / pageWidth).toFixed(5) + '</' + pxbox[k].tagName + '>')
        pdfwriterXml.push('<' + pxbox[k + 1].tagName + '>' + (pxbox[k + 1].textContent / pageHeight).toFixed(5) + '</' + pxbox[k + 1].tagName + '>')
      }
      pdfwriterXml.push('</bndbox>')
      pdfwriterXml.push('<orientation>' + texts[j].getElementsByTagName('orientation')[0].textContent + '</orientation>')
      pdfwriterXml.push('<font>')
      var font = texts[j].getElementsByTagName('font')[0].children
      for (let k = 0; k < font.length; k++) {
        pdfwriterXml.push('<' + font[k].tagName + '>' + font[k].textContent + '</' + font[k].tagName + '>')
      }
      pdfwriterXml.push('</font>')
      var sentence = texts[j].getElementsByTagName('sentence')
      for (let k = 0; k < sentence.length; k++) {
        pdfwriterXml.push('<sentence>')
        pdfwriterXml.push('<bndbox>')
        var senPxbox = sentence[k].getElementsByTagName('pxbox')[0].children
        for (let l = 0; l < senPxbox.length; l += 2) {
          pdfwriterXml.push('<' + senPxbox[l].tagName + '>' + (senPxbox[l].textContent / pageWidth).toFixed(5) + '</' + senPxbox[l].tagName + '>')
          pdfwriterXml.push('<' + senPxbox[l + 1].tagName + '>' + (senPxbox[l + 1].textContent / pageHeight).toFixed(5) + '</' + senPxbox[l + 1].tagName + '>')
        }
        pdfwriterXml.push('</bndbox>')
        pdfwriterXml.push('<content>' + escapeXml(sentence[k].getElementsByTagName('content')[0].textContent) + '</content>')
        pdfwriterXml.push('<font>')
        var senFont = sentence[k].getElementsByTagName('font')[0].children
        for (let l = 0; l < senFont.length; l++) {
          pdfwriterXml.push('<' + senFont[l].tagName + '>' + senFont[l].textContent + '</' + senFont[l].tagName + '>')
        }
        pdfwriterXml.push('</font>')
        pdfwriterXml.push('<angle>' + sentence[k].getElementsByTagName('angle')[0].textContent + '</angle>')
        pdfwriterXml.push('</sentence>')
      }
      pdfwriterXml.push('</text>')
    }
    // 构造底图
    pdfwriterXml.push('<image>')
    pdfwriterXml.push('<z_index>-1</z_index>')
    pdfwriterXml.push('<ref>-1</ref>')
    pdfwriterXml.push('<srcbndbox>')
    pdfwriterXml.push('<x1>0</x1>')
    pdfwriterXml.push('<y1>0</y1>')
    pdfwriterXml.push('<x2>1</x2>')
    pdfwriterXml.push('<y2>0</y2>')
    pdfwriterXml.push('<x3>1</x3>')
    pdfwriterXml.push('<y3>1</y3>')
    pdfwriterXml.push('<x4>0</x4>')
    pdfwriterXml.push('<y4>1</y4>')
    pdfwriterXml.push('</srcbndbox>')
    pdfwriterXml.push('<dstbndbox>')
    pdfwriterXml.push('<x1>0</x1>')
    pdfwriterXml.push('<y1>0</y1>')
    pdfwriterXml.push('<x2>1</x2>')
    pdfwriterXml.push('<y2>0</y2>')
    pdfwriterXml.push('<x3>1</x3>')
    pdfwriterXml.push('<y3>1</y3>')
    pdfwriterXml.push('<x4>0</x4>')
    pdfwriterXml.push('<y4>1</y4>')
    pdfwriterXml.push('</dstbndbox>')
    pdfwriterXml.push('</image>')
    pdfwriterXml.push('<angle>0</angle>')
    pdfwriterXml.push('<angleFlag>false</angleFlag>')
    pdfwriterXml.push('<orientation>0</orientation>')
    pdfwriterXml.push('<orientationFlag>false</orientationFlag>')
    pdfwriterXml.push('</page>')
  }
  pdfwriterXml.push('</pages>')
  pdfwriterXml.push('</document>')

  const xml_string = pdfwriterXml.join('\n')
  var deflate = Zlib.deflateSync(xml_string)
  var base64 = deflate.toString('base64')

  const obj = {
    'xml': base64,
    'imgUrls': imgUrls,
    'exportType': 'pptx',
    'timeout': 3600000,
    'exportPDF': true
  }
  const json = JSON.stringify(obj)

  const axios_config_get_docID = {
    method: 'post',
    headers: { 'Cache-Control': 'no-cache', ...getHeaders() },
    url: transferurl + '/layout/pdfwriter/convert',
    data: json,
    responseType: 'json',
    processData: false, // 必须
    contentType: false
  }

  return new Promise((resolve, reject) => {
    axios(axios_config_get_docID).then((res_create_url_data) => {
      resolve(res_create_url_data)
    }).catch(err => {
      reject(err)
    })
  })
}

// axios_cv_ks
export function fetchUrl2Ks(_this, sourceurl) {
  const filenamemd5 = getUrlNameMD5(_this, sourceurl)
  const formData = new FormData()
  formData.append('url', sourceurl)
  formData.append('md5', filenamemd5)
  const axios_config_post_ks3 = {
    method: 'post',
    headers: { 'Cache-Control': 'no-cache', ...getHeaders() },
    url: process.env.VUE_APP_BASE_API + '/layout/fetchurl',
    data: formData,
    responseType: 'json',
    processData: false, // 必须
    contentType: false
  }
  return axios_cv_ks(axios_config_post_ks3)
}

export function uploadKs3Store(resp, file) {
  const formData = new FormData()

  formData.append('acl', resp.data.data['acl'])
  formData.append('success_action_status', '200')
  formData.append('key', resp.data.data['key'])
  formData.append('Signature', resp.data.data['signature'])
  formData.append('KSSAccessKeyId', resp.data.data['kSSAccessKeyId'])
  formData.append('Policy', resp.data.data['policy'])
  formData.append('file', file)

  const url = 'https://' + resp.data.data['url']

  const axios_config_post_ks3 = {
    method: 'post',
    headers: { 'Cache-Control': 'no-cache', ...getHeaders() },
    url: url,
    data: formData,
    responseType: 'json',
    processData: false, // 必须
    contentType: false
  }

  return new Promise((resolve, reject) => {
    axios(axios_config_post_ks3).then((res_uploadks3_data) => {
      resolve(res_uploadks3_data)
    }).catch(err => {
      reject(err)
    })
  })
}

export function httpRequesSignKs(name, accessmethod = 'public-read', application = '') {
  // eslint-disable-next-line no-undef
  const expiration = new Date(getExpires(60 * 10) * 1000).toISOString()
  const signatureform = new FormData()

  signatureform.append('filename', name)
  signatureform.append('expiration', expiration)
  signatureform.append('accessmethod', accessmethod)
  if (application !== '') {
    signatureform.append('application', application)
  }
  const axios_config_get_policy_signature = {
    method: 'post',
    headers: { 'Cache-Control': 'no-cache', ...getHeaders() },
    url: process.env.VUE_APP_BASE_API + '/layout/signature',
    data: signatureform,
    responseType: 'json',
    processData: false, // 必须
    contentType: false
  }
  return axios_cv_ks(axios_config_get_policy_signature)
//   return new Promise((resolve, reject) => {
//     axios(axios_config_get_policy_signature).then((res_sign_data) => {
//       resolve(res_sign_data)
//     }).catch(err => {
//       reject(err)
//     })
//   })
}
export function SecDataHttpRequestSignKs(name, accessmethod = 'public-read', application = '', object_key = '') {
  // eslint-disable-next-line no-undef
  const expiration = new Date(getExpires(60 * 10) * 1000).toISOString()
  const signatureform = new FormData()

  signatureform.append('filename', name)
  signatureform.append('object_key', object_key)
  signatureform.append('expiration', expiration)
  signatureform.append('accessmethod', accessmethod)
  if (application !== '') {
    signatureform.append('application', application)
  }
  const axios_config_get_policy_signature = {
    method: 'post',
    headers: { 'Cache-Control': 'no-cache', ...getHeaders() },
    url: process.env.VUE_APP_BASE_API + '/upload2ks3/get_signature',
    data: signatureform,
    responseType: 'json',
    processData: false, // 必须
    contentType: false
  }
  return axios_cv_ks(axios_config_get_policy_signature)
}
