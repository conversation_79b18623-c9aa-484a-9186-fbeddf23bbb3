<template>
  <BasicLayout>
    <template #wrapper>
      <el-card class="box-card">
        <el-form ref="queryForm" :model="queryParams" :inline="true">
          <el-form-item label="用户名">
            <el-input
              v-model="queryParams.user_name"
              placeholder="请输入用户名"
              clearable
              size="small"
              style="width: 130px"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="用户ID" prop="user_id"><el-input
            v-model="queryParams.user_id"
            placeholder="请输入用户ID"
            clearable
            size="small"
            style="width: 130px"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="模型提供方"><el-input
            v-model="queryParams.provider"
            placeholder="模型提供方"
            clearable
            size="small"
            style="width: 140px"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="模型名称"><el-input
            v-model="queryParams.model_name"
            placeholder="模型名称"
            clearable
            size="small"
            style="width: 180px"
            @keyup.enter.native="handleQuery"
          />
          </el-form-item>
          <el-form-item label="状态" prop="status"><el-select
            v-model="queryParams.status"
            placeholder="状态"
            clearable
            size="small"
            style="width: 120px"
          >
            <el-option
              v-for="dict in whitelistStatusOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            <!-- <el-button v-permisaction="['cvdata:pdfEditRequest:err']" icon="el-icon-bottom" size="mini" @click="errurlsExport">查询</el-button> -->
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd">新增
            </el-button>
          </el-col>
        </el-row>

        <el-table v-loading="loading" :data="chatfileWhiteList" span="1.36" @selection-change="handleSelectionChange">
          <el-table-column v-if="false" type="selection" width="5" align="center" />
          <el-table-column label="用户名" align="center" prop="user_name" width="170" :show-overflow-tooltip="true" />
          <el-table-column label="用户ID" align="center" prop="user_id" width="170" :show-overflow-tooltip="true" />
          <el-table-column label="模型提供方" align="center" prop="provider" width="120" :show-overflow-tooltip="true" />
          <el-table-column label="模型名称" align="center" prop="model_name" width="120" :show-overflow-tooltip="true" />
          <el-table-column label="创建人" align="center" prop="createBy" width="120" />
          <el-table-column label="更新人" align="center" prop="updateBy" width="120" :show-overflow-tooltip="true" />
          <el-table-column
            label="更新时间"
            align="center"
            prop="updatedAt"
            width="180"
            :show-overflow-tooltip="true"
            :formatter="formatDate"
          />
          <el-table-column label="状态" align="center" prop="status" width="120">
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.status"
                :active-value="0"
                :inactive-value="1"
                @change="updateStatus(scope.row)"
              />
            </template>
          </el-table-column><el-table-column label="动作" align="center" prop="action" width="240">
            <template slot-scope="scope">
              <el-button type="primary" icon="el-icon-edit" @click="handleUpdate(scope.row)" />
              <el-button type="danger" icon="el-icon-delete" @click="deleteItem(scope.row)" />
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageIndex"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
        <!-- 添加白名单对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="500px" :close-on-click-modal="false">
          <el-form ref="form" :model="form" :rules="rules">
            <el-form-item label="用户名" prop="user_name">
              <el-input v-model="form.user_name" placeholder="用户名(添加多个用户ID采用英文逗号隔开,示例:张三,李四,王五)" :disabled="isEdit" clearable />
            </el-form-item>
            <el-form-item label="用户ID" prop="user_id">
              <el-input v-model="form.user_id" placeholder="用户ID(添加多个用户ID采用英文逗号隔开,示例:1,2,3)" :disabled="isEdit" clearable />
              <!-- <el-input-number v-model="form.user_id" placeholder="用户ID" clearable :style="{ width: '450px' }" /> -->
            </el-form-item>
            <el-form-item label="模型提供方" prop="provider">
              <el-input v-model="form.provider" placeholder="模型提供方" clearable size="small" />
            </el-form-item>
            <el-form-item label="模型名称" prop="model_name">
              <el-input v-model="form.model_name" placeholder="模型名称" clearable size="small" />
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
          </div>
        </el-dialog>
      </el-card>
    </template>
  </BasicLayout>
</template>

<script>
import { addWhiteList, delWhiteList, listWhiteList, updateWhiteList, editWhiteList } from '@/api/chatfile/white_list'
import moment from 'moment'

export default {
  name: 'WhiteList',
  components: {},
  data() {
    return {
      // 搜索参数
      search_user_name: undefined,
      search_user_id: undefined,
      search_provider: undefined,
      search_model_name: undefined,
      search_status: undefined,
      // 新增白名单参数
      user_name: '',
      user_id: '',
      model_name: '',

      loading: true,
      open: false,
      isEdit: false,
      showModal: false,
      total: 0,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 弹出层标题
      title: '',
      chatfileModelOptions: [],
      whitelistStatusOptions: [],
      chatfileWhiteList: [],
      // 查询参数
      queryParams: {
        pageIndex: 1,
        pageSize: 10,
        user_name: undefined,
        user_id: undefined,
        provider: undefined,
        model_name: undefined,
        status: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        user_name: [{ required: true, message: '用户名不能为空', trigger: 'blur' }],
        user_id: [{ required: true, message: '用户ID不能为空', trigger: 'blur' }],
        provider: [{ required: true, message: '模型提供方不能为空', trigger: 'blur' }],
        model_name: [{ required: true, message: '模型名称不能为空', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.getList()
    this.getDicts('chatfile_white_list_status').then(response => {
      this.whitelistStatusOptions = response.data
    })
    this.getDicts('chatfile_model').then(response => {
      this.chatfileModelOptions = response.data
    })
  },
  methods: {
    // 列表页
    getList() {
      this.loading = true
      listWhiteList(this.queryParams).then(response => {
        this.chatfileWhiteList = response.data.list
        this.total = response.data.count
        this.loading = false
      }
      )
    },
    /** 修改按钮操作 */
    updateStatus(row) {
      this.reset()
      row.updateBy = this.$store.state.user.name
      updateWhiteList(row).then(response => {
        if (response.code === 200) {
          this.getList()
        } else {
          this.msgError(response.msg)
        }
      })
    },
    /** 编辑按钮操作 */
    handleUpdate(row) {
      this.reset()
      this.title = '修改白名单'
      this.open = true
      this.isEdit = true
      this.form = row
    },
    /** 删除按钮操作 */
    deleteItem(row) {
      this.$confirm('是否确认删除编号为"' + row.id + '"的数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(function() {
        return delWhiteList(row.id)
      }).then((response) => {
        if (response.code === 200) {
          this.msgSuccess(response.msg)
          this.open = false
          this.getList()
        } else {
          this.msgError(response.msg)
        }
      }).catch(function() {
      })
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加白名单'
      this.isEdit = false
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        user_name: undefined,
        user_id: undefined,
        provider: undefined,
        model_name: undefined
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.search_user_name = this.queryParams.user_name
      this.search_user_id = this.queryParams.user_id
      this.search_provider = this.queryParams.provider
      this.search_model_name = this.queryParams.model_name
      this.search_status = this.queryParams.status
      this.queryParams.pageIndex = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.search_user_name = undefined
      this.search_user_id = undefined
      this.search_provider = undefined
      this.search_model_name = undefined
      this.search_status = undefined
      this.queryParams.user_name = undefined
      this.queryParams.user_id = undefined
      this.queryParams.model_name = undefined
      this.queryParams.status = undefined
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },

    /** 提交按钮 */
    submitForm: function() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.isEdit) {
            editWhiteList(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess(response.msg)
                this.open = false
                this.getList()
              } else {
                this.msgError(response.msg)
              }
            })
          } else {
            this.form.status = 1
            this.form.createBy = this.$store.state.user.name
            this.form.updateBy = this.$store.state.user.name

            // 处理字符串为数组，确保字段是字符串类型
            const userNamesArray = (this.form.user_name || '').split(',').map(name => name.trim())
            const userIdsArray = (this.form.user_id || '').split(',').map(id => parseInt(id.trim(), 10))

            // 更新表单数据
            const dataToSend = {
              model_name: this.form.model_name,
              provider: this.form.provider,
              user_names: userNamesArray,
              user_ids: userIdsArray,
              status: this.form.status,
              createBy: this.form.createBy,
              updateBy: this.form.updateBy
            }

            addWhiteList(dataToSend).then(response => {
              if (response.code === 200) {
                this.msgSuccess(response.msg)
                this.open = false
                this.getList()
              } else {
                this.msgError(response.msg)
              }
            })
          }
        }
      })
    },
    closeAdd() {
      this.open = false
      this.reset()
    },
    formatDate(row, column) {
      return moment(row[column.property]).format('YYYY-MM-DD HH:mm:ss')
    }
  }
}
</script>
