import { login, logout, getInfo, refreshtoken } from '@/api/user'
import { getToken, setToken, removeToken } from '@/utils/auth'
import router, { resetRouter } from '@/router'
import storage from '@/utils/storage'

const state = {
  token: getToken(),
  name: '',
  avatar: '',
  introduction: '',
  roles: [],
  permissions: [],
  permisaction: [],
  email: '',
  buttonroleid: ''
}

const mutations = {
  SET_TOKEN: (state, token) => {
    state.token = token
  },
  SET_INTRODUCTION: (state, introduction) => {
    state.introduction = introduction
  },
  SET_NAME: (state, name) => {
    state.name = name
  },
  SET_AVATAR: (state, avatar) => {
    if (avatar.indexOf('http') !== -1) {
      state.avatar = avatar
    } else {
      state.avatar = process.env.VUE_APP_BASE_API + avatar
    }
  },
  SET_ROLES: (state, roles) => {
    state.roles = roles
  },
  SET_PERMISSIONS: (state, permisaction) => { // state 是当前模块的状态对象，permission 是要设置的权限列表
    state.permisaction = permisaction
  },
  SET_EMAIL: (state, email) => {
    state.email = email
  },
  SET_BUTTONROLEID: (state, roleId) => {
    state.buttonroleid = roleId
  }
}

const actions = {
  // user login
  login({ commit }, userInfo) {
    return new Promise((resolve, reject) => {
      login(userInfo).then(response => {
        console.log('【user login】response: ', response)
        // 400 || 403
        if (response === undefined) {
          console.log('undefined resp')
          return Promise.resolve()
        }
        // 500
        if (response.code === undefined) {
          return Promise.reject('内部错误' + response.msg)
        }
        // 200
        const { token } = response
        commit('SET_TOKEN', token)
        setToken(token)
        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  },

  // get user info
  // 调用 getInfo() 来获取用户信息，并将获取到的信息存储到 Vuex 的状态中，具体存储到哪些状态属性中由 mutations 中的相关函数决定
  // 接收两个参数：commit 用于调用 mutations 中的函数来修改状态，state 用于访问当前的状态
  getInfo({ commit, state }) {
    // 创建 Promise 对象，用于异步处理获取用户信息的过程
    return new Promise((resolve, reject) => {
      getInfo().then(response => {
        // 判断是否获取用户信息失败
        if (!response || !response.data) {
          // 清空令牌
          commit('SET_TOKEN', '')
          // 移除令牌
          removeToken()
          // 调用 resolve 函数，表示操作成功
          resolve()
        }

        // 从 response.data 中解构出用户信息的各个属性
        const { roles, name, avatar, introduction, permissions, email } = response.data

        // roles must be a non-empty array
        if (!roles || roles.length <= 0) {
          // 当获取到的用户信息不合法时（为空或长度为零），调用 reject 传递错误信息
          reject('getInfo: roles must be a non-null array!')
        }
        // 调用 mutation 中的 SET_PERMISSIONS函数来修改状态，并将获取到的权限列表作为参数传递进去
        commit('SET_PERMISSIONS', permissions)
        commit('SET_ROLES', roles)
        commit('SET_NAME', name)
        commit('SET_AVATAR', avatar)
        commit('SET_INTRODUCTION', introduction)
        commit('SET_EMAIL', email)
        commit('SET_BUTTONROLEID', '110')
        // 调用 resolve 函数，表示异步操作成功，并传递获取到的用户信息作为结果
        resolve(response)
      }).catch(error => {
        // 调用 reject 传递错误信息
        reject(error)
      })
    })
  },
  // 退出系统
  LogOut({ commit, state }) {
    return new Promise((resolve, reject) => {
      logout(state.token).then(() => {
        commit('SET_TOKEN', '')
        commit('SET_ROLES', [])
        commit('SET_PERMISSIONS', [])
        removeToken()
        storage.clear()
        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  },
  // 刷新token
  refreshToken({ commit, state }) {
    return new Promise((resolve, reject) => {
      refreshtoken({ token: state.token }).then(response => {
        const { token } = response
        commit('SET_TOKEN', token)
        setToken(token)
        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  },

  // remove token
  resetToken({ commit }) {
    return new Promise(resolve => {
      commit('SET_TOKEN', '')
      removeToken()
      resolve()
    })
  },

  // dynamically modify permissions
  changeRoles({ commit, dispatch }, role) {
    // eslint-disable-next-line no-async-promise-executor
    return new Promise(async resolve => {
      const token = role + '-token'

      commit('SET_TOKEN', token)
      setToken(token)

      const { roles } = await dispatch('getInfo')

      resetRouter()

      // generate accessible routes map based on roles
      const accessRoutes = await dispatch('permission/generateRoutes', roles, { root: true })

      // dynamically add accessible routes
      router.addRoutes(accessRoutes)

      // reset visited views and cached views
      dispatch('tagsView/delAllViews', null, { root: true })

      resolve()
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
