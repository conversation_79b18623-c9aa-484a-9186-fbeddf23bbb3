
<template>
  <BasicLayout>
    <template #wrapper>
      <el-card class="box-card">
        <el-form ref="queryForm" :model="queryParams" :inline="true" label-width="110px">
          <!-- <el-form-item label="日期">
            <el-date-picker
              v-model="queryParams.starttime"
              size="small"
              type="date"
              align="right"
              value-format="yyyy-MM-dd"
            />
          </el-form-item> -->
          <el-form-item label="dataset_id">
            <el-input
              v-model="queryParams.dataset_id"
              placeholder="请输入dataset_id"
              clearable
              size="small"
              style="width: 150px"
            />
          </el-form-item>
          <el-form-item label="sample_id">
            <el-input
              v-model="queryParams.sample_id"
              placeholder="请输入sample_id"
              clearable
              size="small"
              style="width: 150px"
            />
          </el-form-item>
          <!-- <el-form-item label="评测人">
            <el-input
              v-model="queryParams.reviewer"
              placeholder="请输入评测人"
              clearable
              size="small"
              style="width: 180px"
            />
          </el-form-item> -->

          <!-- <el-form-item v-if="showLabelList" label="标签类别"><el-select
            v-model="queryParams.label_name"
            placeholder="选择标签类别"
            clearable
            size="small"
            multiple
          >
            <el-option
              v-for="dict in labelNameOptions"
              :key="dict.value"
              :label="dict.LabelName"
              :value="dict.LabelName"
            />
          </el-select>
          </el-form-item> -->
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
            >搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
          <!-- <el-form-item label="显示object_key">
            <el-checkbox
              @change="showOtherInfosClick()"
            />
          </el-form-item> -->
        </el-form>

        <!-- <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="success"
              icon="el-icon-edit"
              size="mini"
              :disabled="single"
              @click="handleUpdate"
            >修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="warning"
              icon="el-icon-edit"
              size="mini"
              :disabled="multiple"
              @click="handleUpdateLabel"
            >批量修改样张标签</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              icon="el-icon-delete"
              size="mini"
              :disabled="isEmpty"
              @click="handleDelete"
            >删除</el-button>
          </el-col>
        </el-row> -->
        <el-table v-loading="loading" :data="samplesList" span="11.1" :cell-style="{padding:'5'}" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column v-if="false" type="selection" width="5" align="left" /><el-table-column
            label="recordID"
            align="center"
            prop="record_id"
            width="80"
            :show-overflow-tooltip="true"
          /><el-table-column v-if="false" type="selection" width="5" align="left" /><el-table-column
            label="index"
            align="center"
            prop="index"
            width="60"
            :show-overflow-tooltip="true"
          /><el-table-column
            label="sampleID"
            align="center"
            prop="sample_id"
            width="160"
            sortable
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="Source"
            align="center"
            prop="source"
            width="160"
            sortable
            :show-overflow-tooltip="true"
          />
          <!-- <el-table-column label="源文件" align="center">
            <template slot-scope="scope">
              <div v-if="isPdfFile !== true">
                <el-image
                  style="max-width: 300px; max-height: 300px; object-fit: contain"
                  :src="scope.row.origin_url[0].object_key"
                  @click="handlePreview(scope.row.origin_url[0].object_key)"
                />
              </div>
              <div v-else>
                <iframe
                  :src="scope.row.origin_url[0].object_key + '#navpanes=0'"
                  style="width: 100%; height: 300px; border: none;"
                  scrolling="yes"
                />
                <el-button
                  size="mini"
                  type="text"
                  @click="handlePreview(scope.row.origin_url[0].object_key)"
                >打开文件</el-button>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="目标文件" align="center">
            <template slot-scope="scope">
              <el-button
                size="mini"
                icon="el-icon-document"
                @click="handlePreview(scope.row.result_url)"
              >
                打开文件
              </el-button>
            </template>
          </el-table-column> -->
          <!-- <el-table-column label="渲染文件" align="center">
            <template slot-scope="scope">
              <el-image
                style="max-width: 300px; max-height: 300px; object-fit: contain"
                :src="scope.row.rendering_url[0].object_key"
                @click="handlePreview(scope.row.rendering_url[0].object_key)"
              />
            </template>
          </el-table-column> -->

          <!-- style="max-width: 300px; max-height: 300px; object-fit: contain; margin: 5px;" -->
          <!-- <el-table-column label="渲染图" align="center">
            <template slot-scope="scope">
              <div v-if="scope.row.rendering_url[0].object_key!==''" style="display: flex; flex-wrap: wrap;">
                <el-image
                  v-for="(url, index) in scope.row.rendering_url"
                  :key="index"
                  style="max-width: 300px; max-height: 300px; object-fit: contain;"
                  :src="url.object_key"
                  @click="handlePreview(url.object_key)"
                />
              </div>
              <div v-else>
                {{ '无渲染图' }}
              </div>
            </template>
          </el-table-column> -->
          <el-table-column label="其他文件" align="center">
            <template slot-scope="scope">
              <el-button
                size="mini"
                icon="el-icon-document"
                @click="handlePreview(scope.row.other_url[0].object_key)"
              >
                打开文件
              </el-button>
            </template>
          </el-table-column>
          <!-- :src="scope.row.result_url + '#navpanes=0'"   -->
          <!-- style="width: 100%; height: 100%; border: none;" -->
          <!-- style="width: 300px; height: 300px; border: none;" -->
          <!-- :src="'https://zhai-datas.ks3-cn-beijing.ksyuncs.com/layout/people/wangluoyan/demo_sample/markdown/cleandoc.pdf' + '#navpanes=0'" -->
          <!-- pdf文件 -->
          <!-- <el-table-column label="结果文件" align="center">
            <template slot-scope="scope">
              <iframe
                :src="scope.row.results_url + '#navpanes=0'"
                style="width: 100%; height: 300px; border: none;"
                scrolling="yes"
              />
              <el-button
                size="mini"
                type="text"
                @click="handlePreview(scope.row.results_url)"
              >打开文件</el-button>
            </template>
          </el-table-column> -->
          <el-table-column label="结果详情" align="center">
            <template slot-scope="scope">
              <div>
                <!-- v-permisaction="['cvdata:All:viewparameter']" -->
                <el-button icon="el-icon-view" size="mini" @click="handleView(scope.row.result_url.object_key)">查看</el-button>
                <el-button icon="el-icon-download" size="mini" @click="handleDownload(scope.row.result_url.object_key, 'recordID'+scope.row.record_id+'_index'+scope.row.index+'_result.json')">下载</el-button>
              </div>
              <el-dialog :visible.sync="viewScoreDetail" :title="'结果详情'" class="json-viewer-container" width="500px">
                <el-button icon="el-icon-check" size="mini" @click="handleCopy(scope.row.result_json_data)">复制</el-button>
                <json-viewer :value="scope.row.result_json_data" theme="dark" :expand-depth="500" />
                <div slot="footer" class="dialog-footer">
                  <el-button @click="cancelScoreDetail">关 闭</el-button>
                </div>
              </el-dialog>
            </template>
          </el-table-column>

          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <EvaluationForm
                :key="scope.row.record_id+'_'+scope.row.index"
                :record-id="scope.row.record_id"
                :record-index="scope.row.index"
                :score-detail-data="scope.row.score_detail"
              />
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page-sizes="[100, 200, 300, 500]"
          :page.sync="queryParams.pageIndex"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>
    </template>
  </BasicLayout>
</template>

<script>
import axios from 'axios'
import { getHeaders } from '@/utils/get-header'
import EvaluationForm from '@/components/Record/evaluation.vue'

// import elImageViewer from 'element-ui/packages/image/src/image-viewer.vue'
export default {
  name: 'RecordEvaluation',
  components: {
    EvaluationForm
  },
  props: {
    acceptFileType: {
      type: Array,
      default: function() {
        // 支持预览图片和pdf文件
        return ['image/png', 'image/jpg', 'image/jpeg', 'application/pdf', 'image/webp']
      }
    }
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 非空禁用
      isEmpty: true,
      showHandleUpdateLabel: false,
      // 总条数
      total: 0,
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      isEdit: false,
      showErrMsgs: false,
      // 类型数据字典
      samplesList: [],
      statusOptions: [],
      labelNameOptions: [],
      fileTypeOptions: undefined,
      // 是否只返回object_key
      isOnlyObjectKeyOptions: [
        { label: '是', value: false },
        { label: '否', value: true }
      ],
      // 是否显示标签列表
      showLabelList: false,
      // 标签列表
      labelList: [],
      // 关系表类型
      dowdate: undefined,
      imgUrls: [],
      // 查询参数
      queryParams: {
        pageIndex: 1,
        pageSize: 100,
        dataset_id: undefined,
        sample_id: undefined
      },
      // 是否展示其他信息
      showOtherInfos: false,
      // 表单校验
      rules: { docid: [{ required: true, message: 'id不能为空', trigger: 'blur' }],
        starttime: [{ required: true, message: '(:s)不能为空', trigger: 'blur' }],
        status: [{ required: true, message: ', 0:, 1:, 2:不能为空', trigger: 'blur' }]
      },
      // 文件预览相关
      showViewer: false,
      viewPhotoList: [],
      // 查看评分细则
      viewScoreDetail: false,
      // 评分细则json数据
      scoreDetailData: {},
      // 是否为pdf文件
      isPdfFile: false
    }
  },
  created() {
    this.initializeData()
    this.getDicts('sample_filetype').then(response => {
      this.fileTypeOptions = response.data
    })
  },
  watch: {
    // 监听路由变化，当dataset_id参数变化时重新加载数据
    '$route'(to, from) {
      console.log('评估页面路由变化:', to.query, from.query)
      if (to.query.dataset_id !== from.query.dataset_id) {
        this.initializeData()
      }
    }
  },
  methods: {
    /** 初始化数据 */
    initializeData() {
      const { dataset_id } = this.$route.query
      console.log('评估页面 initializeData dataset_id:', dataset_id)

      // 重置数据
      this.samplesList = []
      this.total = 0
      this.queryParams.pageIndex = 1

      if (dataset_id) {
        this.queryParams.dataset_id = dataset_id
        console.log('评估页面设置 queryParams.dataset_id:', dataset_id)
        this.getList()
      } else {
        this.loading = false
      }
    },
    handlePreview(imageUrl) {
      // 在此处打开模态框或者跳转到新页面展示原图
      window.open(imageUrl, '_blank')
    },
    /** 查看评分细则 */
    handleView(score_detail) {
      // this.handleSelectDocid()
      this.scoreDetailData = score_detail
      this.viewScoreDetail = true
    },
    // 取消评分细则按钮
    cancelScoreDetail() {
      this.viewScoreDetail = false
      // this.reset()
    },
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.sample_id)
      this.single = selection.length !== 1
      this.multiple = this.getMultiple(selection.length)
      this.isEmpty = selection.length === 0
    },
    getMultiple(length) {
      return length <= 1
    },
    /** 查询参数列表 */
    getList() {
      this.loading = true
      this.isPdfFile = false
      // listRequests接口传入两个参数：pageIndex 和 pageSize
      // listEffect(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
      //   // const form = new FormData()
      //   // listSamples(form).then(response => {
      //   this.samplesList = response.data.result_data
      //   this.total = response.data.result_count
      //   this.loading = false
      // }
      // )
      var data_dict = {
        // 'dataset_id': 2,
        'function': 'eval',
        'page_index': this.queryParams.pageIndex,
        'page_size': this.queryParams.pageSize
      }
      if (this.queryParams.dataset_id) {
        data_dict.dataset_id = parseInt(this.queryParams.dataset_id)
      }
      if (this.queryParams.sample_id) {
        data_dict.sample_id = parseInt(this.queryParams.sample_id)
      }
      console.log('param:', data_dict)
      return new Promise((resolve, reject) => {
        axios({
          method: 'post',
          headers: { 'Cache-Control': 'no-cache', 'Content-Type': 'application/json', ...getHeaders() },
          // 公式服务
          url: 'http://10.213.40.66:8001/data-processing' + '/records/search',
          data: data_dict,
          responseType: 'json',
          processData: false,
          contentType: false,
          timeout: 80000 // 设置超时时间为80秒
        }).then(res => {
          if (res.data.code !== 200 || res.data.data === undefined || res.data.data.record_data === undefined) {
            return Promise.reject('转化请求失败!')
          }
          // 处理完成得到string类型的公式
          // this.samplesList = res.data.data.dataset_data
          console.log('res.data.data:', res.data.data)
          if (res.data.data.data_count <= 0) {
            this.message('warn', res.data.msg, '但数据为空 data_count = 0')
            this.loading = false
            return Promise.resolve(1)
          }
          const result_data = res.data.data.record_data
          // var record_datas = []
          const score_detail = {
            item: '',
            score: 0,
            reason: '',
            remark: ''
          }

          // 创建一个数组来存储所有的Promise
          const promises = result_data.map(async(data, index) => {
            // console.log('data.result_url:', data.result_url)
            // const jsonData = await axios.get(data.result_url)
            // data.result_json_data = jsonData.data
            // 初始化，用于后续打分
            data.score_detail = score_detail
            data.score = 0
            data.remark = ''
            if (data.sample_id === undefined) {
              data.sample_id = 0
            }
            // console.log('data.result_json_data:', data.result_json_data)
            // 返回已经处理好的数据
            return data
          })

          // 使用Promise.all()等待所有Promise完成
          Promise.all(promises).then((record_datas) => {
            // 判断是否pdf文件
            const url = record_datas[0].origin_url[0].object_key
            console.log('url:', url)
            const parsedUrl = new URL(url)
            const pathname = parsedUrl.pathname
            const filename = pathname.substring(pathname.lastIndexOf('/') + 1)
            console.log('filename:', filename)
            if (filename.toLowerCase().endsWith('.pdf')) {
              this.isPdfFile = true
              console.log('This is a PDF file')
            }
            console.log('record_datas:', record_datas)
            this.samplesList = record_datas
            console.log('this.samplesList:', this.samplesList)
            this.total = res.data.data.data_count
            this.loading = false
            resolve(res.data.data.data_count)
          })
        }).catch((error) => {
          this.message('error', '服务请求失败! ' + error, '。具体信息: ' + error.response.data.msg)
          // return Promise.resolve(0)
          reject(error)
        })
      })
    },
    message(type, msg, dataMsg) {
      this.$message({
        showClose: true,
        message: msg + dataMsg,
        center: true,
        duration: 4000,
        type: type
      })
    },
    /** 复制结果详情 */
    handleCopy(result_json_data) {
      this.copyData(result_json_data)
    },
    copyData(data) {
      var data_value = JSON.stringify(data)
      // console.log('data_value:', data_value)

      var textArea = document.createElement('textarea')
      textArea.value = data_value
      document.body.appendChild(textArea)
      textArea.select()
      textArea.focus()
      document.execCommand('copy')
      document.body.removeChild(textArea)
      this.$message.success('复制成功')
    },
    async handleDownload(fileUrl, fileName) {
    //   const fileUrl = 'https://example.com/file.pdf' // 替换为要下载的文件链接
      const response = await fetch(fileUrl)
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = fileName // 替换为要保存的文件名
      a.style.display = 'none'
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
    },
    // 格式化
    format(imgsUrl) {
      const externalUrl = imgsUrl.replace(/-internal/g, '')
      const cleanUrl = externalUrl.replace(/\[|\]/g, '')
      return cleanUrl
    },
    closeViewer() {
      this.showViewer = false
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        sample_id: undefined,
        initial_name: '',
        labels: [],
        remark: ''
      }
      this.showHandleUpdateLabel = false
      this.resetForm('form')
    },
    /** 是否展示其他信息 */
    showOtherInfosClick() {
      this.showOtherInfos = !this.showOtherInfos
    },
    /** 搜索按钮操作 */
    handleQuery() {
      // this.dowdate = this.queryParams.starttime
      this.queryParams.pageIndex = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      // this.resetForm('queryForm')
      this.queryParams.dataset_id = undefined
      this.queryParams.sample_id = undefined
      this.queryParams.pageIndex = 1
      this.queryParams.pageSize = 10
      // 清空数据
      this.samplesList = []
      this.loading = false
      // this.handleQuery()
    }
  }
}
</script>

<style>
.json-viewer-container {
    text-align: left;
}
</style>
