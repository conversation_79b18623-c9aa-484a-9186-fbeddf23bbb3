
<template>
  <BasicLayout>
    <template #wrapper>
      <el-card class="box-card">
        <el-form ref="queryForm" :model="queryParams" :inline="true" label-width="110px">
          <el-form-item label="dataset_name">
            <el-input
              v-model="queryParams.dataset_name"
              placeholder="请输入dataset_name"
              clearable
              size="small"
              style="width: 150px"
            />
          </el-form-item>
          <el-form-item class="button-group">
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              class="mr-8"
              @click="handleQuery"
            >搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" class="mr-16" @click="resetQuery">重置</el-button>
          </el-form-item>
          <el-form-item class="create-group">
            <el-button
              type="success"
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
            >创建数据集</el-button>
          </el-form-item>
        </el-form>

        <el-table v-loading="loading" :data="samplesList" style="width: 100%" :cell-style="{padding:'5px'}" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" />
          <!-- <el-table-column v-if="false" type="selection" width="5" align="left" /> -->
          <el-table-column
            label="datasetID"
            align="center"
            flex="1"
            prop="dataset_id"
            sortable
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="dataset_name"
            align="center"
            prop="dataset_name"
            flex="1"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="status"
            align="center"
            flex="1"
            prop="status"
            sortable
            :show-overflow-tooltip="true"
          > <template slot-scope="scope">
            <el-tag :type="scope.row.status === 0 ? 'info' : 'success'">
              {{ statusFormat(scope.row) }}
            </el-tag>
          </template>
          </el-table-column>
          <el-table-column
            label="business_scene"
            align="center"
            prop="business_scene"
            flex="1"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="remark"
            align="center"
            prop="remark"
            flex="1"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="version"
            align="center"
            prop="version"
            flex="1"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            label="操作"
            align="center"
            width="200"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                v-hasPermi="['system:dataset:evaluate']"
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleEvaluate(scope.row)"
              >评测</el-button>
              <el-button
                v-hasPermi="['system:dataset:remove']"
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
              >删除</el-button>
              <el-button
                v-hasPermi="['system:dataset:export']"
                size="mini"
                type="text"
                icon="el-icon-info"
                @click="handleDetail(scope.row)"
              >数据详情</el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page-sizes="[10, 20, 30, 50]"
          :page.sync="queryParams.pageIndex"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>

      <!-- 创建数据集弹窗 -->
      <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
        <el-form ref="form" :model="form" :rules="rules" label-width="100px">
          <el-form-item label="数据集名称" prop="dataset_name">
            <el-input
              v-model="form.dataset_name"
              placeholder="请输入数据集名称"
              maxlength="50"
              show-word-limit
            />
          </el-form-item>
          <el-form-item label="业务场景" prop="scene">
            <el-input
              v-model="form.business_scene"
              placeholder="请输入业务场景"
              maxlength="100"
              show-word-limit
            />
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="form.remark"
              type="textarea"
              placeholder="请输入备注信息"
              :rows="3"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
          <el-form-item label="版本信息" prop="version">
            <el-input
              v-model="form.version"
              placeholder="请输入版本信息，如：v1.0"
              maxlength="20"
              show-word-limit
            />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </el-dialog>
    </template>
  </BasicLayout>
</template>

<script>
import axios from 'axios'
import { getHeaders } from '@/utils/get-header'
export default {
  name: 'RecordDataset',
  props: {
    acceptFileType: {
      type: Array,
      default: function() {
        // 支持预览图片和pdf文件
        return ['image/png', 'image/jpg', 'image/jpeg', 'application/pdf', 'image/webp']
      }
    }
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 非空禁用
      isEmpty: true,
      showHandleUpdateLabel: false,
      // 总条数
      total: 0,
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      isEdit: false,
      showErrMsgs: false,
      // 类型数据字典
      samplesList: [],
      statusOptions: [],
      labelNameOptions: [],
      fileTypeOptions: undefined,
      // 是否只返回object_key
      isOnlyObjectKeyOptions: [
        { label: '是', value: false },
        { label: '否', value: true }
      ],
      // 是否显示标签列表
      showLabelList: false,
      // 标签列表
      labelList: [],
      // 关系表类型
      dowdate: undefined,
      imgUrls: [],
      // 查询参数
      queryParams: {
        pageIndex: 1,
        pageSize: 100,
        dataset_name: undefined
      },
      // 表单参数
      form: {
        dataset_name: '',
        business_scene: '',
        remark: '',
        version: ''
      },
      // 表单校验
      rules: {
        dataset_name: [
          { required: true, message: '数据集名称不能为空', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        business_scene: [
          { max: 100, message: '长度不能超过 100 个字符', trigger: 'blur' }
        ],
        remark: [
          { max: 200, message: '长度不能超过 200 个字符', trigger: 'blur' }
        ],
        version: [
          { max: 20, message: '长度不能超过 20 个字符', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getList()
    this.loading = false
    this.getDicts('sample_filetype').then(response => {
      this.fileTypeOptions = response.data
    })
  },
  methods: {
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '创建数据集'
    },
    /** 提交按钮 */
    submitForm() {
      console.log('提交表单，当前表单数据:', this.form)
      this.$refs['form'].validate(valid => {
        if (valid) {
          console.log('表单验证通过，开始创建数据集')
          this.handleCreateDataset()
        } else {
          console.log('表单验证失败')
        }
      })
    },
    /** 创建数据集 */
    handleCreateDataset() {
      this.loading = true
      const data_dict = {
        dataset_name: this.form.dataset_name
      }

      // 添加可选字段
      if (this.form.business_scene) {
        data_dict.business_scene = this.form.business_scene
      }
      if (this.form.remark) {
        data_dict.remark = this.form.remark
      }
      if (this.form.version) {
        data_dict.version = this.form.version
      }

      console.log('准备发送的数据:', data_dict)

      axios({
        method: 'post',
        headers: {
          'Cache-Control': 'no-cache',
          'Content-Type': 'application/json',
          ...getHeaders()
        },
        url: 'http://************:8001/data-processing/dataset',
        data: data_dict,
        timeout: 30000
      }).then(res => {
        if (res.data.code === 200) {
          this.$message.success('数据集创建成功')
          this.open = false
          this.getList()
        } else {
          this.$message.error(`创建失败：${res.data.msg || '未知错误'}`)
        }
      }).catch(error => {
        this.$message.error(`请求失败：${error.message}`)
      }).finally(() => {
        this.loading = false
      })
    },
    /** 取消按钮 */
    cancel() {
      this.open = false
      this.reset()
    },
    handleEvaluate(row) {
      this.$router.push({
        path: '/record/evaluation',
        query: { 
          dataset_id: row.dataset_id,
        }
      })
    },
    handleDetail(row) {
      this.$router.push({
        path: '/record/search',
        query: { dataset_id: row.dataset_id }
      })
    },
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.sample_id)
      this.single = selection.length !== 1
      this.multiple = this.getMultiple(selection.length)
      this.isEmpty = selection.length === 0
    },
    getMultiple(length) {
      return length <= 1
    },
    /** 查询参数列表 */
    getList() {
      this.loading = true
      var data_dict = {
        'page_index': this.queryParams.pageIndex,
        'page_size': this.queryParams.pageSize
      }
      if (this.queryParams.dataset_name) {
        data_dict.dataset_name = this.queryParams.dataset_name
      }
      console.log('param:', data_dict)
      return new Promise((resolve, reject) => {
        axios({
          method: 'post',
          headers: { 'Cache-Control': 'no-cache', 'Content-Type': 'application/json', ...getHeaders() },
          url: 'http://************:8001/data-processing' + '/dataset/search',
          data: data_dict,
          responseType: 'json',
          processData: false,
          contentType: false,
          timeout: 80000 // 设置超时时间为80秒
        }).then(res => {
          console.log('原始响应:', res)
          if (res.data.code !== 200 || res.data.data === undefined || res.data.data.datas === undefined) {
            return Promise.reject('服务请求失败!')
          }

          console.log('res.data.data:', res.data.data)
          if (res.data.data.total <= 0) {
            this.message('warn', res.data.msg, '但数据为空 total = 0')
            this.loading = false
            return Promise.resolve(1)
          }
          const result_data = res.data.data.datas
          console.log('result_data:', result_data)
          var result_datasets = []
          result_data.forEach(item => {
            console.log('item:', item)
            const dataset_id = item.dataset_id
            const dataset_name = item.dataset_name
            const status = item.status
            const business_scene = item.business_scene
            const remark = item.remark
            const version = item.version
            console.log('item:', item)
            result_datasets.push({ dataset_id, dataset_name, status, business_scene, remark, version })
            console.log('result_datasets:', result_datasets)
          })
          this.samplesList = result_datasets
          console.log('this.samplesList:', this.samplesList)
          this.total = res.data.data.total
          this.loading = false
          resolve(res.data.data.total)
        }).catch((error) => {
          console.log('错误响应:', error.response)
          this.message('error', '服务请求失败! ' + error, '。具体信息: ' + error.response)
          reject(error)
        })
      })
    },
    message(type, msg, dataMsg) {
      this.$message({
        showClose: true,
        message: msg + dataMsg,
        center: true,
        duration: 4000,
        type: type
      })
    },
    statusFormat(row) {
      switch (row.status) {
        case 0:
          return '未归档'
        case 1:
          return '已归档'
        default:
          return '未知状态'
      }
    },
    // 表单重置
    reset() {
      this.form = {
        dataset_name: '',
        business_scene: '',
        remark: '',
        version: ''
      }
      // 重置表单验证状态
      if (this.$refs.form) {
        this.$refs.form.resetFields()
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      // this.dowdate = this.queryParams.starttime
      this.queryParams.pageIndex = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.dataset_name = undefined
      this.queryParams.pageIndex = 1
      this.queryParams.pageSize = 10
      this.getList()
      this.samplesList = []
      this.loading = false
    }
  }
}

</script>

  <style scoped>
  .json-viewer-container {
      text-align: left;
  }

  .create-group {
    margin-left: auto;
  }

  .mr-8 {
    margin-right: 8px;
  }

  .mr-16 {
    margin-right: 16px;
  }

  .dialog-footer {
    text-align: right;
  }

  .el-form-item {
    margin-bottom: 20px;
  }

  .el-dialog__body {
    padding: 20px 20px 10px 20px;
  }
  </style>
