
<template>
  <div>
    <div>
      <el-button
        size="small"
        type="text"
        @click="evalScoring()"
      >打分 |</el-button>
      <el-button
        size="small"
        type="text"
        @click="tempStorageDetails()"
      >暂存详情 |</el-button>
      <!-- :disabled="!acceptFileType.includes(scope.row.file_type)" -->
      <el-button
        size="small"
        type="text"
        @click="openSubmitForm()"
      >提交</el-button>
    </div>

    <div>
      <el-dialog :visible.sync="evalScoringOpen" :title="'打分表单'" width="500px">
        <el-form ref="form" :model="scoreDetailData" :rules="rules" label-width="120px">
          <el-form-item label="item: " prop="item">
            <el-input v-model="scoreDetailData.item" placeholder="请输入打分项目" type="textarea" rows="2" />
          </el-form-item>
          <el-form-item label="score: " prop="score">
            <el-input v-model="scoreDetailData.score" placeholder="请输入分数" type="number" />
          </el-form-item>
          <el-form-item label="reason: " prop="reason">
            <el-input v-model="scoreDetailData.reason" placeholder="请输入打分原因" type="textarea" />
          </el-form-item>
          <el-form-item label="remark: " prop="remark">
            <el-input v-model="scoreDetailData.remark" placeholder="填写需要补充的信息" type="textarea" />
          </el-form-item>
        </el-form>
        <!-- <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="item: " prop="item">
            <el-input v-model="form.item" placeholder="请输入打分项目" type="textarea" rows="2" />
        </el-form-item>
        <el-form-item label="score: " prop="score">
            <el-input v-model="form.score" placeholder="请输入分数" type="textarea" />
        </el-form-item>
        <el-form-item label="reason: " prop="reason">
            <el-input v-model="form.reason" placeholder="请输入打分原因" type="textarea" />
        </el-form-item>
        <el-form-item label="remark: " prop="remark">
            <el-input v-model="form.remark" placeholder="填写需要补充的信息" type="textarea" />
        </el-form-item>
        </el-form> -->
        <div slot="footer" class="dialog-footer">
          <div class="cancel-button">
            <el-button @click="cancelEvalScoring">取 消</el-button>
          </div>
          <div class="save-submit-buttons">
            <el-button type="primary" @click="tempStorage(scoreDetailData)">暂 存</el-button>
            <!-- <el-button type="success" :loading="loading" @click="openSubmitForm">提 交</el-button> -->
          </div>
        </div>
      </el-dialog>

      <el-dialog :visible.sync="tempStorageOpen" :title="'暂存详情'" width="800px">
        <el-form ref="form" :rules="rules" label-width="120px">
          <el-table :data="tempStorageList" style="width: 100%">
            <el-table-column prop="item" label="Item" />
            <el-table-column prop="score" label="Score" />
            <el-table-column prop="reason" label="Reason" />
            <el-table-column prop="remark" label="Remark" />
          </el-table>
        </el-form>
        <!-- <div slot="footer" class="dialog-footer"> -->
        <div slot="footer">
          <el-button @click="cancelTempStorage">关 闭</el-button>
        </div>
      </el-dialog>

      <el-dialog :visible.sync="submitFormOpen" :title="'提交表单'" width="500px">
        <el-form ref="form" :model="subForm" :rules="rules" label-width="120px">
          <el-form-item label="总分: " prop="score">
            <el-input v-model="subForm.score" placeholder="请输入总分" type="number" />
          </el-form-item>
          <el-form-item label="remark: " prop="remark">
            <el-input v-model="subForm.remark" placeholder="填写需要补充的信息" type="textarea" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <div class="cancel-button">
            <el-button @click="cancelSubmitForm">取 消</el-button>
          </div>
          <div class="save-submit-buttons">
            <el-button type="success" :loading="loading" @click="submitForm">提 交</el-button>
          </div>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import axios from 'axios'
import { getHeaders } from '@/utils/get-header'
import { mapGetters } from 'vuex'

export default {
  name: 'EvaluationForm',
  props: {
    recordId: {
      type: Number,
      required: true
    },
    recordIndex: {
      type: Number,
      required: true
    },
    scoreDetailData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      loading: false,
      // 查询参数
      queryParams: {
        pageIndex: 1,
        pageSize: 10,
        dataset_id: undefined,
        reviewer: undefined
      },
      // 是否展示其他信息
      showOtherInfos: false,
      // 打分表单参数
      form: {
        item: '测试项1',
        score: 50,
        reason: '原因1',
        remark: '备注1'
      },
      tempStorageList: [],
      // 提交表单参数
      subForm: {
        score: 50,
        remark: '备注1'
      },
      // 表单校验
      rules: { docid: [{ required: true, message: 'id不能为空', trigger: 'blur' }],
        starttime: [{ required: true, message: '(:s)不能为空', trigger: 'blur' }],
        status: [{ required: true, message: ', 0:, 1:, 2:不能为空', trigger: 'blur' }]
      },
      // 打分表单
      evalScoringOpen: false,
      // 暂存详情
      tempStorageOpen: false,
      // 提交表单
      submitFormOpen: false
      // selectedRowData: {}
    }
  },
  computed: {
    ...mapGetters([
      'name'
    ])
  },
  created() {
    // this.getList()
    // this.getDicts('sample_filetype').then(response => {
    //   this.fileTypeOptions = response.data
    // })
  },
  methods: {
    /** 提交 */
    openSubmitForm() {
      this.cancelEvalScoring()
      this.submitFormOpen = true
    },
    cancelSubmitForm() {
      this.submitFormOpen = false
    },
    submitForm() {
      console.log('提交表单的score_detail信息: ', this.tempStorageList)
      console.log('提交表单的总分信息: ', this.subForm)
      this.cancelSubmitForm()
      // 在这里调用评测接口!!!!!!!!
      console.log('name：', this.name)
      console.log('recordId:', this.recordId)
      this.createRecord()
      // 清空暂存数据
      this.clearTempStorage()
    },
    createRecord() {
      for (const key in this.tempStorageList) {
        // this.tempStorageList[key].index = key
        // this.tempStorageList[key].index = parseInt(this.tempStorageList[key].index)
        this.tempStorageList[key].index = this.recordIndex
        this.tempStorageList[key].score = parseInt(this.tempStorageList[key].score)
      }
      console.log('提交表单的score_detail信息处理后: ', this.tempStorageList)
      var data_dict = {
        // 'record_id': this.recordId,
        'reviewer': this.name,
        'score': parseInt(this.subForm.score),
        'remark': this.subForm.remark,
        'score_detail': this.tempStorageList
      }
      console.log('param:', data_dict)
      return new Promise((resolve, reject) => {
        axios({
          method: 'post',
          headers: { 'Cache-Control': 'no-cache', 'Content-Type': 'application/json', ...getHeaders() },
          url: 'http://10.213.40.66:8001/data-processing' + '/records/' + this.recordId + '/evaluation',
          data: data_dict,
          responseType: 'json',
          processData: false,
          contentType: false,
          timeout: 80000 // 设置超时时间为80秒
        }).then(res => {
          if (res.data.code !== 200 || res.data.data === undefined || res.data.msg === undefined) {
            return Promise.reject('请求失败!')
          }
          console.log('请求处理结果：', res.data.msg)
          this.message('info', res.data.msg, '')
        }).catch((error) => {
          // this.$message({
          //   showClose: true,
          //   message: '请求处理失败! ' + error,
          //   center: true,
          //   duration: 4000,
          //   type: 'error'
          // })
          this.message(error, '请求处理失败! ', error)
          // return Promise.resolve(0)
          reject(error)
        })
      })
    },
    message(type, msg, dataMsg) {
      this.$message({
        showClose: true,
        message: msg + dataMsg,
        center: true,
        duration: 4000,
        type: type
      })
    },
    clearTempStorage() {
      this.tempStorageList = []
    },
    /** 暂存 */
    tempStorage(scoreDetailData) {
      console.log('[tempStorage] scoreDetailData:', scoreDetailData)
      // 使用深拷贝方法将this.form对象复制一份，然后添加到tempStorageList中
      this.tempStorageList.push(JSON.parse(JSON.stringify(scoreDetailData)))
      this.cancelEvalScoring()
    },
    tempStorageDetails() {
      this.tempStorageOpen = true
    },
    cancelTempStorage() {
      this.tempStorageOpen = false
    },
    cancelEvalScoring() {
      this.evalScoringOpen = false
      // 将值复位
    },
    /** 打分 */
    evalScoring(data) {
      // console.log('[evalScoring] data:', data)
      // console.log('[evalScoring] data.score_detail:', data.score_detail)
      // this.selectedRowData = data.score_detail
      // 使用JSON方法进行深拷贝
      // this.selectedRowData = JSON.parse(JSON.stringify(data))
      this.evalScoringOpen = true
    },
    handlePreview(imageUrl) {
      // 在此处打开模态框或者跳转到新页面展示原图
      window.open(imageUrl, '_blank')
    },
    // 取消评分细则按钮
    cancelScoreDetail() {
      this.viewScoreDetail = false
      // this.reset()
    },
    // 格式化
    format(imgsUrl) {
      const externalUrl = imgsUrl.replace(/-internal/g, '')
      const cleanUrl = externalUrl.replace(/\[|\]/g, '')
      return cleanUrl
    },
    closeViewer() {
      this.showViewer = false
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        sample_id: undefined,
        initial_name: '',
        labels: [],
        remark: ''
      }
      this.showHandleUpdateLabel = false
      this.resetForm('form')
    }
  }
}
</script>

<style>
.dialog-footer {
    display: flex;
    justify-content: space-between;
}

.cancel-button {
    align-self: flex-start;
}

.save-submit-buttons {
    align-self: flex-end;
}
</style>
