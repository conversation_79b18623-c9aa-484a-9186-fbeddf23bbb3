<template>
  <el-tabs v-model="activeName" style="width: 80%; min-width: 600px; margin: 1px 3%">
    <el-tab-pane label="样章列表" name="sample">
      <el-table
        v-loading="loadingSample"
        :data="sampleData"
        style="width: 100%;margin-bottom: 20px;"
        row-key="id"
        :tree-props="{children: 'urls', hasChildren: 'hasChildren'}"
      >
        <el-table-column prop="app_show" label="项目名称" sortable width="180" />
        <el-table-column prop="url" label="url" min-width="280" />
        <el-table-column fixed="right" align="right" header-align="center" label="操作" width="180">
          <template slot-scope="scope">
            <template v-if="scope.row.url !== ''">
              <el-button type="danger" size="mini" @click="handleDeleteSample(scope.row)">删除</el-button>
            </template>
            <template v-else>
              <el-popover placement="bottom" title="样章列表" width="400" trigger="click" style="margin-right: 5px">
                {{ scope.row.url_list }}
                <el-button slot="reference" size="mini">获取列表</el-button>
              </el-popover>
              <el-button type="primary" size="mini" @click="handleAdd(scope.row)">新增</el-button>
            </template>
          </template>
        </el-table-column>
      </el-table>
    </el-tab-pane>
    <el-tab-pane label="基线列表" name="testset">
      <el-table
        v-loading="loadingTestset"
        :data="testsetData"
        style="width: 100%;margin-bottom: 20px;"
        row-key="id"
        :tree-props="{children: 'vers', hasChildren: 'hasChildren'}"
      >
        <el-table-column prop="app_show" label="项目名称" sortable width="180" />
        <el-table-column prop="default_show" label="默认版本号" width="180" />
        <el-table-column prop="ver" label="版本号" width="180" />
        <el-table-column prop="url" label="文件地址" min-width="360" />
        <el-table-column fixed="right" label="操作" width="180">
          <template slot-scope="scope">
            <template v-if="scope.row.ver !== ''">
              <el-button v-if="scope.row.ver !== scope.row.default" type="success" size="mini" @click="handleClick(scope.row)">设为默认</el-button>
              <el-button v-else type="info" size="mini" disabled>默认版本</el-button>
              <el-button type="danger" size="mini" @click="handleDelete(scope.row)">删除</el-button>
            </template>
          </template>
        </el-table-column>
      </el-table>
    </el-tab-pane>
    <el-tab-pane label="临时集列表" name="tempset">
      <el-table
        v-loading="loadingTempset"
        :data="tempsetData"
        style="width: 100%;margin-bottom: 20px;"
        row-key="id"
        :tree-props="{children: 'groups', hasChildren: 'hasChildren'}"
      >
        <el-table-column prop="app_show" label="项目名称" sortable width="180" />
        <el-table-column prop="group" label="ID" min-width="280" />
        <el-table-column prop="mod_time" label="处理时间" min-width="180" />
        <el-table-column fixed="right" label="操作" width="120">
          <template slot-scope="scope">
            <template v-if="scope.row.group !== ''">
              <el-button type="primary" size="mini" @click="handleDetail(scope.row)">查看详情</el-button>
            </template>
          </template>
        </el-table-column>
      </el-table>
    </el-tab-pane>
  </el-tabs>
</template>

<script>
import SampleUploader from '@/views/autotest/SampleUploader'
import CryptoJS from 'crypto-js'
import { httpRequesSignKs } from '@/api/cv/request-ks3'
import { ks3_post } from '@/api/model/ks3'
import { testsetUrlMaker } from '@/api/autotest/common'

export default {
  name: 'TestSet',
  components: {
    // eslint-disable-next-line vue/no-unused-components
    SampleUploader
  },
  data() {
    return {
      apps: [],
      activeName: 'sample',
      testsetData: [],
      tempsetData: [],
      sampleData: [],
      loadingTestset: true,
      loadingTempset: true,
      loadingSample: true,
      sampleFile: '',
      loading: false
    }
  },
  mounted() {
    this.getTestsetData()
    this.getSampleData()
  },
  methods: {
    async getTestsetData() {
      const response = await this.$autotest_http.autotest_get(this.$autotest_api.configApi)
      if (response.status === 1) {
        this.testsetData = []
        this.apps = []
        for (let i = 0; i < response.config.length; i++) {
          const appData = { id: i + 1, ver: '', vers: [] }
          const config = response.config[i]
          this.apps.push(config['app'])
          appData['app_show'] = config['app']
          appData['default_show'] = config['default']
          for (let j = 0; j < config['vers'].length; j++) {
            const verData = { id: (i + 1) * 10 + j }
            const ver = config['vers'][j]
            verData['app'] = config['app']
            verData['default'] = config['default']
            verData['ver'] = ver['ver']
            verData['url'] = testsetUrlMaker({ set: 'testset', app: config['app'], version: ver['ver'] })
            appData.vers.push(verData)
          }
          this.testsetData.push(appData)
        }
        this.loadingTestset = false
        this.getTempsetData()
      } else {
        this.$message.error('加载基线失败')
      }
    },
    async getTempsetData() {
      const response = []
      for (const app of this.apps) {
        const res = await this.$autotest_http.autotest_get(this.$autotest_api.groupsApi, { appName: app })
        response.push({ 'app': app, 'groups': res.groups, 'mod_times': res['modTimes'] })
      }
      this.tempsetData = []
      for (let i = 0; i < response.length; i++) {
        const appData = { id: i + 1, group: '', groups: [] }
        appData['app_show'] = response[i]['app']
        for (let j = 0; j < response[i]['groups'].length; j++) {
          const groupData = { id: (i + 1) * 10 + j }
          groupData['app'] = response[i]['app']
          groupData['group'] = response[i]['groups'][j]
          groupData['mod_time'] = this.ts2date(parseInt(response[i]['mod_times'][j]) * 1000)
          appData.groups.push(groupData)
        }
        this.tempsetData.push(appData)
      }
      this.loadingTempset = false
    },
    async getSampleData() {
      const response = await this.$autotest_http.autotest_get(this.$autotest_api.samplesApi)
      if (response.status === 1) {
        this.sampleData = []
        for (let i = 0; i < Object.keys(response.samples).length; i++) {
          const appData = { id: i + 1, url: '', urls: [] }
          const app = Object.keys(response.samples)[i]
          const urls = response.samples[app]
          appData['app_show'] = app
          appData['url_list'] = urls
          for (let j = 0; j < urls.length; j++) {
            const urlData = { id: (i + 1) * 10 + j }
            urlData['app'] = app
            urlData['url'] = urls[j]
            appData.urls.push(urlData)
          }
          this.sampleData.push(appData)
        }
        this.loadingSample = false
      } else {
        this.$message.error('加载样章失败')
      }
    },
    async deleteTestset(appName, baselineVer) {
      const response = await this.$autotest_http.autotest_delete(this.$autotest_api.testsetApi,
        { appName: appName, baselineVer: baselineVer })
      if (response.status === 1) {
        this.msgSuccess(response.msg)
        this.loadingTestset = true
        // location.reload()
        await this.getTestsetData()
      } else {
        this.msgError(response.msg)
      }
    },
    async setDefault(appName, baselineVer) {
      const response = await this.$autotest_http.autotest_post(this.$autotest_api.configApi,
        { appName: appName, baselineVer: baselineVer })
      if (response.status === 1) {
        this.msgSuccess(response.msg)
        this.loadingTestset = true
        await this.getTestsetData()
      } else {
        this.msgError(response.msg)
      }
    },
    async deleteSample(appName, sampleUrls) {
      const response = await this.$autotest_http.autotest_delete(this.$autotest_api.samplesApi,
        { appName: appName, sampleUrls: [sampleUrls] })
      if (response.status === 1) {
        this.msgSuccess(response.msg)
        this.loadingSample = true
        // location.reload()
        await this.getSampleData()
      } else {
        this.msgError(response.msg)
      }
    },
    async addSample2KS3(appName, filenamebymd5) {
      // 服务端获取签名
      const res = await httpRequesSignKs(filenamebymd5, 'private', appName)
      if (res.status === 200) {
        const formData = new FormData()
        formData.append('acl', res.data.data['acl'])
        formData.append('key', res.data.data['key'])
        formData.append('Signature', res.data.data['signature'])
        formData.append('KSSAccessKeyId', res.data.data['kSSAccessKeyId'])
        // eslint-disable-next-line no-undef
        formData.append('Policy', res.data.data['policy'])
        formData.append('file', this.sampleFile)
        const url = 'https://' + res.data.data['url']
        const resKs3 = await ks3_post(url, formData)
        return { status: resKs3.status, url: url + '/' + res.data.data['key'] }
      } else {
        return { status: res.status }
      }
    },
    async addSample(appName, sampleUrls) {
      const response = await this.$autotest_http.autotest_post(this.$autotest_api.samplesApi,
        { appName: appName, sampleUrls: [sampleUrls] })
      if (response.status === 1) {
        this.msgSuccess(response.msg)
        this.loadingSample = true
        // location.reload()
        await this.getSampleData()
      } else {
        this.$message.warning(response.msg)
      }
    },
    handleClick(row) {
      this.setDefault(row.app, row.ver)
    },
    handleDelete(row) {
      this.$confirm('是否确认删除项目"<strong>' + row.app + '</strong>"版本"<strong>' + row.ver +
        '</strong>"的数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        dangerouslyUseHTMLString: true,
        type: 'warning'
      }).then(() => {
        this.deleteTestset(row.app, row.ver)
      })
    },
    handleDetail(row) {
      this.$router.replace({ name: 'TempsetDetail', params: row })
    },
    handleDeleteSample(row) {
      this.$confirm(
        '',
        '警告',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          dangerouslyUseHTMLString: true,
          type: 'warning'
        }
      ).then(() => {
        this.deleteSample(row.app, row.url)
      })
    },
    handleAdd(row) {
      const h = this.$createElement
      this.$msgbox({
        message: h('SampleUploader', { on: { getSampleFile: this.handleSampleFile }}),
        title: row.app_show + '新增样章',
        showCancelButton: true,
        confirmButtonText: '上传',
        cancelButtonText: '取消'
      }).then(() => {
        const reader = new FileReader()
        reader.readAsBinaryString(this.sampleFile)
        reader.md5 = ''
        reader.filename = this.sampleFile.name
        reader.onload = function() {
          this.md5 = CryptoJS.MD5(reader.result).toString() + '.' + this.filename.split('.').pop()
        }
        const _this = this
        reader.onloadend = async function() {
          const resKs3 = await _this.addSample2KS3(row.app_show, this.md5)
          if (resKs3.status === 200) {
            _this.$message.success('样章上传ks3成功')
            await _this.addSample(row.app_show, resKs3.url)
          } else {
            _this.$message.warning('样章上传ks3失败, 请重试')
          }
        }
      })
    },
    handleSampleFile(data) {
      this.sampleFile = data
    },
    ts2date(ts) {
      const date = new Date(ts)
      let MM = date.getMonth() + 1
      MM = MM < 10 ? ('0' + MM) : MM
      let d = date.getDate()
      d = d < 10 ? ('0' + d) : d
      let h = date.getHours()
      h = h < 10 ? ('0' + h) : h
      let m = date.getMinutes()
      m = m < 10 ? ('0' + m) : m
      let s = date.getSeconds()
      s = s < 10 ? ('0' + s) : s
      return date.getFullYear() + '-' + MM + '-' + d + ' ' + h + ':' + m + ':' + s
    }
  }
}
</script>

<style scoped>

</style>
